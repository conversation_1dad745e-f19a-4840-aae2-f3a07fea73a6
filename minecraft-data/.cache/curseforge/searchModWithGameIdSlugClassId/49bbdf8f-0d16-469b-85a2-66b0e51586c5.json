{"allowModDistribution": true, "screenshots": [{"description": "Custom Color Screen.png", "id": 920173, "title": "Custom Color Screen.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/173/custom-color-screen.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/173/310/172/custom-color-screen.png"}, {"description": "Minimap Support.png", "id": 920179, "title": "Minimap Support.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/179/minimap-support.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/179/310/172/minimap-support.png"}, {"description": "Display Type.png", "id": 920174, "title": "Display Type.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/174/display-type.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/174/310/172/display-type.png"}, {"description": "Spring.png", "id": 920175, "title": "Spring.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/175/spring.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/175/310/172/spring.png"}, {"description": "Summer.png", "id": 920176, "title": "Summer.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/176/summer.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/176/310/172/summer.png"}, {"description": "Autumn.png", "id": 920177, "title": "Autumn.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/177/autumn.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/177/310/172/autumn.png"}, {"description": "Winter.png", "id": 920178, "title": "Winter.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/178/winter.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/178/310/172/winter.png"}, {"description": "Season.png", "id": 920169, "title": "Season.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/169/season.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/169/310/172/season.png"}, {"description": "Sub-Season.png", "id": 920170, "title": "Sub-Season.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/170/sub-season.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/170/310/172/sub-season.png"}, {"description": "Day Count.png", "id": 920171, "title": "Day Count.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/171/day-count.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/171/310/172/day-count.png"}, {"description": "Day Count + Total.png", "id": 920172, "title": "Day Count + Total.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/172/day-count-total.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/172/310/172/day-count-total.png"}, {"description": "Optional Curios Support.png", "id": 920180, "title": "Optional Curios Support.png", "modId": 690971, "url": "https://media.forgecdn.net/attachments/920/180/optional-curios-support.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/920/180/310/172/optional-curios-support.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "seasonhud-neoforge-1.21.8-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036354}, {"filename": "seasonhud-neoforge-1.21.8-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036354}, {"filename": "seasonhud-neoforge-1.21.8-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036354}, {"filename": "seasonhud-neoforge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036347}, {"filename": "seasonhud-neoforge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036347}, {"filename": "seasonhud-neoforge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036347}, {"filename": "seasonhud-neoforge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036347}, {"filename": "seasonhud-forge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7036346}, {"filename": "seasonhud-forge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7036346}, {"filename": "seasonhud-forge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7036346}, {"filename": "seasonhud-forge-1.21.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7036346}, {"filename": "seasonhud-neoforge-1.21.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036342}, {"filename": "seasonhud-neoforge-1.21.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7036342}, {"filename": "seasonhud-forge-1.21.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7036341}, {"filename": "seasonhud-forge-1.21.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7036341}, {"filename": "seasonhud-forge-1.20.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7036323}, {"filename": "seasonhud-forge-1.20.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7036323}, {"filename": "seasonhud-forge-1.20.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7036323}, {"filename": "seasonhud-forge-1.20.1-1.13.11.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7036323}, {"filename": "seasonhud-forge-1.19.2-1.13.11.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7036311}, {"filename": "seasonhud-forge-1.19.2-1.13.11.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7036311}, {"filename": "seasonhud-forge-1.19.2-1.13.11.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7036311}, {"filename": "seasonhud-forge-1.18.2-1.13.11.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 7036302}, {"filename": "seasonhud-forge-1.16.5-1.13.11.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 7036287}, {"filename": "seasonhud-neoforge-1.20.6-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5576115}, {"filename": "seasonhud-neoforge-1.20.6-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5576115}, {"filename": "seasonhud-forge-1.20.6-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5576114}, {"filename": "seasonhud-forge-1.20.6-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5576114}, {"filename": "seasonhud-neoforge-1.20.4-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5576107}, {"filename": "seasonhud-forge-1.20.4-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5576106}, {"filename": "seasonhud-forge-1.20.2-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5576097}, {"filename": "seasonhud-forge-1.20.2-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5576097}, {"filename": "seasonhud-forge-1.20.2-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5576097}, {"filename": "seasonhud-forge-1.20.2-1.9.8.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5576097}, {"filename": "seasonhud-1.17.1-1.2.5.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 4357038}], "dateCreated": "2022-10-23T12:51:50.89Z", "logo": {"description": "", "id": 1076109, "title": "638613523741181503.png", "modId": 690971, "url": "https://media.forgecdn.net/avatars/1076/109/638613523741181503.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1076/109/256/256/638613523741181503.png"}, "links": {"sourceUrl": "https://github.com/IanMods/SeasonHUD", "issuesUrl": "https://github.com/IanMods/SeasonHUD/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/seasonhud", "wikiUrl": ""}, "dateReleased": "2025-09-26T19:36:59.433Z", "id": 690971, "categories": [{"gameId": 432, "classId": 6, "name": "Map and Information", "dateModified": "2014-05-08T17:42:23.74Z", "parentCategoryId": 6, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}], "slug": "<PERSON>hud", "gameId": 432, "summary": "Display the current season on the HUD or under the minimap", "latestFiles": [{"gameId": 432, "fileName": "seasonhud-forge-1.21.5-1.13.11.jar", "gameVersions": ["1.21.5", "1.21.4", "Forge", "1.21.3", "1.21.2"], "displayName": "seasonhud-forge-1.21.5-1.13.11.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000021.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0000000001.0000000021.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000003", "gameVersion": "1.21.3", "gameVersionReleaseDate": "2024-10-23T13:16:45.71Z", "gameVersionName": "1.21.3", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0000000001.0000000021.0000000002", "gameVersion": "1.21.2", "gameVersionReleaseDate": "2024-10-22T00:00:00Z", "gameVersionName": "1.21.2", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/7036/346/seasonhud-forge-1.21.5-1.13.11.jar", "fileDate": "2025-09-26T19:34:34.127Z", "exposeAsAlternative": null, "modId": 690971, "modules": [{"name": "META-INF", "fingerprint": 652351589}, {"name": "architectury_inject_seasonhud1215_common_776e9284697e48d28d6904bd795c67c6_4aa3b74f42292411e572b97dbb6db0c7917d767b62d66fa110bb8c0c5acc4429seasonhudcommon121511311devjar", "fingerprint": 3793242765}, {"name": "assets", "fingerprint": 2702633734}, {"name": "club", "fingerprint": 1094070505}, {"name": "data", "fingerprint": 2518275652}, {"name": "pack.mcmeta", "fingerprint": 2463850448}, {"name": "seasonhud-common-1.21.5-common-refmap.json", "fingerprint": 3030358677}, {"name": "seasonhud.forge.mixins.json", "fingerprint": 1516049318}, {"name": "seasonhud.mixins.json", "fingerprint": 2043471553}, {"name": "seasonhud.png", "fingerprint": 1293895720}], "dependencies": [], "fileFingerprint": 3255780761, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "5e17abe78337259d67c4fe10a921160313b32924", "algo": 1}, {"value": "9fdf1052c53841b8cf8fd9402e5230d5", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7036346, "fileLength": 293766, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "seasonhud-neoforge-1.21.8-1.13.11.jar", "gameVersions": ["1.21.8", "NeoForge", "1.21.7", "1.21.6"], "displayName": "seasonhud-neoforge-1.21.8-1.13.11.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000021.0000000008", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0000000001.0000000021.0000000006", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/7036/354/seasonhud-neoforge-1.21.8-1.13.11.jar", "fileDate": "2025-09-26T19:36:59.433Z", "exposeAsAlternative": null, "modId": 690971, "modules": [{"name": "META-INF", "fingerprint": 2027714930}, {"name": "architectury_inject_seasonhud1218_common_6ee0017b247947ed93567dd07d322fa6_01bbd376b79d80ea57bddcf79cd264c1abacef36d9151207aa13d2d4520138cfseasonhudcommon121811311devjar", "fingerprint": 4063516334}, {"name": "assets", "fingerprint": 2702633734}, {"name": "club", "fingerprint": 2507165656}, {"name": "data", "fingerprint": 2518275652}, {"name": "pack.mcmeta", "fingerprint": 3575522079}, {"name": "seasonhud-common-1.21.8-common-refmap.json", "fingerprint": 221030776}, {"name": "seasonhud.mixins.json", "fingerprint": 2348274473}, {"name": "seasonhud.png", "fingerprint": 3009749526}], "dependencies": [], "fileFingerprint": 2904596060, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "f66b8af4f135d6d082eb81e6557eb62646464da5", "algo": 1}, {"value": "c45e8716879fcf5ebc9be6633c1f6a06", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7036354, "fileLength": 310136, "downloadCount": 31, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-09-26T19:43:56.84Z", "gamePopularityRank": 1918, "thumbsUpCount": 0, "name": "SeasonHUD", "mainFileId": 7036354, "primaryCategoryId": 5191, "downloadCount": 4787275, "status": 4, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "id": 6845464, "url": "https://www.curseforge.com/members/iana<PERSON>on"}], "available": false, "featured": false}