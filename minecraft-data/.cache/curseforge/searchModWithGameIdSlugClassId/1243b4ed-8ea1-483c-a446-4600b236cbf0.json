{"allowModDistribution": true, "screenshots": [{"description": "Just check out all those colors.", "id": 387160, "title": "Supports modded items and rarities!", "modId": 513769, "url": "https://media.forgecdn.net/attachments/387/160/2021-08-10_16.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/387/160/310/172/2021-08-10_16.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "ItemBorders-1.21.4-forge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6168722}, {"filename": "ItemBorders-1.21.4-neoforge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6168720}, {"filename": "ItemBorders-1.21.3-forge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5991628}, {"filename": "ItemBorders-1.21.3-neoforge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5991626}, {"filename": "ItemBorders-1.20.1-forge-1.2.2.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5903326}, {"filename": "ItemBorders-1.21-neoforge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5591010}, {"filename": "ItemBorders-1.21-neoforge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5591010}, {"filename": "ItemBorders-1.21-forge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5591005}, {"filename": "ItemBorders-1.21-forge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5591005}, {"filename": "ItemBorders-1.20.6-forge-1.2.3.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5365032}, {"filename": "ItemBorders-1.20.1-forge-1.2.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4985587}, {"filename": "ItemBorders-1.20.2-forge-1.2.0.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4918156}, {"filename": "ItemBorders-1.20.2-forge-1.2.0.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4918156}, {"filename": "ItemBorders-1.20.1-forge-1.2.0.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4655513}, {"filename": "ItemBorders-1.12.2-forge-1.2.0.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 4590380}, {"filename": "ItemBorders-1.19.4-forge-1.2.0.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4499850}, {"filename": "ItemBorders-1.19.3-forge-1.2.0.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4277021}, {"filename": "ItemBorders-1.19.1-1.2.0.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3912057}, {"filename": "ItemBorders-1.19.1-1.2.0.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3912057}, {"filename": "ItemBorders-1.19.1-1.2.0.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3912057}, {"filename": "ItemBorders-1.16.5-1.1.6.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3584996}, {"filename": "ItemBorders-1.18.1-1.1.5.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3583942}, {"filename": "ItemBorders-1.18.1-1.1.5.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3583942}, {"filename": "ItemBorders-1.17.1-1.1.5.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 3583914}, {"filename": "ItemBorders-1.18-1.1.3.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3555172}, {"filename": "ItemBorders-1.15.2-1.1.0.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 3434962}], "dateCreated": "2021-08-09T09:38:51.893Z", "logo": {"description": "", "id": 417257, "title": "637640874948180814.png", "modId": 513769, "url": "https://media.forgecdn.net/avatars/417/257/637640874948180814.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/417/257/256/256/637640874948180814.png"}, "links": {"sourceUrl": "https://github.com/AHilyard/ItemBorders", "issuesUrl": "https://discord.gg/UCKjnamDaW", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/item-borders", "wikiUrl": ""}, "dateReleased": "2025-02-07T20:25:59.827Z", "id": 513769, "categories": [{"gameId": 432, "classId": 6, "name": "Armor, Tools, and Weapons", "dateModified": "2014-05-08T17:44:39.057Z", "parentCategoryId": 6, "id": 434, "iconUrl": "https://media.forgecdn.net/avatars/6/47/635351498790409758.png", "slug": "armor-weapons-tools", "url": "https://www.curseforge.com/minecraft/mc-mods/armor-weapons-tools", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Adventure and RPG", "dateModified": "2014-05-08T17:42:09.54Z", "parentCategoryId": 6, "id": 422, "iconUrl": "https://media.forgecdn.net/avatars/6/37/635351497295252123.png", "slug": "adventure-rpg", "url": "https://www.curseforge.com/minecraft/mc-mods/adventure-rpg", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "parentCategoryId": 6, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}], "slug": "item-borders", "gameId": 432, "summary": "Add colored borders to inventory slots to make your rare items stand out!", "latestFiles": [{"gameId": 432, "fileName": "ItemBorders-1.21.4-neoforge-1.2.5.jar", "gameVersions": ["Client", "1.21.4", "NeoForge"], "displayName": "ItemBorders-1.21.4-neoforge-1.2.5.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0000000001.0000000021.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6168/720/ItemBorders-1.21.4-neoforge-1.2.5.jar", "fileDate": "2025-02-07T20:25:43.737Z", "exposeAsAlternative": null, "modId": 513769, "modules": [{"name": "META-INF", "fingerprint": 2044432135}, {"name": "ItemBorders-1.21.4-common-common-refmap.json", "fingerprint": 2766308302}, {"name": "architectury_inject_ItemBorders_common_78d6b92cbfc646ddb85f3ae479209f42_77841dded791ccf10b1d4c631394e49061f58810f0ef3afd31609b8a34de7f82ItemBorders1214common125devjar", "fingerprint": 1227082933}, {"name": "com", "fingerprint": 860179542}, {"name": "icon.png", "fingerprint": 2466089056}, {"name": "itemborders.mixins.json", "fingerprint": 4273400311}, {"name": "itemborders.neoforge.mixins.json", "fingerprint": 2791869023}, {"name": "pack.mcmeta", "fingerprint": 390150631}], "dependencies": [], "fileFingerprint": 805964025, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "77c4a85b2f7e86c618454940629b58aae243899a", "algo": 1}, {"value": "1aacd9006c8795db563d7e2d286739cc", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6168720, "fileLength": 23525, "downloadCount": 1940, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ItemBorders-1.21.4-forge-1.2.5.jar", "gameVersions": ["Client", "1.21.4", "Forge"], "displayName": "ItemBorders-1.21.4-forge-1.2.5.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0000000001.0000000021.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6168/722/ItemBorders-1.21.4-forge-1.2.5.jar", "fileDate": "2025-02-07T20:25:59.827Z", "exposeAsAlternative": null, "modId": 513769, "modules": [{"name": "META-INF", "fingerprint": 1741006739}, {"name": "ItemBorders-1.21.4-common-common-refmap.json", "fingerprint": 2766308302}, {"name": "ItemBorders-1.21.4-forge-forge-refmap.json", "fingerprint": 1493072104}, {"name": "architectury_inject_ItemBorders_common_78d6b92cbfc646ddb85f3ae479209f42_77841dded791ccf10b1d4c631394e49061f58810f0ef3afd31609b8a34de7f82ItemBorders1214common125devjar", "fingerprint": 1085471404}, {"name": "com", "fingerprint": 3149670318}, {"name": "icon.png", "fingerprint": 2466089056}, {"name": "itemborders.forge.mixins.json", "fingerprint": 2517245219}, {"name": "itemborders.mixins.json", "fingerprint": 4273400311}, {"name": "pack.mcmeta", "fingerprint": 2174239361}], "dependencies": [], "fileFingerprint": 4256554352, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "5e6c865662ab5d901da82d7dd1bfcac6b5f093ac", "algo": 1}, {"value": "d7493b695e32882b4fd07c184408a834", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6168722, "fileLength": 25107, "downloadCount": 13642, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-02-07T20:30:15.967Z", "gamePopularityRank": 943, "thumbsUpCount": 0, "name": "Item Borders [Neo/Forge]", "mainFileId": 6168722, "primaryCategoryId": 425, "downloadCount": 11512734, "status": 4, "authors": [{"name": "Grend_G", "id": 100663802, "url": "https://www.curseforge.com/members/grend_g"}], "available": false, "featured": false}