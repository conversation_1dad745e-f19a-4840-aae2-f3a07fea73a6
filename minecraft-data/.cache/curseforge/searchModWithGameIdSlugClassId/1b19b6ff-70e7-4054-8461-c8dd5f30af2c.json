{"allowModDistribution": true, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "ftbbackups2-neoforge-1.21.5-1.0.31.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6481737}, {"filename": "ftbbackups2-fabric-1.21.5-1.0.31.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6481736}, {"filename": "ftbbackups2-neoforge-1.21.4-1.0.29.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6133888}, {"filename": "ftbbackups2-fabric-1.21.4-1.0.29.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6133887}, {"filename": "ftbbackups2-neoforge-1.20.2-1.0.29.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5859645}, {"filename": "ftbbackups2-forge-1.20.2-1.0.29.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5859644}, {"filename": "ftbbackups2-fabric-1.20.2-1.0.29.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5859643}, {"filename": "ftbbackups2-neoforge-1.20.4-1.0.29.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5859622}, {"filename": "ftbbackups2-forge-1.20.4-1.0.29.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5859621}, {"filename": "ftbbackups2-fabric-1.20.4-1.0.29.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5859620}, {"filename": "ftbbackups2-fabric-1.21-1.0.29.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5859580}, {"filename": "ftbbackups2-neoforge-1.21-1.0.29.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5859579}, {"filename": "ftbbackups2-neoforge-1.21.3-1.0.29.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5859480}, {"filename": "ftbbackups2-fabric-1.21.3-1.0.29.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5859479}, {"filename": "ftbbackups2-neoforge-1.21-1.0.28.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5501351}, {"filename": "ftbbackups2-fabric-1.21-1.0.28.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5501350}, {"filename": "ftbbackups2-forge-1.20-1.0.23.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4834403}, {"filename": "ftbbackups2-forge-1.20-1.0.23.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4834403}, {"filename": "ftbbackups2-forge-1.20-1.0.23.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4834403}, {"filename": "ftbbackups2-forge-1.20-1.0.23.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4834403}, {"filename": "ftbbackups2-fabric-1.20-1.0.23.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4834401}, {"filename": "ftbbackups2-fabric-1.20-1.0.23.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4834401}, {"filename": "ftbbackups2-forge-1.19.2-1.0.23.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4834398}, {"filename": "ftbbackups2-forge-1.19.2-1.0.23.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4834398}, {"filename": "ftbbackups2-fabric-1.19.2-1.0.23.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4834395}, {"filename": "ftbbackups2-fabric-1.19.2-1.0.23.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4834395}, {"filename": "ftbbackups2-forge-1.18.2-1.0.23.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 4834394}, {"filename": "ftbbackups2-fabric-1.18.2-1.0.23.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 4834390}, {"filename": "ftbbackups2-forge-1.18.2-1.0.13.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": null, "fileId": 3884571}, {"filename": "ftbbackups2-forge-1.19-1.0.13.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3884551}, {"filename": "ftbbackups2-forge-1.19-1.0.13.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3884551}, {"filename": "ftbbackups2-fabric-1.19-1.0.13.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3884550}, {"filename": "ftbbackups2-fabric-1.19-1.0.13.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3884550}, {"filename": "ftbbackups2-forge-1.19-1.0.12.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3832193}, {"filename": "ftbbackups2-fabric-1.19-1.0.12.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3832191}], "dateCreated": "2022-05-12T09:05:29.53Z", "logo": {"description": "", "id": 972585, "title": "638474887871512665.png", "modId": 622737, "url": "https://media.forgecdn.net/avatars/972/585/638474887871512665.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/972/585/256/256/638474887871512665.png"}, "links": {"sourceUrl": "https://github.com/CreeperHost/FTB-Backups-2", "issuesUrl": "https://github.com/CreeperHost/FTB-Backups-2/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/ftb-backups-2", "wikiUrl": ""}, "dateReleased": "2025-04-30T22:48:17.877Z", "id": 622737, "categories": [{"gameId": 432, "classId": 6, "name": "Map and Information", "dateModified": "2014-05-08T17:42:23.74Z", "parentCategoryId": 6, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Server Utility", "dateModified": "2014-05-08T17:44:55.057Z", "parentCategoryId": 6, "id": 435, "iconUrl": "https://media.forgecdn.net/avatars/6/48/635351498950580836.png", "slug": "server-utility", "url": "https://www.curseforge.com/minecraft/mc-mods/server-utility", "displayIndex": 0, "class": false}], "slug": "ftb-backups-2", "gameId": 432, "summary": "Updated verson of FTB Backups.", "latestFiles": [{"gameId": 432, "fileName": "ftbbackups2-fabric-1.19-1.0.12.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.19"], "displayName": "1.0.12 [<PERSON><PERSON><PERSON>]", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-06-07T15:38:07.377Z", "gameVersionName": "1.19", "gameVersionTypeId": 73407}], "downloadUrl": "https://edge.forgecdn.net/files/3832/191/ftbbackups2-fabric-1.19-1.0.12.jar", "fileDate": "2022-06-14T16:42:28.263Z", "exposeAsAlternative": null, "modId": 622737, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "com", "fingerprint": 182394198}, {"name": "assets", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "block-colors-water-1_17.json", "fingerprint": **********}, {"name": "block-colors-foliage-1_16.json", "fingerprint": 57908228}, {"name": "block-colors-foliage-1_13.json", "fingerprint": **********}, {"name": "block-colors-default-1_13.json", "fingerprint": **********}, {"name": "block-colors-foliage-1_15.json", "fingerprint": **********}, {"name": "block-colors-default-1_15.json", "fingerprint": 292087905}, {"name": "block-colors-x-ray-1_15.json", "fingerprint": **********}, {"name": "block-colors-water-1_14.json", "fingerprint": **********}, {"name": "biome-colors.json", "fingerprint": 993934737}, {"name": "block-colors-water-1_16.json", "fingerprint": **********}, {"name": "block-colors-x-ray-1_13.json", "fingerprint": **********}, {"name": "block-colors-caves-1_16.json", "fingerprint": **********}, {"name": "block-colors-x-ray-1_18.json", "fingerprint": 304154855}, {"name": "block-colors-default-1_18.json", "fingerprint": 2096871510}, {"name": "block-colors-caves-1_17.json", "fingerprint": 4099938819}, {"name": "block-colors-foliage-1_18.json", "fingerprint": 315087539}, {"name": "block-states-1_13.json", "fingerprint": 2314797772}, {"name": "block-colors-default-1_14.json", "fingerprint": 2015178715}, {"name": "block-colors-foliage-1_17.json", "fingerprint": 3391150073}, {"name": "heightmap.json", "fingerprint": 3530235401}, {"name": "block-colors-water-1_15.json", "fingerprint": 715620426}, {"name": "block-colors-rails-1_16.json", "fingerprint": 1359809855}, {"name": "block-colors-caves-1_14.json", "fingerprint": 3072923262}, {"name": "block-colors-x-ray-1_17.json", "fingerprint": 304154855}, {"name": "block-states-1_15.json", "fingerprint": 1776260775}, {"name": "block-colors-x-ray-1_14.json", "fingerprint": 95805284}, {"name": "block-colors-rails-1_17.json", "fingerprint": 1290519433}, {"name": "block-colors-rails-1_15.json", "fingerprint": 507993599}, {"name": "block-colors-default-1_17.json", "fingerprint": 323870691}, {"name": "block-colors-water-1_18.json", "fingerprint": 2897924713}, {"name": "block-colors-rails-1_14.json", "fingerprint": 2991216048}, {"name": "block-colors-water-1_13.json", "fingerprint": 1403550861}, {"name": "block-colors-rails-1_18.json", "fingerprint": 1290519433}, {"name": "block-states-1_17.json", "fingerprint": 708389835}, {"name": "block-states-1_14.json", "fingerprint": 3019307305}, {"name": "block-colors-x-ray-1_16.json", "fingerprint": 3718965679}, {"name": "block-colors-foliage-1_14.json", "fingerprint": 2762436567}, {"name": "block-states-1_18.json", "fingerprint": 708389835}, {"name": "block-colors-caves-1_15.json", "fingerprint": 3589391907}, {"name": "block-colors-caves-1_18.json", "fingerprint": 1160482320}, {"name": "block-states-1_16.json", "fingerprint": 3123485341}, {"name": "block-colors-caves-1_13.json", "fingerprint": 1062105531}, {"name": "block-colors-default-1_16.json", "fingerprint": 3699929219}, {"name": "block-colors-rails-1_13.json", "fingerprint": 158067849}, {"name": "fabric.mod.json", "fingerprint": 1561558409}, {"name": "net", "fingerprint": 3617257549}, {"name": "architectury_inject_FTBBackups2_common_3392e09cff9349deafffcaa53850f1ad_b1c874ff9922f6e0203ba401b16a73ca7882a18c1a7c070bd624366cbde88761ftbbackups21012devjar", "fingerprint": 2137305933}], "dependencies": [{"relationType": 3, "modId": 576589}, {"relationType": 3, "modId": 419699}], "fileFingerprint": 2401988626, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "d0b238a25d5b86c951e293839353af6bf7a2629e", "algo": 1}, {"value": "a4420f0c538d81313e429f5f7ec60ab6", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3832191, "fileLength": 3727616, "downloadCount": 4521, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ftbbackups2-forge-1.19-1.0.12.jar", "gameVersions": ["Forge", "1.19"], "displayName": "1.0.12 [Forge]", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-06-07T15:38:07.377Z", "gameVersionName": "1.19", "gameVersionTypeId": 73407}], "downloadUrl": "https://edge.forgecdn.net/files/3832/193/ftbbackups2-forge-1.19-1.0.12.jar", "fileDate": "2022-06-14T16:42:51.780Z", "exposeAsAlternative": null, "modId": 622737, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "com", "fingerprint": 182394198}, {"name": "assets", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "block-colors-water-1_17.json", "fingerprint": **********}, {"name": "block-colors-foliage-1_16.json", "fingerprint": 57908228}, {"name": "block-colors-foliage-1_13.json", "fingerprint": **********}, {"name": "block-colors-default-1_13.json", "fingerprint": **********}, {"name": "block-colors-foliage-1_15.json", "fingerprint": **********}, {"name": "block-colors-default-1_15.json", "fingerprint": 292087905}, {"name": "block-colors-x-ray-1_15.json", "fingerprint": **********}, {"name": "block-colors-water-1_14.json", "fingerprint": **********}, {"name": "biome-colors.json", "fingerprint": 993934737}, {"name": "block-colors-water-1_16.json", "fingerprint": **********}, {"name": "block-colors-x-ray-1_13.json", "fingerprint": **********}, {"name": "block-colors-caves-1_16.json", "fingerprint": **********}, {"name": "block-colors-x-ray-1_18.json", "fingerprint": 304154855}, {"name": "block-colors-default-1_18.json", "fingerprint": 2096871510}, {"name": "block-colors-caves-1_17.json", "fingerprint": 4099938819}, {"name": "block-colors-foliage-1_18.json", "fingerprint": 315087539}, {"name": "block-states-1_13.json", "fingerprint": 2314797772}, {"name": "block-colors-default-1_14.json", "fingerprint": 2015178715}, {"name": "block-colors-foliage-1_17.json", "fingerprint": 3391150073}, {"name": "heightmap.json", "fingerprint": 3530235401}, {"name": "block-colors-water-1_15.json", "fingerprint": 715620426}, {"name": "block-colors-rails-1_16.json", "fingerprint": 1359809855}, {"name": "block-colors-caves-1_14.json", "fingerprint": 3072923262}, {"name": "block-colors-x-ray-1_17.json", "fingerprint": 304154855}, {"name": "block-states-1_15.json", "fingerprint": 1776260775}, {"name": "block-colors-x-ray-1_14.json", "fingerprint": 95805284}, {"name": "block-colors-rails-1_17.json", "fingerprint": 1290519433}, {"name": "block-colors-rails-1_15.json", "fingerprint": 507993599}, {"name": "block-colors-default-1_17.json", "fingerprint": 323870691}, {"name": "block-colors-water-1_18.json", "fingerprint": 2897924713}, {"name": "block-colors-rails-1_14.json", "fingerprint": 2991216048}, {"name": "block-colors-water-1_13.json", "fingerprint": 1403550861}, {"name": "block-colors-rails-1_18.json", "fingerprint": 1290519433}, {"name": "block-states-1_17.json", "fingerprint": 708389835}, {"name": "block-states-1_14.json", "fingerprint": 3019307305}, {"name": "block-colors-x-ray-1_16.json", "fingerprint": 3718965679}, {"name": "block-colors-foliage-1_14.json", "fingerprint": 2762436567}, {"name": "block-states-1_18.json", "fingerprint": 708389835}, {"name": "block-colors-caves-1_15.json", "fingerprint": 3589391907}, {"name": "block-colors-caves-1_18.json", "fingerprint": 1160482320}, {"name": "block-states-1_16.json", "fingerprint": 3123485341}, {"name": "block-colors-caves-1_13.json", "fingerprint": 1062105531}, {"name": "block-colors-default-1_16.json", "fingerprint": 3699929219}, {"name": "block-colors-rails-1_13.json", "fingerprint": 158067849}, {"name": "pack.mcmeta", "fingerprint": 738398233}, {"name": "net", "fingerprint": 688234515}, {"name": "architectury_inject_FTBBackups2_common_3392e09cff9349deafffcaa53850f1ad_b1c874ff9922f6e0203ba401b16a73ca7882a18c1a7c070bd624366cbde88761ftbbackups21012devjar", "fingerprint": 1593819385}], "dependencies": [{"relationType": 3, "modId": 576589}, {"relationType": 3, "modId": 419699}], "fileFingerprint": 3130650959, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "80d60190f0e1f564b4a136cf7894d6f8272cfca8", "algo": 1}, {"value": "e92a6f0b97eb730d895cf0eb14a5a590", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3832193, "fileLength": 3727978, "downloadCount": 12796, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ftbbackups2-forge-1.18.2-1.0.13.jar", "gameVersions": ["1.18.2"], "displayName": "1.0.13 [<PERSON><PERSON>]", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.18.2", "gameVersionReleaseDate": "2022-02-28T14:23:37.723Z", "gameVersionName": "1.18.2", "gameVersionTypeId": 73250}], "downloadUrl": "https://edge.forgecdn.net/files/3884/571/ftbbackups2-forge-1.18.2-1.0.13.jar", "fileDate": "2022-07-19T10:40:33.033Z", "exposeAsAlternative": null, "modId": 622737, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "com", "fingerprint": 182394198}, {"name": "assets", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "block-colors-water-1_17.json", "fingerprint": **********}, {"name": "block-colors-foliage-1_16.json", "fingerprint": 57908228}, {"name": "block-colors-foliage-1_13.json", "fingerprint": **********}, {"name": "block-colors-default-1_13.json", "fingerprint": **********}, {"name": "block-colors-foliage-1_15.json", "fingerprint": **********}, {"name": "block-colors-default-1_15.json", "fingerprint": 292087905}, {"name": "block-colors-x-ray-1_15.json", "fingerprint": **********}, {"name": "block-colors-water-1_14.json", "fingerprint": **********}, {"name": "biome-colors.json", "fingerprint": 993934737}, {"name": "block-colors-water-1_16.json", "fingerprint": **********}, {"name": "block-colors-x-ray-1_13.json", "fingerprint": **********}, {"name": "block-colors-caves-1_16.json", "fingerprint": **********}, {"name": "block-colors-x-ray-1_18.json", "fingerprint": 304154855}, {"name": "block-colors-default-1_18.json", "fingerprint": 2096871510}, {"name": "block-colors-caves-1_17.json", "fingerprint": 4099938819}, {"name": "block-colors-foliage-1_18.json", "fingerprint": 315087539}, {"name": "block-states-1_13.json", "fingerprint": 2314797772}, {"name": "block-colors-default-1_14.json", "fingerprint": 2015178715}, {"name": "block-colors-foliage-1_17.json", "fingerprint": 3391150073}, {"name": "heightmap.json", "fingerprint": 3530235401}, {"name": "block-colors-water-1_15.json", "fingerprint": 715620426}, {"name": "block-colors-rails-1_16.json", "fingerprint": 1359809855}, {"name": "block-colors-caves-1_14.json", "fingerprint": 3072923262}, {"name": "block-colors-x-ray-1_17.json", "fingerprint": 304154855}, {"name": "block-states-1_15.json", "fingerprint": 1776260775}, {"name": "block-colors-x-ray-1_14.json", "fingerprint": 95805284}, {"name": "block-colors-rails-1_17.json", "fingerprint": 1290519433}, {"name": "block-colors-rails-1_15.json", "fingerprint": 507993599}, {"name": "block-colors-default-1_17.json", "fingerprint": 323870691}, {"name": "block-colors-water-1_18.json", "fingerprint": 2897924713}, {"name": "block-colors-rails-1_14.json", "fingerprint": 2991216048}, {"name": "block-colors-water-1_13.json", "fingerprint": 1403550861}, {"name": "block-colors-rails-1_18.json", "fingerprint": 1290519433}, {"name": "block-states-1_17.json", "fingerprint": 708389835}, {"name": "block-states-1_14.json", "fingerprint": 3019307305}, {"name": "block-colors-x-ray-1_16.json", "fingerprint": 3718965679}, {"name": "block-colors-foliage-1_14.json", "fingerprint": 2762436567}, {"name": "block-states-1_18.json", "fingerprint": 708389835}, {"name": "block-colors-caves-1_15.json", "fingerprint": 3589391907}, {"name": "block-colors-caves-1_18.json", "fingerprint": 1160482320}, {"name": "block-states-1_16.json", "fingerprint": 3123485341}, {"name": "block-colors-caves-1_13.json", "fingerprint": 1062105531}, {"name": "block-colors-default-1_16.json", "fingerprint": 3699929219}, {"name": "block-colors-rails-1_13.json", "fingerprint": 158067849}, {"name": "pack.mcmeta", "fingerprint": 738398233}, {"name": "net", "fingerprint": 1467612471}, {"name": "architectury_inject_FTBBackups2_common_3392e09cff9349deafffcaa53850f1ad_cdbd4a34ccc69ee02c9c60e9763aadd3d60c0e8d416e2a8731002a609f8adf38ftbbackups21013devjar", "fingerprint": 3339878914}], "dependencies": [{"relationType": 3, "modId": 576589}, {"relationType": 3, "modId": 419699}], "fileFingerprint": 3942518322, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "4bd315c19158c32ff5659b8218c0492fa0afb18e", "algo": 1}, {"value": "a6e9837620e1beade251bce22364b81e", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3884571, "fileLength": 3903622, "downloadCount": 532810, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ftbbackups2-forge-1.20.2-1.0.29.jar", "gameVersions": ["1.20.2", "Forge"], "displayName": "1.0.29 [<PERSON><PERSON>]", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.2", "gameVersionReleaseDate": "2023-09-21T15:25:15.053Z", "gameVersionName": "1.20.2", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/5859/644/ftbbackups2-forge-1.20.2-1.0.29.jar", "fileDate": "2024-10-30T12:19:34.853Z", "exposeAsAlternative": null, "modId": 622737, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "architectury_inject_FTBBackups2_common_bce0b87e387f49d79908433edce67376_069ac41a1920cacdf7b71f99cda3bb8276d216b8e5d679a8f2b181c8be247ca4ftbbackups21029devjar", "fingerprint": 486877499}, {"name": "assets", "fingerprint": **********}, {"name": "map_colours.json", "fingerprint": **********}, {"name": "net", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": 738398233}, {"name": "vanilla_map.json", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 576589}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "bb7192da10ffe179b12e403a9a75e7f5228c589f", "algo": 1}, {"value": "23728d8315311847b8972bf77aaa9eb3", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5859644, "fileLength": 1996972, "downloadCount": 397, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ftbbackups2-fabric-1.21.5-1.0.31.jar", "gameVersions": ["1.21.5", "<PERSON><PERSON><PERSON>"], "displayName": "1.0.31 [<PERSON><PERSON><PERSON>]", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000021.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6481/736/ftbbackups2-fabric-1.21.5-1.0.31.jar", "fileDate": "2025-04-30T22:48:15.917Z", "exposeAsAlternative": null, "modId": 622737, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "architectury_inject_FTBBackups2_common_70b3f95dcad344fc812ee0538481f5fa_3f7689c94ba3ed4b44a7a1bc0bac06e479cb338550f95a22ace521c557a04895ftbbackups21031devjar", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": 182394198}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "map_colours.json", "fingerprint": **********}, {"name": "net", "fingerprint": 755153562}, {"name": "vanilla_map.json", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 576589}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "8787e8d0c69f9b281c4381d06f7e29cdcd82bc8a", "algo": 1}, {"value": "e04f23dc62871b085e7a379225916786", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6481736, "fileLength": 2627780, "downloadCount": 398, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ftbbackups2-neoforge-1.21.5-1.0.31.jar", "gameVersions": ["1.21.5", "NeoForge"], "displayName": "1.0.31 [<PERSON><PERSON><PERSON><PERSON>]", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000021.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6481/737/ftbbackups2-neoforge-1.21.5-1.0.31.jar", "fileDate": "2025-04-30T22:48:17.877Z", "exposeAsAlternative": null, "modId": 622737, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "architectury_inject_FTBBackups2_common_70b3f95dcad344fc812ee0538481f5fa_3f7689c94ba3ed4b44a7a1bc0bac06e479cb338550f95a22ace521c557a04895ftbbackups21031devjar", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "map_colours.json", "fingerprint": **********}, {"name": "net", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": 738398233}, {"name": "vanilla_map.json", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 576589}], "fileFingerprint": 168262185, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "90e47d8e0387f5a7871df4ab67bf791db071fc82", "algo": 1}, {"value": "82e9c349659959a479ab7aaf06042aa3", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6481737, "fileLength": 1997562, "downloadCount": 749, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-07-22T11:16:46.573Z", "gamePopularityRank": 923, "thumbsUpCount": 0, "name": "FTB Backups 2", "mainFileId": 6481737, "primaryCategoryId": 435, "downloadCount": 22817364, "status": 4, "authors": [{"name": "FTB", "id": 17809311, "url": "https://www.curseforge.com/members/ftb"}, {"name": "FTBTeam", "id": 129670203, "url": "https://www.curseforge.com/members/ftbteam"}, {"name": "Official_CreeperHost", "id": 7538727, "url": "https://www.curseforge.com/members/official_creeperhost"}], "available": false, "featured": false}