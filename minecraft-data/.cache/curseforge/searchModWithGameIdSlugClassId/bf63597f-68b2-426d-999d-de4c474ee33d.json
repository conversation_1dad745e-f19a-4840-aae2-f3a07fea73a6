{"allowModDistribution": true, "screenshots": [{"description": "", "id": 483056, "title": "Fog", "modId": 654373, "url": "https://media.forgecdn.net/attachments/483/56/r6e9bjl.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/483/56/310/172/r6e9bjl.png"}, {"description": "", "id": 483055, "title": "Smoke Particles", "modId": 654373, "url": "https://media.forgecdn.net/attachments/483/55/ejdl7z7.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/483/55/310/172/ejdl7z7.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "rubidium-extra-0.5.7e+mc1.21-build.132.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6860464}, {"filename": "rubidium-extra-0.5.7e+mc1.21-build.132.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6860464}, {"filename": "rubidium-extra-*******+mc1.20.1-build.131.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6101745}, {"filename": "rubidium-extra-*******+mc1.20.1-build.131.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6101745}, {"filename": "rubidium-extra-0.5.6+mc1.20.6-build.123.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5493258}, {"filename": "rubidium-extra-0.5.4.2+mc1.20.4-build.120.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5176872}, {"filename": "rubidium-extra-0.5.3.2+mc1.20.2-build.112.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4920164}, {"filename": "rubidium-extra-0.5.3.1+mc1.20.1-build.110.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4873766}, {"filename": "rubidium-extra-0.5.3.1+mc1.20.1-build.110.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4873766}, {"filename": "rubidium-extra-0.4.18+mc1.16.5-build.107.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 4823321}, {"filename": "rubidium-extra-0.4.18+mc1.16.5-build.107.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 4823321}, {"filename": "rubidium-extra-0.4.18+mc1.16.5-build.107.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 4823321}, {"filename": "rubidium-extra-0.4.18+mc1.16.5-build.107.jar", "releaseType": 1, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 4823321}, {"filename": "rubidium-extra-0.4.18+mc1.16.5-build.107.jar", "releaseType": 1, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 4823321}, {"filename": "rubidium-extra-0.4.18+mc1.16.5-build.107.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 4823321}, {"filename": "rubidium-extra-0.4.19+mc1.19.2-build.105.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4768249}, {"filename": "rubidium-extra-0.4.19+mc1.19.4-build.88.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4544135}, {"filename": "rubidium-extra-0.4.18+mc1.18.2-build.86.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 4513591}, {"filename": "rubidium-extra-0.4.17+mc1.19.3-build.73.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4431042}, {"filename": "RubidiumExtra-1.19.2-0.4.11.44.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4059817}, {"filename": "RubidiumExtra-1.19.2-0.4.11.44.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4059817}, {"filename": "sodiumextra-forge-0.4.6+mc1.19.1.jar", "releaseType": 2, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3913722}, {"filename": "sodiumextra-forge-0.4.6+mc1.19.1.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3913722}, {"filename": "sodium-extra-forge-0.4.6+mc1.18.2.jar", "releaseType": 2, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3913719}, {"filename": "sodium-extra-forge-0.4.6+mc1.16.5.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3913717}], "dateCreated": "2022-08-03T10:18:14.38Z", "logo": {"description": "", "id": 587602, "title": "637956775773435327.png", "modId": 654373, "url": "https://media.forgecdn.net/avatars/587/602/637956775773435327.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/587/602/256/256/637956775773435327.png"}, "links": {"sourceUrl": "https://github.com/dima-dencep/rubidium-extra", "issuesUrl": "https://github.com/dima-dencep/rubidium-extra/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/rubidium-extra", "wikiUrl": ""}, "dateReleased": "2025-08-07T18:15:15.043Z", "id": 654373, "categories": [{"gameId": 432, "classId": 6, "name": "Miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "parentCategoryId": 6, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "displayIndex": 0, "class": false}], "slug": "rubidium-extra", "gameId": 432, "summary": "Port of Sodium Extra to work with Embeddium/Rubidium on (Neo)Forge", "latestFiles": [{"gameId": 432, "fileName": "sodiumextra-forge-0.4.6+mc1.19.1.jar", "gameVersions": ["Forge", "1.19.1", "1.19"], "displayName": "sodiumextra-forge-0.4.6+mc1.19.1.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.19.1", "gameVersionReleaseDate": "2022-06-28T00:00:00Z", "gameVersionName": "1.19.1", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-06-07T15:38:07.377Z", "gameVersionName": "1.19", "gameVersionTypeId": 73407}], "downloadUrl": "https://edge.forgecdn.net/files/3913/722/sodiumextra-forge-0.4.6%2bmc1.19.1.jar", "fileDate": "2022-08-03T03:05:11.787Z", "exposeAsAlternative": null, "modId": 654373, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "sodiumextra.mixins.json", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "sodiumextra-forge-refmap.json", "fingerprint": **********}, {"name": "me", "fingerprint": **********}, {"name": "net", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 574856}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "44f28e23e2f27ee321eb8c075201af17d5fbfcd8", "algo": 1}, {"value": "180fc0707f8b655e28842be77fcbe5f7", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3913722, "fileLength": 160904, "downloadCount": 1104, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "rubidium-extra-*******+mc1.20.1-build.131.jar", "gameVersions": ["Client", "NeoForge", "1.20.1", "Forge"], "displayName": "[(Neo)Forge 1.20.1] v*******", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6101/745/rubidium-extra-*******%2bmc1.20.1-build.131.jar", "fileDate": "2025-01-19T03:16:15.170Z", "exposeAsAlternative": null, "modId": 654373, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "me", "fingerprint": 412179423}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "rubidium-extra-refmap.json", "fingerprint": **********}, {"name": "sodium-extra.mixins.json", "fingerprint": 369665014}], "dependencies": [{"relationType": 3, "modId": 908741}, {"relationType": 2, "modId": 581495}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "bb422d5626bf69841e444a3e545c8e71b6928cf8", "algo": 1}, {"value": "a96fb0a07b75eb07b200e8f9c0dfb9f4", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6101745, "fileLength": 568608, "downloadCount": 1806879, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "rubidium-extra-0.5.7e+mc1.21-build.132.jar", "gameVersions": ["1.21", "NeoForge", "1.21.1"], "displayName": "[NeoForge 1.21] v0.5.7e", "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6860/464/rubidium-extra-0.5.7e%2bmc1.21-build.132.jar", "fileDate": "2025-08-07T18:15:14.603Z", "exposeAsAlternative": null, "modId": 654373, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE.txt", "fingerprint": **********}, {"name": "assets", "fingerprint": 113843131}, {"name": "icon.png", "fingerprint": **********}, {"name": "me", "fingerprint": **********}, {"name": "sodium-extra.mixins.json", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 908741}, {"relationType": 2, "modId": 581495}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "963b6b8c92197fd9a6f1dc80b9473e3d1b72256e", "algo": 1}, {"value": "e536a480534445daee5ae25797a54f3e", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6860464, "fileLength": 383418, "downloadCount": 236, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-08-08T03:44:30.04Z", "gamePopularityRank": 543, "thumbsUpCount": 0, "name": "Embeddium (Rubidium) Extra", "mainFileId": 6860464, "primaryCategoryId": 425, "downloadCount": 26605601, "status": 4, "authors": [{"name": "dima_dencep", "id": 102141805, "url": "https://www.curseforge.com/members/dima_dencep"}], "available": false, "featured": false}