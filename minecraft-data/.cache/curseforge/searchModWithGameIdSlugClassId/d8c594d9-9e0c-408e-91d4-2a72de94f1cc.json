{"allowModDistribution": false, "screenshots": [{"description": "", "id": 915555, "title": "Swampjaw", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/555/swampjaw-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/555/310/172/swampjaw-update.png"}, {"description": "", "id": 915552, "title": "<PERSON><PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/552/bellringer-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/552/310/172/bellringer-update.png"}, {"description": "", "id": 915551, "title": "Void Worm", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/551/void-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/551/310/172/void-update.png"}, {"description": "", "id": 915550, "title": "<PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/550/witherstorm-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/550/310/172/witherstorm-update.png"}, {"description": "", "id": 915549, "title": "Obsidilith", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/549/obsidilith-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/549/310/172/obsidilith-update.png"}, {"description": "", "id": 915548, "title": "Void Blossom", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/548/voidblossom-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/548/310/172/voidblossom-update.png"}, {"description": "", "id": 915547, "title": "Nether Gauntlet", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/547/nethergauntlet-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/547/310/172/nethergauntlet-update.png"}, {"description": "", "id": 915546, "title": "Night Lich", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/546/nightlich-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/546/310/172/nightlich-update.png"}, {"description": "", "id": 915545, "title": "Val<PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/545/valkyrie-queen-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/545/310/172/valkyrie-queen-update.png"}, {"description": "", "id": 915544, "title": "Sun Spirit", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/544/sun-spirit-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/544/310/172/sun-spirit-update.png"}, {"description": "", "id": 915543, "title": "Slide<PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/543/slider-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/543/310/172/slider-update.png"}, {"description": "", "id": 915541, "title": "<PERSON><PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/541/leviathan-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/541/310/172/leviathan-update.png"}, {"description": "", "id": 915540, "title": "<PERSON><PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/540/harbinger-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/540/310/172/harbinger-update.png"}, {"description": "", "id": 915539, "title": "<PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/539/enderguardian-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/539/310/172/enderguardian-update.png"}, {"description": "", "id": 915538, "title": "Netherite Monstrosity", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/538/netherite-monstrosoity-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/538/310/172/netherite-monstrosoity-update.png"}, {"description": "", "id": 915537, "title": "<PERSON><PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/537/ignis-update.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/537/310/172/ignis-update.png"}, {"description": "", "id": 915536, "title": "Frostmaw", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/536/frostmaw-update2.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/536/310/172/frostmaw-update2.png"}, {"description": "", "id": 915534, "title": "Ferrous Wroughtnaut", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/534/ferrous-update2.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/534/310/172/ferrous-update2.png"}, {"description": "", "id": 915519, "title": "<PERSON>er", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/519/wither-update2.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/519/310/172/wither-update2.png"}, {"description": "", "id": 915518, "title": "<PERSON><PERSON>", "modId": 1063296, "url": "https://media.forgecdn.net/attachments/915/518/enderdragon-update3.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/915/518/310/172/enderdragon-update3.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "enhanced_boss_bars-1.21-1.0.0.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5529872}, {"filename": "enhanced_boss_bars-1.20.1-1.0.0.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5529869}, {"filename": "enhanced_boss_bars-1.19.2-1.0.0.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5529866}, {"filename": "enhanced_boss_bars-1.16.5-1.0.0.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 5529858}], "dateCreated": "2024-07-14T10:43:49.447Z", "logo": {"description": "", "id": 1039449, "title": "638564982213431593.png", "modId": 1063296, "url": "https://media.forgecdn.net/avatars/1039/449/638564982213431593.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1039/449/256/256/638564982213431593.png"}, "links": {"sourceUrl": null, "issuesUrl": null, "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/enhanced-boss-bars", "wikiUrl": null}, "dateReleased": "2024-07-13T20:36:59.377Z", "id": 1063296, "categories": [{"gameId": 432, "classId": 6, "name": "Adventure and RPG", "dateModified": "2014-05-08T17:42:09.54Z", "parentCategoryId": 6, "id": 422, "iconUrl": "https://media.forgecdn.net/avatars/6/37/635351497295252123.png", "slug": "adventure-rpg", "url": "https://www.curseforge.com/minecraft/mc-mods/adventure-rpg", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "<PERSON><PERSON>", "dateModified": "2014-05-08T17:46:59.507Z", "parentCategoryId": 406, "id": 411, "iconUrl": "https://media.forgecdn.net/avatars/6/53/635351500195070414.png", "slug": "world-mobs", "url": "https://www.curseforge.com/minecraft/mc-mods/world-gen/world-mobs", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "parentCategoryId": 6, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "displayIndex": 0, "class": false}], "slug": "enhanced-boss-bars", "gameId": 432, "summary": "New improved boss bars!", "latestFiles": [{"gameId": 432, "fileName": "enhanced_boss_bars-1.21-1.0.0.jar", "gameVersions": ["1.21", "Client", "Forge"], "displayName": "enhanced_boss_bars-1.21-1.0.0.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000021", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": null, "fileDate": "2024-07-13T20:36:59.377Z", "exposeAsAlternative": null, "modId": 1063296, "modules": [{"name": "META-INF", "fingerprint": 923356993}, {"name": "assets", "fingerprint": 1858295994}, {"name": "com", "fingerprint": 897457120}, {"name": "logo.png", "fingerprint": 1742288278}, {"name": "pack.mcmeta", "fingerprint": 3062343988}], "dependencies": [], "fileFingerprint": 18741840, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "150c9ee96a620d692bca99ddf69adb599132c8b5", "algo": 1}, {"value": "7d4edfd6cf760c5f0e85a182235d99b0", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5529872, "fileLength": 244955, "downloadCount": 10875, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2024-07-14T15:53:25.25Z", "gamePopularityRank": 1127, "thumbsUpCount": 0, "name": "Enhanced Boss Bars", "mainFileId": 5529872, "primaryCategoryId": 425, "downloadCount": 3416465, "status": 4, "authors": [{"name": "<PERSON><PERSON><PERSON>", "id": 103071936, "url": "https://www.curseforge.com/members/nakuring"}], "available": false, "featured": false}