{"allowModDistribution": true, "screenshots": [{"description": "Are you curious as to what armor or items a mob currently has? This will reveal this information to you, ", "id": 16111, "title": "<PERSON><PERSON> Items", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/111/brqbklqciaarrec.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/111/310/172/brqbklqciaarrec.png"}, {"description": "Ever wonder who a player head is representing, but you don't want to break it to find out? Now you can do that, just look at the skull with this handy mod installed. ", "id": 16110, "title": "Player Skulls", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/110/bsrj3sgccaatrgl.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/110/310/172/bsrj3sgccaatrgl.png"}, {"description": "When looking at a beacon you can see what tier it is, along with what effects have been selected for it. By default the main effect is always Regen, so under normal circumstances only a secondary effect will be displayed. ", "id": 16109, "title": "Beacons", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/109/buu3p9wccae-2f9.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/109/310/172/buu3p9wccae-2f9.png"}, {"description": "In tinkers construct you can make landmines and make them camouflaged. This mod will prevent waila from telling the difference between the land mine and its camo block. ", "id": 16108, "title": "Tinkers: Land Mine hidden", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/108/bul7dl3caaay3fw.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/108/310/172/bul7dl3caaay3fw.png"}, {"description": "All enchantment books are capable of displaying a description of what they do, even enchantment books from other mods work off of this. If a book lacks a description, please report it to me on the issue tracker. ", "id": 16107, "title": "Enchantment Books", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/107/yusco4c.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/107/310/172/yusco4c.png"}, {"description": "Thaumcraft recharge pedestals display the name of the wand they contain, along with the amount of vis they each contain. ", "id": 16106, "title": "Thaumcraft: Re<PERSON><PERSON> Pedestal", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/106/bymayxwcqaejrdv.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/106/310/172/bymayxwcqaejrdv.png"}, {"description": "This mod has full support for Version Checker. When version checker is installed, you will be alerted of new updates. ", "id": 16105, "title": "Version Checker Integration", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/105/njzlhqc.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/105/310/172/njzlhqc.png"}, {"description": "When looking at a drying rack, it will display its conversion progress as a percentage. ", "id": 16104, "title": "Tinkers: Drying racks", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/104/bybrr1_cmaej7aa.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/104/310/172/bybrr1_cmaej7aa.png"}, {"description": "When looking at an apricorn bush you can see its growth rate as a percentage. ", "id": 16103, "title": "Pixelmon: Apr<PERSON>s", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/103/btn1mv5ccaaoaik.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/103/310/172/btn1mv5ccaaoaik.png"}, {"description": "You can see what <PERSON>pect is in <PERSON><PERSON><PERSON><PERSON>'s warded jars, by looking at them. The amount and the type is displayed on the tool tip. ", "id": 16102, "title": "Aspect Jars", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/102/byb7wphcaaazllf.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/102/310/172/byb7wphcaaazllf.png"}, {"description": "When looking at a furnace, you can see how much burn time is left. You can also see what is inside if you are sneaking. ", "id": 16101, "title": "Furnace Info", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/101/caziq4a.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/101/310/172/caziq4a.png"}, {"description": "When looking at a block, <PERSON><PERSON><PERSON> will advise you on if your tool can break that block. ", "id": 16100, "title": "Harvestability", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/100/bsq-xnnciae8ldl.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/100/310/172/bsq-xnnciae8ldl.png"}, {"description": "When looking at a pet type mob like an Ocelot or a Wolf you can see who its owner is, if it has one. ", "id": 16099, "title": "<PERSON>", "modId": 224712, "url": "https://media.forgecdn.net/attachments/16/99/br04rrwciaaplog.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/16/99/310/172/br04rrwciaaplog.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "WAWLA-1.16.5-8.0.9.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3732265}, {"filename": "WAWLA-1.16.5-8.0.8.jar", "releaseType": 3, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3480164}, {"filename": "WAWLA-1.16.4-7.0.4.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3189211}, {"filename": "WAWLA-1.16.3-6.0.1.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3063205}, {"filename": "WAWLA-1.16.2-5.0.1.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3063195}, {"filename": "WAWLA-1.16.1-4.0.1.jar", "releaseType": 1, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3063185}, {"filename": "WAWLA-1.14.4-2.9.1.jar", "releaseType": 1, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 3023719}, {"filename": "WAWLA-1.15.2-3.0.4.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 3008867}, {"filename": "WAWLA-1.15.2-3.0.3.jar", "releaseType": 3, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 2988150}, {"filename": "WAWLA-1.15.2-3.0.2.jar", "releaseType": 2, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 2944889}, {"filename": "Wawla-1.12.2-2.6.275.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2916368}, {"filename": "Wawla-1.12.2-2.6.274.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 2875678}, {"filename": "Wawla-1.12.2-2.5.272.jar", "releaseType": 3, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2706063}, {"filename": "Wawla-1.12.1-2.5.243.jar", "releaseType": 1, "gameVersion": "1.12", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2478098}, {"filename": "Wawla-1.12.1-2.5.243.jar", "releaseType": 1, "gameVersion": "1.12.1", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2478098}, {"filename": "Wawla-1.11.2-2.4.0.233.jar", "releaseType": 3, "gameVersion": "1.11.2", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2429417}, {"filename": "Wawla-1.11.2-2.4.0.231.jar", "releaseType": 1, "gameVersion": "1.11.2", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2407377}, {"filename": "Wawla-1.11.2-2.4.0.219.jar", "releaseType": 2, "gameVersion": "1.11.2", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2405756}, {"filename": "Wawla-1.10.2-2.3.2.215.jar", "releaseType": 1, "gameVersion": "1.10.2", "gameVersionTypeId": 572, "modLoader": null, "fileId": 2398640}, {"filename": "Wawla-1.10.2-2.3.1.214.jar", "releaseType": 3, "gameVersion": "1.10.2", "gameVersionTypeId": 572, "modLoader": null, "fileId": 2343128}, {"filename": "Wawla-1.10.2-2.3.0.203.jar", "releaseType": 1, "gameVersion": "1.9.4", "gameVersionTypeId": 552, "modLoader": null, "fileId": 2326949}, {"filename": "Wawla-1.10.2-2.3.0.197.jar", "releaseType": 1, "gameVersion": "1.10", "gameVersionTypeId": 572, "modLoader": null, "fileId": 2318040}, {"filename": "Wawla-1.10.2-2.3.0.197.jar", "releaseType": 1, "gameVersion": "1.10.1", "gameVersionTypeId": 572, "modLoader": null, "fileId": 2318040}, {"filename": "Wawla-1.9.4-2.2.1.192.jar", "releaseType": 3, "gameVersion": "1.9.4", "gameVersionTypeId": 552, "modLoader": null, "fileId": 2306375}, {"filename": "Wawla-1.0.5.120.jar", "releaseType": 1, "gameVersion": "1.7.10", "gameVersionTypeId": 5, "modLoader": null, "fileId": 2298772}, {"filename": "Wawla-1.9-2.2.0.186.jar", "releaseType": 1, "gameVersion": "1.9", "gameVersionTypeId": 552, "modLoader": null, "fileId": 2293843}, {"filename": "Wawla-1.9-2.2.0.184.jar", "releaseType": 3, "gameVersion": "1.9", "gameVersionTypeId": 552, "modLoader": null, "fileId": 2293369}, {"filename": "Wawla-1.8.9-1.1.4.171.jar", "releaseType": 1, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2287651}, {"filename": "Wawla-1.8.9-1.1.4.170.jar", "releaseType": 3, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2287647}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.7", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.4", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.8", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.3", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.6", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.2", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.5", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}, {"filename": "Wawla-1.4.7-1.8+.jar", "releaseType": 1, "gameVersion": "1.8.1", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2276743}], "dateCreated": "2014-10-05T21:17:57.857Z", "logo": {"description": "", "id": 11204, "title": "635483047149673159.png", "modId": 224712, "url": "https://media.forgecdn.net/avatars/11/204/635483047149673159.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/11/204/256/256/635483047149673159.png"}, "links": {"sourceUrl": "https://github.com/Darkhax-Minecraft/WAWLA", "issuesUrl": "https://github.com/Darkhax-Minecraft/WAWLA/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/wawla", "wikiUrl": ""}, "dateReleased": "2022-04-02T22:24:26.353Z", "id": 224712, "categories": [{"gameId": 432, "classId": 6, "name": "Map and Information", "dateModified": "2014-05-08T17:42:23.74Z", "parentCategoryId": 6, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Server Utility", "dateModified": "2014-05-08T17:44:55.057Z", "parentCategoryId": 6, "id": 435, "iconUrl": "https://media.forgecdn.net/avatars/6/48/635351498950580836.png", "slug": "server-utility", "url": "https://www.curseforge.com/minecraft/mc-mods/server-utility", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}], "slug": "wawla", "gameId": 432, "summary": "Waila/Hwyla addon which adds all the info.", "latestFiles": [{"gameId": 432, "fileName": "Wawla-1.11.2-2.4.0.219.jar", "gameVersions": ["1.11.2"], "displayName": "Wawla-1.11.2-2.4.0.219.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000011.0000000002", "gameVersion": "1.11.2", "gameVersionReleaseDate": "2016-12-21T06:00:00Z", "gameVersionName": "1.11.2", "gameVersionTypeId": 599}], "downloadUrl": "https://edge.forgecdn.net/files/2405/756/Wawla-1.11.2-2.4.0.219.jar", "fileDate": "2017-04-11T18:50:05.667Z", "exposeAsAlternative": null, "modId": 224712, "modules": [{"name": "META-INF", "fingerprint": 1655036106}, {"name": "net", "fingerprint": 2064423068}, {"name": "assets", "fingerprint": 2077201241}, {"name": "mcmod.info", "fingerprint": 259357148}, {"name": "pack.mcmeta", "fingerprint": 3337708757}], "dependencies": [{"relationType": 2, "modId": 253449}, {"relationType": 2, "modId": 73488}], "fileFingerprint": 1486425965, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "67346312d86ea0af29cd13900b6e0529ad16cfe9", "algo": 1}, {"value": "fb22c2c1e213263f0bb9328433f9cf83", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2405756, "fileLength": 73549, "downloadCount": 3222, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Wawla-1.12.2-2.5.272.jar", "gameVersions": ["1.12.2"], "displayName": "Wawla-1.12.2-2.5.272.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000012.0000000002", "gameVersion": "1.12.2", "gameVersionReleaseDate": "2017-09-18T05:00:00Z", "gameVersionName": "1.12.2", "gameVersionTypeId": 628}], "downloadUrl": "https://edge.forgecdn.net/files/2706/63/Wawla-1.12.2-2.5.272.jar", "fileDate": "2019-05-01T19:18:22.583Z", "exposeAsAlternative": null, "modId": 224712, "modules": [{"name": "META-INF", "fingerprint": 2698829744}, {"name": "assets", "fingerprint": 3057676969}, {"name": "mcmod.info", "fingerprint": 2207594574}, {"name": "net", "fingerprint": 2161130908}, {"name": "pack.mcmeta", "fingerprint": 3337708757}], "dependencies": [{"relationType": 2, "modId": 253449}, {"relationType": 2, "modId": 73488}], "fileFingerprint": 72180177, "fileStatus": 4, "releaseType": 3, "hashes": [{"value": "b663e2869e1ab1df53604ace68aec49b04ffdd6b", "algo": 1}, {"value": "9fb22c03a295fcdaa590a3b0cd3df793", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2706063, "fileLength": 94174, "downloadCount": 5466, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Wawla-1.12.2-2.6.275.jar", "gameVersions": ["1.12.2"], "displayName": "Wawla-1.12.2-2.6.275.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000012.0000000002", "gameVersion": "1.12.2", "gameVersionReleaseDate": "2017-09-18T05:00:00Z", "gameVersionName": "1.12.2", "gameVersionTypeId": 628}], "downloadUrl": "https://edge.forgecdn.net/files/2916/368/Wawla-1.12.2-2.6.275.jar", "fileDate": "2020-03-30T09:15:19.083Z", "exposeAsAlternative": null, "modId": 224712, "modules": [{"name": "META-INF", "fingerprint": 2898121098}, {"name": "assets", "fingerprint": 3891243095}, {"name": "mcmod.info", "fingerprint": 2022755023}, {"name": "net", "fingerprint": 2658338695}, {"name": "pack.mcmeta", "fingerprint": 2831549854}], "dependencies": [{"relationType": 3, "modId": 253449}], "fileFingerprint": 1231381817, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "783157c607149875de1e045d72382d370256257c", "algo": 1}, {"value": "3762f87395a87af86e9552c6d7ca7e06", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2916368, "fileLength": 94115, "downloadCount": 21705602, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "WAWLA-1.15.2-3.0.2.jar", "gameVersions": ["1.15.2", "Forge"], "displayName": "WAWLA-1.15.2-3.0.2.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000015.0000000002", "gameVersion": "1.15.2", "gameVersionReleaseDate": "2020-01-22T00:00:00Z", "gameVersionName": "1.15.2", "gameVersionTypeId": 68722}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/2944/889/WAWLA-1.15.2-3.0.2.jar", "fileDate": "2020-04-30T20:43:47.523Z", "exposeAsAlternative": null, "modId": 224712, "modules": [{"name": "META-INF", "fingerprint": 3748678649}, {"name": "net", "fingerprint": 3258696462}, {"name": "pack.mcmeta", "fingerprint": 3570862391}, {"name": "wawla_at.cfg", "fingerprint": 891415590}, {"name": "assets", "fingerprint": 2958270480}], "dependencies": [{"relationType": 3, "modId": 228525}, {"relationType": 3, "modId": 253449}], "fileFingerprint": 1228921769, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "92632f2c49aa996bbf07f80504cfcd7ce7121351", "algo": 1}, {"value": "b0a6ce6fcf3437d5c013c2230285cf57", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2944889, "fileLength": 23718, "downloadCount": 320493, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "WAWLA-1.16.5-8.0.8.jar", "gameVersions": ["1.16.5", "Forge"], "displayName": "WAWLA-1.16.5-8.0.8.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/3480/164/WAWLA-1.16.5-8.0.8.jar", "fileDate": "2021-10-03T15:07:09.420Z", "exposeAsAlternative": null, "modId": 224712, "modules": [{"name": "META-INF", "fingerprint": 4155294331}, {"name": "assets", "fingerprint": 3725645950}, {"name": "net", "fingerprint": 1822608394}, {"name": "pack.mcmeta", "fingerprint": 176560983}, {"name": "wawla_at.cfg", "fingerprint": 891415590}], "dependencies": [{"relationType": 3, "modId": 228525}, {"relationType": 3, "modId": 253449}], "fileFingerprint": 1459273229, "fileStatus": 4, "releaseType": 3, "hashes": [{"value": "4410d380a6b42d8b666c291eebf4f06c7a273484", "algo": 1}, {"value": "22d6d96c015f3926f76b7a4f3107ec95", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3480164, "fileLength": 34243, "downloadCount": 88081, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "WAWLA-1.16.5-8.0.9.jar", "gameVersions": ["1.16.5", "Forge"], "displayName": "WAWLA-1.16.5-8.0.9.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/3732/265/WAWLA-1.16.5-8.0.9.jar", "fileDate": "2022-04-02T22:24:24.227Z", "exposeAsAlternative": null, "modId": 224712, "modules": [{"name": "META-INF", "fingerprint": 998567376}, {"name": "assets", "fingerprint": 3725645950}, {"name": "net", "fingerprint": 1822608394}, {"name": "pack.mcmeta", "fingerprint": 176560983}, {"name": "wawla_at.cfg", "fingerprint": 891415590}], "dependencies": [{"relationType": 3, "modId": 228525}, {"relationType": 3, "modId": 253449}], "fileFingerprint": 3064548838, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "a81613367c0dd72ea126e5708a4ecf3a5308595e", "algo": 1}, {"value": "3e2e589f1f14baefb013708a78af8ee6", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3732265, "fileLength": 34261, "downloadCount": 3498401, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2022-04-02T22:35:13.577Z", "gamePopularityRank": 1249, "thumbsUpCount": 0, "name": "<PERSON><PERSON><PERSON> - What Are We Looking At", "mainFileId": 3732265, "primaryCategoryId": 423, "downloadCount": 77491986, "status": 4, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 6968547, "url": "https://www.curseforge.com/members/darkhaxdev"}], "available": false, "featured": false}