{"allowModDistribution": true, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "FpsReducer2-neoforge-1.21.9-2.14.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7053436}, {"filename": "FpsReducer2-fabric-1.21.9-2.14.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7051631}, {"filename": "FpsReducer2-fabric-1.21.9-2.14.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 7051631}, {"filename": "FpsReducer2-forge-1.20.1-2.5.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6791399}, {"filename": "FpsReducer2-forge-1.20.1-2.5.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6791399}, {"filename": "FpsReducer2-fabric-1.21.7-2.13.1.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6714563}, {"filename": "FpsReducer2-fabric-1.21.7-2.13.1.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6714563}, {"filename": "FpsReducer2-fabric-1.21.7-2.13.1.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6714563}, {"filename": "FpsReducer2-fabric-1.21.7-2.13.1.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6714563}, {"filename": "FpsReducer2-fabric-1.21.7-2.13.1.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6714563}, {"filename": "FpsReducer2-fabric-1.21.7-2.13.1.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6714563}, {"filename": "FpsReducer2-forge-1.21.6-2.13.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6680545}, {"filename": "FpsReducer2-forge-1.21.6-2.13.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6680545}, {"filename": "FpsReducer2-forge-1.21.6-2.13.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6680545}, {"filename": "FpsReducer2-neoforge-1.21.6-2.13.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6664630}, {"filename": "FpsReducer2-neoforge-1.21.6-2.13.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6664630}, {"filename": "FpsReducer2-neoforge-1.21.6-2.13.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6664630}, {"filename": "FpsReducer2-forge-1.21.5-2.12.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6363498}, {"filename": "FpsReducer2-neoforge-1.21.5-2.12.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6363496}, {"filename": "FpsReducer2-fabric-1.21.5-2.12.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6348754}, {"filename": "FpsReducer2-fabric-1.21.5-2.12.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6348754}, {"filename": "FpsReducer2-forge-1.21.4-2.11.1.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5971577}, {"filename": "FpsReducer2-forge-1.21.4-2.11.1.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5971577}, {"filename": "FpsReducer2-neoforge-1.21.4-2.11.1.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5971575}, {"filename": "FpsReducer2-neoforge-1.21.4-2.11.1.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5971575}, {"filename": "FpsReducer2-neoforge-1.21.4-2.11.1.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5971575}, {"filename": "FpsReducer2-fabric-1.21.3-2.11.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5841779}, {"filename": "FpsReducer2-fabric-1.21.3-2.11.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 5841779}, {"filename": "FpsReducer2-fabric-1.21.3-2.11.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5841779}, {"filename": "FpsReducer2-fabric-1.21.3-2.11.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 5841779}, {"filename": "FpsReducer2-fabric-1.21.3-2.11.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5841779}, {"filename": "FpsReducer2-fabric-1.21.3-2.11.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 5841779}, {"filename": "FpsReducer2-neoforge-1.21-2.10.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5561051}, {"filename": "FpsReducer2-neoforge-1.21-2.10.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5561051}, {"filename": "FpsReducer2-forge-1.19.4-2.4.1.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5511372}, {"filename": "FpsReducer2-forge-1.19.3-2.2.1.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5511363}, {"filename": "FpsReducer2-forge-1.19.2-2.1.1.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5511353}, {"filename": "FpsReducer2-forge-1.19.2-2.1.1.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5511353}, {"filename": "FpsReducer2-forge-1.19.2-2.1.1.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5511353}, {"filename": "FpsReducer2-neoforge-1.20.6-2.8.2.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5440968}, {"filename": "FpsReducer2-neoforge-1.20.6-2.8.2.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5440968}, {"filename": "FpsReducer2-forge-1.21-2.9.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5428258}, {"filename": "FpsReducer2-forge-1.21-2.9.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5428258}, {"filename": "FpsReducer2-fabric-1.21-2.9.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5427187}, {"filename": "FpsReducer2-fabric-1.21-2.9.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 5427187}, {"filename": "FpsReducer2-fabric-1.21-2.9.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5427187}, {"filename": "FpsReducer2-fabric-1.21-2.9.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 5427187}, {"filename": "FpsReducer2-forge-1.20.6-2.8.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5314701}, {"filename": "FpsReducer2-fabric-1.20.5-2.8.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5287930}, {"filename": "FpsReducer2-fabric-1.20.5-2.8.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5287930}, {"filename": "FpsReducer2-fabric-1.20.5-2.8.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5287930}, {"filename": "FpsReducer2-fabric-1.20.5-2.8.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5287930}, {"filename": "FpsReducer2-neoforge-1.20.4-2.7.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4970820}, {"filename": "FpsReducer2-forge-1.20.4-2.7.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4970492}, {"filename": "FpsReducer2-fabric-1.20.4-2.7.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4949217}, {"filename": "FpsReducer2-fabric-1.20.4-2.7.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 4949217}, {"filename": "FpsReducer2-neoforge-1.20.2-2.6.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4864457}, {"filename": "FpsReducer2-forge-1.20.2-2.6.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4777305}, {"filename": "FpsReducer2-fabric-1.20.2-2.6.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4765072}, {"filename": "FpsReducer2-fabric-1.20.2-2.6.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 4765072}, {"filename": "FpsReducer2-forge-1.20-2.5.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4586784}, {"filename": "FpsReducer2-forge-1.20-2.5.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4586784}, {"filename": "FpsReducer2-fabric-1.20-2.5.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4578392}, {"filename": "FpsReducer2-fabric-1.20-2.5.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 4578392}, {"filename": "FpsReducer2-fabric-1.20-2.5.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4578392}, {"filename": "FpsReducer2-fabric-1.20-2.5.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 4578392}, {"filename": "FpsReducer2-fabric-1.19.4-2.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4552128}, {"filename": "FpsReducer2-fabric-1.19.4-2.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 4552128}, {"filename": "FpsReducer2-fabric-1.19.3-2.2.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4160319}, {"filename": "FpsReducer2-fabric-1.19.3-2.2.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 4160319}, {"filename": "FpsReducer2-fabric-1.19.2-2.1.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3931334}, {"filename": "FpsReducer2-fabric-1.19.2-2.1.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 3931334}, {"filename": "FpsReducer2-fabric-1.19.2-2.1.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3931334}, {"filename": "FpsReducer2-fabric-1.19.2-2.1.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 3931334}, {"filename": "FpsReducer2-fabric-1.19.2-2.1.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3931334}, {"filename": "FpsReducer2-fabric-1.19.2-2.1.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 3931334}, {"filename": "FpsReducer2-fabric-1.18.2-2.0.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 3680785}, {"filename": "FpsReducer2-fabric-1.18.2-2.0.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 3680785}, {"filename": "FpsReducer2-forge-1.18.2-2.0.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3680770}, {"filename": "FpsReducer2-forge-1.18.2-2.0.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3680770}, {"filename": "FpsReducer-fabric-1.25-mc1.18.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 3548893}, {"filename": "FpsReducer-forge-1.25-mc1.18.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3548891}, {"filename": "FpsReducer-fabric-1.24-mc1.18-beta.jar", "releaseType": 2, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 3529586}, {"filename": "FpsReducer-forge-1.24-mc1.17.1.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 3510442}, {"filename": "FpsReducer-fabric-1.24-mc1.17.1.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 3510440}, {"filename": "FpsReducer-forge-1.24-mc1.16.5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3510435}, {"filename": "FpsReducer-fabric-1.24-mc1.16.5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3510432}, {"filename": "FpsReducer-fabric-1.23.1-mc1.17.1.jar", "releaseType": 1, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 3425771}, {"filename": "FpsReducer-mc1.15.2-1.20.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 3362164}, {"filename": "FpsReducer-mc1.12.2-1.20.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 3362160}, {"filename": "FpsReducer-mc1.16.5-1.19.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3281559}, {"filename": "FpsReducer-mc1.16.3-1.18.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3054551}, {"filename": "FpsReducer-mc1.16.2-1.18.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3042763}, {"filename": "FpsReducer-mc1.16.1-1.17.jar", "releaseType": 1, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3009115}, {"filename": "FpsReducer-mc1.14.4-1.17.jar", "releaseType": 1, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 3009112}, {"filename": "FpsReducer-mc1.15.1-1.14.jar", "releaseType": 1, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 2853230}, {"filename": "FpsReducer-mc1.14.4-1.12.4.jar", "releaseType": 1, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": null, "fileId": 2748189}, {"filename": "FpsReducer-mc1.14.4-1.12.4.jar", "releaseType": 1, "gameVersion": "1.14.2", "gameVersionTypeId": 64806, "modLoader": null, "fileId": 2748189}, {"filename": "FpsReducer-mc1.14.4-1.12.4.jar", "releaseType": 1, "gameVersion": "1.14.3", "gameVersionTypeId": 64806, "modLoader": null, "fileId": 2748189}, {"filename": "FpsReducer-mc1.13.2-1.12.4.jar", "releaseType": 1, "gameVersion": "1.13.2", "gameVersionTypeId": 55023, "modLoader": null, "fileId": 2732478}, {"filename": "FpsReducer-mc1.12.2-1.12.4.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2732475}, {"filename": "FpsReducer-mc1.14.2-1.12.2-beta-1.jar", "releaseType": 2, "gameVersion": "1.14.2", "gameVersionTypeId": 64806, "modLoader": null, "fileId": 2728661}, {"filename": "FpsReducer-mc1.13.2-1.11-alpha-1.jar", "releaseType": 3, "gameVersion": "1.13.2", "gameVersionTypeId": 55023, "modLoader": null, "fileId": 2686699}, {"filename": "FpsReducer-mc1.11.2-1.10.3.jar", "releaseType": 1, "gameVersion": "1.11.2", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2627307}, {"filename": "FpsReducer-mc1.10.2-1.10.3.jar", "releaseType": 1, "gameVersion": "1.10.2", "gameVersionTypeId": 572, "modLoader": null, "fileId": 2627306}, {"filename": "FpsReducer-mc1.9.4-1.10.3.jar", "releaseType": 1, "gameVersion": "1.9.4", "gameVersionTypeId": 552, "modLoader": null, "fileId": 2627305}, {"filename": "FpsReducer-mc1.8.9-1.10.3.jar", "releaseType": 1, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2627304}, {"filename": "FpsReducer-mc1.7.10-1.10.3.jar", "releaseType": 1, "gameVersion": "1.7.10", "gameVersionTypeId": 5, "modLoader": null, "fileId": 2627303}, {"filename": "FpsReducer-1.12.2-1.9.jar", "releaseType": 1, "gameVersion": "1.12.1", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2527580}], "dateCreated": "2017-10-20T15:50:42.93Z", "logo": {"description": "", "id": 127648, "title": "636440915960912895.png", "modId": 280294, "url": "https://media.forgecdn.net/avatars/127/648/636440915960912895.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/127/648/256/256/636440915960912895.png"}, "links": {"sourceUrl": null, "issuesUrl": null, "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/fps-reducer", "wikiUrl": ""}, "dateReleased": "2025-10-01T13:18:13.447Z", "id": 280294, "categories": [{"gameId": 432, "classId": 6, "name": "Miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "parentCategoryId": 6, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}], "slug": "fps-reducer", "gameId": 432, "summary": "Reduce GPU and CPU usage automatically when no user operation exists.", "latestFiles": [{"gameId": 432, "fileName": "FpsReducer-mc1.13.2-1.11-alpha-1.jar", "gameVersions": ["1.13.2"], "displayName": "FpsReducer-mc1.13.2-1.11-alpha-1.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000013.0000000002", "gameVersion": "1.13.2", "gameVersionReleaseDate": "2018-10-22T00:00:00Z", "gameVersionName": "1.13.2", "gameVersionTypeId": 55023}], "downloadUrl": "https://edge.forgecdn.net/files/2686/699/FpsReducer-mc1.13.2-1.11-alpha-1.jar", "fileDate": "2019-03-13T15:18:42.010Z", "exposeAsAlternative": null, "modId": 280294, "modules": [{"name": "META-INF", "fingerprint": 2129899943}, {"name": "bre", "fingerprint": 3189448141}, {"name": "assets", "fingerprint": 2932126957}, {"name": "pack.mcmeta", "fingerprint": 386259831}], "dependencies": [], "fileFingerprint": 3994685299, "fileStatus": 4, "releaseType": 3, "hashes": [{"value": "ce537bb60dd7d49aa48b8a3fbd29dc01f2d41566", "algo": 1}, {"value": "48c8c497c63c90c35d795db48c8685af", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2686699, "fileLength": 36300, "downloadCount": 4736, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "FpsReducer-mc1.14.2-1.12.2-beta-1.jar", "gameVersions": ["1.14.2"], "displayName": "FpsReducer-mc1.14.2-1.12.2-beta-1.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000014.0000000002", "gameVersion": "1.14.2", "gameVersionReleaseDate": "2019-05-27T00:00:00Z", "gameVersionName": "1.14.2", "gameVersionTypeId": 64806}], "downloadUrl": "https://edge.forgecdn.net/files/2728/661/FpsReducer-mc1.14.2-1.12.2-beta-1.jar", "fileDate": "2019-06-23T14:47:07.767Z", "exposeAsAlternative": null, "modId": 280294, "modules": [{"name": "META-INF", "fingerprint": 3696696612}, {"name": "bre2el", "fingerprint": 1073775720}, {"name": "assets", "fingerprint": 2210304913}, {"name": "fpsreducer_logo.png", "fingerprint": 2600135182}, {"name": "pack.mcmeta", "fingerprint": 386259831}], "dependencies": [], "fileFingerprint": 669691904, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "e23af72530762b10a535320db383c6cf61e6a669", "algo": 1}, {"value": "b657eb043ce8833e63d072e1e8a5e3cd", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2728661, "fileLength": 55484, "downloadCount": 5335, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "FpsReducer-mc1.14.4-1.12.4.jar", "gameVersions": ["1.14.4", "1.14.3", "1.14.2"], "displayName": "FpsReducer-mc1.14.4-1.12.4.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000014.0000000004", "gameVersion": "1.14.4", "gameVersionReleaseDate": "2019-07-19T00:00:00Z", "gameVersionName": "1.14.4", "gameVersionTypeId": 64806}, {"gameVersionPadded": "0000000001.0000000014.0000000003", "gameVersion": "1.14.3", "gameVersionReleaseDate": "2019-06-24T00:00:00Z", "gameVersionName": "1.14.3", "gameVersionTypeId": 64806}, {"gameVersionPadded": "0000000001.0000000014.0000000002", "gameVersion": "1.14.2", "gameVersionReleaseDate": "2019-05-27T00:00:00Z", "gameVersionName": "1.14.2", "gameVersionTypeId": 64806}], "downloadUrl": "https://edge.forgecdn.net/files/2748/189/FpsReducer-mc1.14.4-1.12.4.jar", "fileDate": "2019-07-25T13:53:57.417Z", "exposeAsAlternative": null, "modId": 280294, "modules": [{"name": "META-INF", "fingerprint": 4135323288}, {"name": "bre2el", "fingerprint": 2926623893}, {"name": "assets", "fingerprint": 2210304913}, {"name": "fpsreducer_logo.png", "fingerprint": 2600135182}, {"name": "pack.mcmeta", "fingerprint": 386259831}], "dependencies": [], "fileFingerprint": 4186063656, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "242541890c5fde450623a03d1f00fe3513ca3b0d", "algo": 1}, {"value": "03e810f5f98163463b38138f8f27f7d0", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2748189, "fileLength": 55857, "downloadCount": 24130, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "FpsReducer-fabric-1.24-mc1.18-beta.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.18", "1.18-<PERSON><PERSON><PERSON>"], "displayName": "FpsReducer-fabric-1.24-mc1.18-beta.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000018", "gameVersion": "1.18", "gameVersionReleaseDate": "2021-11-30T16:23:01.427Z", "gameVersionName": "1.18", "gameVersionTypeId": 73250}, {"gameVersionPadded": "0000000001.0000000018", "gameVersion": "1.18", "gameVersionReleaseDate": "2021-08-12T00:00:00Z", "gameVersionName": "1.18-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73250}], "downloadUrl": "https://edge.forgecdn.net/files/3529/586/FpsReducer-fabric-1.24-mc1.18-beta.jar", "fileDate": "2021-11-18T14:30:57.543Z", "exposeAsAlternative": null, "modId": 280294, "modules": [{"name": "META-INF", "fingerprint": 3261462338}, {"name": "fpsreducer.json", "fingerprint": 2849542487}, {"name": "fabric.mod.json", "fingerprint": 867463428}, {"name": "assets", "fingerprint": 1761399305}, {"name": "FpsReducer-fabric-refmap.json", "fingerprint": 3439771184}, {"name": "bre2el", "fingerprint": 1345443090}], "dependencies": [{"relationType": 3, "modId": 306612}], "fileFingerprint": 1898097688, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "36d8577377bcbc2f9172fb86092a7d8b072efde5", "algo": 1}, {"value": "798ad0544eb8b28531f10d6d703bc163", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3529586, "fileLength": 83405, "downloadCount": 8797, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "FpsReducer2-forge-1.20.1-2.5.1.jar", "gameVersions": ["1.20.1", "Forge", "1.20"], "displayName": "FpsReducer2-forge-1.20.1-2.5.1.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000020.0000000001", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000020", "gameVersion": "1.20", "gameVersionReleaseDate": "2023-06-07T00:00:00Z", "gameVersionName": "1.20", "gameVersionTypeId": 75125}], "downloadUrl": "https://edge.forgecdn.net/files/6791/399/FpsReducer2-forge-1.20.1-2.5.1.jar", "fileDate": "2025-07-20T14:53:34.933Z", "exposeAsAlternative": null, "modId": 280294, "modules": [{"name": "META-INF", "fingerprint": 2004448760}, {"name": "assets", "fingerprint": 2938161472}, {"name": "bre2el", "fingerprint": 1901310781}, {"name": "fpsreducer.mixins.json", "fingerprint": 4057008972}, {"name": "fpsreducer.refmap.json", "fingerprint": 1535602947}, {"name": "fpsreducer_logo.png", "fingerprint": 2600135182}, {"name": "pack.mcmeta", "fingerprint": 3033482006}], "dependencies": [], "fileFingerprint": 4111825473, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "4920a2e1d17090007179a568181624847fba0993", "algo": 1}, {"value": "d17bad88d746c21dcd37f16257a1e84d", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6791399, "fileLength": 125660, "downloadCount": 3460, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "FpsReducer2-fabric-1.21.9-2.14.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.9", "Quilt"], "displayName": "FpsReducer2-fabric-1.21.9-2.14.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/7051/631/FpsReducer2-fabric-1.21.9-2.14.jar", "fileDate": "2025-10-01T01:11:06.167Z", "exposeAsAlternative": null, "modId": 280294, "modules": [{"name": "META-INF", "fingerprint": 3309883890}, {"name": "FpsReducer2-fabric-refmap.json", "fingerprint": 2609686533}, {"name": "assets", "fingerprint": 3947033594}, {"name": "bre2el", "fingerprint": 1871140128}, {"name": "fabric.mod.json", "fingerprint": 1675739230}, {"name": "fpsreducer.accesswidener", "fingerprint": 1895056265}, {"name": "fpsreducer.json", "fingerprint": 1295220600}], "dependencies": [{"relationType": 3, "modId": 306612}], "fileFingerprint": 1843719601, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "ee34dca5aa53a2b26b63905fcc77be279553e408", "algo": 1}, {"value": "35f936c7aed74bdcaf11da444e1f28ca", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7051631, "fileLength": 140958, "downloadCount": 364, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "FpsReducer2-neoforge-1.21.9-2.14.jar", "gameVersions": ["1.21.9", "NeoForge"], "displayName": "FpsReducer2-neoforge-1.21.9-2.14.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000021.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/7053/436/FpsReducer2-neoforge-1.21.9-2.14.jar", "fileDate": "2025-10-01T13:18:13.447Z", "exposeAsAlternative": null, "modId": 280294, "modules": [{"name": "META-INF", "fingerprint": 1418777007}, {"name": "bre2el", "fingerprint": 2647125647}, {"name": "assets", "fingerprint": 2408232185}, {"name": "fpsreducer.mixins.json", "fingerprint": 3824249399}, {"name": "fpsreducer_logo.png", "fingerprint": 2600135182}], "dependencies": [], "fileFingerprint": 2616887122, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "10d7e42c5ab52e50619f2ba02f8d9972a0cc62cd", "algo": 1}, {"value": "d477834274235440c2a594e1cd396703", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7053436, "fileLength": 156747, "downloadCount": 142, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-10-01T13:20:56.273Z", "gamePopularityRank": 520, "thumbsUpCount": 0, "name": "FPS Reducer", "mainFileId": 7053436, "primaryCategoryId": 425, "downloadCount": 53237121, "status": 4, "authors": [{"name": "bre2el", "id": 34993322, "url": "https://www.curseforge.com/members/bre2el"}], "available": false, "featured": false}