{"allowModDistribution": true, "screenshots": [{"description": "image.png", "id": 967890, "title": "image.png", "modId": 1103431, "url": "https://media.forgecdn.net/attachments/967/890/image.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/967/890/310/172/image.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "sodiumoptionsapi-neoforge-1.0.11-1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6407768}, {"filename": "sodiumoptionsapi-fabric-1.0.11-1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6407767}, {"filename": "sodiumoptionsapi-neoforge-1.0.10-1.21.1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6100815}, {"filename": "sodiumoptionsapi-fabric-1.0.10-1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6100814}, {"filename": "sodiumoptionsapi-fabric-1.0.10-1.21.1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6100813}, {"filename": "sodiumoptionsapi-forge-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6100812}, {"filename": "sodiumoptionsapi-forge-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6100812}, {"filename": "sodiumoptionsapi-fabric-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6100811}, {"filename": "sodiumoptionsapi-fabric-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6100811}, {"filename": "sodiumoptionsapi-neoforge-1.0.10-1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6100810}], "dateCreated": "2024-09-16T08:25:25.94Z", "logo": {"description": "", "id": 1080739, "title": "638620165439711976.png", "modId": 1103431, "url": "https://media.forgecdn.net/avatars/1080/739/638620165439711976.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1080/739/256/256/638620165439711976.png"}, "links": {"sourceUrl": "https://github.com/txnimc/SodiumOptionsAPI", "issuesUrl": "https://github.com/txnimc/SodiumOptionsAPI/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/sodium-options-api", "wikiUrl": ""}, "dateReleased": "2025-04-10T17:03:35.57Z", "id": 1103431, "categories": [{"gameId": 432, "classId": 6, "name": "API and Library", "dateModified": "2014-05-23T03:21:44.06Z", "parentCategoryId": 6, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}], "slug": "sodium-options-api", "gameId": 432, "summary": "Config API for adding Sodium/Embeddium options with a better categories menu", "latestFiles": [{"gameId": 432, "fileName": "sodiumoptionsapi-forge-1.0.10-1.20.1.jar", "gameVersions": ["1.20.1", "Forge", "1.20"], "displayName": "SodiumOptionsAPI Forge 1.0.10 for 1.20.1", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.20", "gameVersionReleaseDate": "2023-06-07T00:00:00Z", "gameVersionName": "1.20", "gameVersionTypeId": 75125}], "downloadUrl": "https://edge.forgecdn.net/files/6100/812/sodiumoptionsapi-forge-1.0.10-1.20.1.jar", "fileDate": "2025-01-18T20:48:18.013Z", "exposeAsAlternative": null, "modId": 1103431, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "licenses", "fingerprint": **********}, {"name": "mixins.sodiumoptionsapi.json", "fingerprint": 739035747}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "sodiumoptionsapi-forge-1.20.1-forge-refmap.json", "fingerprint": 443544756}, {"name": "toni", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 908741}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "7529fbf938787ed13a251cbf40936f8c29ec4b6c", "algo": 1}, {"value": "75807590ae2648ecdeb855705868ce59", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6100812, "fileLength": 443978, "downloadCount": 5072935, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "sodiumoptionsapi-fabric-1.0.11-1.21.5.jar", "gameVersions": ["1.21.5", "<PERSON><PERSON><PERSON>"], "displayName": "SodiumOptionsAPI Fabric 1.0.11 for 1.21.5", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6407/767/sodiumoptionsapi-fabric-1.0.11-1.21.5.jar", "fileDate": "2025-04-10T17:03:35.547Z", "exposeAsAlternative": null, "modId": 1103431, "modules": [{"name": "META-INF", "fingerprint": 926063081}, {"name": "assets", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": 231285786}, {"name": "licenses", "fingerprint": **********}, {"name": "mixins.sodiumoptionsapi.json", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "sodiumoptionsapi-fabric-1.21.5-fabric-refmap.json", "fingerprint": **********}, {"name": "sodiumoptionsapi.accesswidener", "fingerprint": **********}, {"name": "toni", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 394468}, {"relationType": 3, "modId": 306612}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "b739d04891d216831b8f4daf9e6c5b8aa584abea", "algo": 1}, {"value": "204901d0751b659dcee6dc4c5d1aeee7", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6407767, "fileLength": 312065, "downloadCount": 40452, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "sodiumoptionsapi-neoforge-1.0.11-1.21.5.jar", "gameVersions": ["1.21.5", "NeoForge"], "displayName": "SodiumOptionsAPI Neoforge 1.0.11 for 1.21.5", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6407/768/sodiumoptionsapi-neoforge-1.0.11-1.21.5.jar", "fileDate": "2025-04-10T17:03:35.570Z", "exposeAsAlternative": null, "modId": 1103431, "modules": [{"name": "META-INF", "fingerprint": 885437229}, {"name": "assets", "fingerprint": **********}, {"name": "licenses", "fingerprint": **********}, {"name": "mixins.sodiumoptionsapi.json", "fingerprint": 997596813}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "toni", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 394468}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "f5d1076bbef70786ff787e4f08673638f7baa8aa", "algo": 1}, {"value": "869f6d680567d246a2973f9b9bbbdae4", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6407768, "fileLength": 315097, "downloadCount": 7947, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-04-10T17:07:57.38Z", "gamePopularityRank": 107, "thumbsUpCount": 0, "name": "Sodium/Embeddium Options API", "mainFileId": 6407768, "primaryCategoryId": 424, "downloadCount": 28986221, "status": 4, "authors": [{"name": "Txni", "id": 102782844, "url": "https://www.curseforge.com/members/txni"}], "available": false, "featured": false}