{"allowModDistribution": true, "screenshots": [], "classId": 4471, "latestFilesIndexes": [{"filename": "Enigmatica9Expert-1.25.0.zip", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6151096}, {"filename": "Enigmatica9Expert-1.23.0.zip", "releaseType": 3, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5707817}, {"filename": "Enigmatica9Expert-1.20.0.zip", "releaseType": 2, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5195623}, {"filename": "Enigmatica9Expert-1.16.1.zip", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": null, "fileId": 4915407}], "dateCreated": "2023-07-23T08:57:15.663Z", "logo": {"description": "", "id": 844083, "title": "638240908112377505.jpeg", "modId": 882461, "url": "https://media.forgecdn.net/avatars/844/83/638240908112377505.jpeg", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/844/83/256/256/638240908112377505.jpeg"}, "links": {"sourceUrl": "https://github.com/EnigmaticaModpacks/Enigmatica9", "issuesUrl": "https://github.com/EnigmaticaModpacks/Enigmatica9/issues", "websiteUrl": "https://www.curseforge.com/minecraft/modpacks/enigmatica9expert", "wikiUrl": "https://wiki.enigmatica.net/"}, "dateReleased": "2025-06-25T20:43:47.733Z", "id": 882461, "categories": [{"gameId": 432, "classId": 4471, "name": "Quests", "dateModified": "2015-02-16T17:06:53.797Z", "parentCategoryId": 4471, "id": 4478, "iconUrl": "https://media.forgecdn.net/avatars/14/487/635596816137981263.png", "slug": "quests", "url": "https://www.curseforge.com/minecraft/modpacks/quests", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 4471, "name": "Tech", "dateModified": "2015-02-16T15:35:53.467Z", "parentCategoryId": 4471, "id": 4472, "iconUrl": "https://media.forgecdn.net/avatars/14/479/635596761534662757.png", "slug": "tech", "url": "https://www.curseforge.com/minecraft/modpacks/tech", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 4471, "name": "Multiplayer", "dateModified": "2015-02-16T16:28:03.85Z", "parentCategoryId": 4471, "id": 4484, "iconUrl": "https://media.forgecdn.net/avatars/14/481/635596792838491141.png", "slug": "multiplayer", "url": "https://www.curseforge.com/minecraft/modpacks/multiplayer", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 4471, "name": "Magic", "dateModified": "2015-02-16T15:34:17.873Z", "parentCategoryId": 4471, "id": 4473, "iconUrl": "https://media.forgecdn.net/avatars/14/474/635596760578719019.png", "slug": "magic", "url": "https://www.curseforge.com/minecraft/modpacks/magic", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 4471, "name": "Exploration", "dateModified": "2015-02-16T17:06:29.69Z", "parentCategoryId": 4471, "id": 4476, "iconUrl": "https://media.forgecdn.net/avatars/14/486/635596815896417213.png", "slug": "exploration", "url": "https://www.curseforge.com/minecraft/modpacks/exploration", "displayIndex": 0, "class": false}], "slug": "enigmatica9expert", "gameId": 432, "summary": "Explore, battle, and harness magic in Enigmatica's progression based 1.19.2 modpack, restoring life and mastering innovative tech systems.", "latestFiles": [{"gameId": 432, "fileName": "Enigmatica9Expert-1.16.1.zip", "gameVersions": ["1.19.2"], "displayName": "Enigmatica 9 Expert 1.16.1", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}], "downloadUrl": "https://edge.forgecdn.net/files/4915/407/Enigmatica9Expert-1.16.1.zip", "fileDate": "2023-11-30T21:39:23.217Z", "exposeAsAlternative": null, "modId": 882461, "modules": [{"name": "manifest.json", "fingerprint": 2832271238}], "dependencies": [], "fileFingerprint": 3006381611, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "66ed6e74de84c90022e363d80632fcf2b9d6b20f", "algo": 1}, {"value": "1df02a16ba7834b31323845ad5cb9460", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 4915407, "fileLength": 7276805, "downloadCount": 14226, "serverPackFileId": 4915408, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Enigmatica9Expert-1.20.0.zip", "gameVersions": ["1.19.2", "Forge"], "displayName": "Enigmatica 9 Expert 1.20.0", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/5195/623/Enigmatica9Expert-1.20.0.zip", "fileDate": "2024-03-19T19:20:47.660Z", "exposeAsAlternative": null, "modId": 882461, "modules": [{"name": "manifest.json", "fingerprint": 1392514844}], "dependencies": [], "fileFingerprint": 1670626195, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "01488507beb776e077d8ca3d6aa3fcadc1c0bfda", "algo": 1}, {"value": "fd69365a057fde87b7ed881346f19a1a", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5195623, "fileLength": 7244411, "downloadCount": 59, "serverPackFileId": 5195624, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Enigmatica9Expert-1.23.0.zip", "gameVersions": ["1.19.2", "Forge"], "displayName": "Enigmatica 9 Expert 1.23.0", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/5707/817/Enigmatica9Expert-1.23.0.zip", "fileDate": "2024-09-09T19:52:00.110Z", "exposeAsAlternative": null, "modId": 882461, "modules": [{"name": "manifest.json", "fingerprint": 3841110587}, {"name": "overrides", "fingerprint": 4160787636}], "dependencies": [], "fileFingerprint": 3406523015, "fileStatus": 4, "releaseType": 3, "hashes": [{"value": "3e25699a033277d08ad7b246bf50d0a4caf8117d", "algo": 1}, {"value": "5dae8d2caf20a77f5e21fcc7390a4f9f", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5707817, "fileLength": 7333363, "downloadCount": 56, "serverPackFileId": 5707818, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Enigmatica9Expert-1.25.0.zip", "gameVersions": ["1.19.2", "Forge"], "displayName": "Enigmatica 9 Expert 1.25.0", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6151/96/Enigmatica9Expert-1.25.0.zip", "fileDate": "2025-02-02T18:59:21.287Z", "exposeAsAlternative": null, "modId": 882461, "modules": [{"name": "manifest.json", "fingerprint": 1761299261}, {"name": "overrides", "fingerprint": 1430993754}], "dependencies": [], "fileFingerprint": 1565236721, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "a90128139f57b93c648e1a07024a36106b5b8b73", "algo": 1}, {"value": "c08cd92e3246b7b5ad66d3ff443345df", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6151096, "fileLength": 7335216, "downloadCount": 28778, "serverPackFileId": 6695924, "serverPack": false, "available": false}], "dateModified": "2025-06-25T20:48:53.843Z", "gamePopularityRank": 10188, "thumbsUpCount": 0, "name": "Enigmatica 9: Expert - E9E", "mainFileId": 6151096, "primaryCategoryId": 4473, "downloadCount": 185918, "status": 4, "authors": [{"name": "NillerMedDild", "id": 26760190, "url": "https://www.curseforge.com/members/nillermeddild"}], "available": false, "featured": false}