{"allowModDistribution": true, "screenshots": [{"description": "", "id": 667227, "title": "Example Map", "modId": 866084, "url": "https://media.forgecdn.net/attachments/667/227/68747470733a2f2f692e696d6775722e636f6d2f6f59596844.jpg", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/667/227/310/172/68747470733a2f2f692e696d6775722e636f6d2f6f59596844.jpg"}], "classId": 6, "latestFilesIndexes": [{"filename": "XaeroPlus-2.28.4+neoforge-1.21.9-WM1.39.16-MM25.2.14.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7054207}, {"filename": "XaeroPlus-2.28.4+fabric-1.21.9-WM1.39.16-MM25.2.14.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7054206}, {"filename": "XaeroPlus-2.28.4+fabric-1.21.9-WM1.39.16-MM25.2.14.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 7054206}, {"filename": "XaeroPlus-2.28.2+neoforge-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7021086}, {"filename": "XaeroPlus-2.28.2+forge-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7021083}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7021082}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 7021082}, {"filename": "XaeroPlus-2.28.2+neoforge-1.21.5-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7021081}, {"filename": "XaeroPlus-2.28.2+forge-1.21.5-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7021080}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.5-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7021079}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.5-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 7021079}, {"filename": "XaeroPlus-2.28.2+neoforge-1.21.8-WM1.39.14-MM25.2.12.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7021078}, {"filename": "XaeroPlus-2.28.2+forge-1.21.8-WM1.39.13-MM25.2.12.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7021077}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.8-WM1.39.13-MM25.2.12.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7021076}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.8-WM1.39.13-MM25.2.12.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 7021076}, {"filename": "XaeroPlus-2.28.2+neoforge-1.21.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7021075}, {"filename": "XaeroPlus-2.28.2+neoforge-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7021074}, {"filename": "XaeroPlus-2.28.2+forge-1.21.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7021072}, {"filename": "XaeroPlus-2.28.2+neoforge-1.20.2-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7021071}, {"filename": "XaeroPlus-2.28.2+forge-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7021070}, {"filename": "XaeroPlus-2.28.2+forge-1.20.2-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7021069}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7021068}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 7021068}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7021067}, {"filename": "XaeroPlus-2.28.2+fabric-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 7021067}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.2-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7021066}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.2-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 7021066}, {"filename": "XaeroPlus-2.28.2+neoforge-1.20.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7021065}, {"filename": "XaeroPlus-2.28.2+neoforge-1.20.6-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7021064}, {"filename": "XaeroPlus-2.28.2+forge-1.20.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7021063}, {"filename": "XaeroPlus-2.28.2+forge-1.20.6-WM1.39.12-MM25.2.12.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7021062}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7021061}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 7021061}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.6-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7021060}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.6-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 7021060}, {"filename": "XaeroPlus-2.28.2+forge-1.19.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7021056}, {"filename": "XaeroPlus-2.28.2+fabric-1.19.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 7021055}, {"filename": "XaeroPlus-2.28.2+fabric-1.19.4-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 7021055}, {"filename": "XaeroPlus-2.28.2+forge-1.19.2-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7021054}, {"filename": "XaeroPlus-2.28.2+fabric-1.19.2-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 7021053}, {"filename": "XaeroPlus-2.28.2+fabric-1.19.2-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 7021053}, {"filename": "XaeroPlus-2.28.2+forge-1.20.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7021046}, {"filename": "XaeroPlus-2.28.2+forge-1.20.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7021046}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7021045}, {"filename": "XaeroPlus-2.28.2+fabric-1.20.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 7021045}, {"filename": "XaeroPlus-2.27.6+neoforge-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6789896}, {"filename": "XaeroPlus-2.27.6+forge-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6789894}, {"filename": "XaeroPlus-2.27.6+fabric-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6789892}, {"filename": "XaeroPlus-2.27.6+fabric-1.21.3-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6789892}, {"filename": "XaeroPlus-2.27.6+neoforge-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6789890}, {"filename": "XaeroPlus-2.27.6+forge-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6789889}, {"filename": "XaeroPlus-2.27.6+fabric-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6789887}, {"filename": "XaeroPlus-2.27.6+fabric-1.21.1-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6789887}, {"filename": "XaeroPlus-Forge-1.12.2-179-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 6789860}, {"filename": "XaeroPlus-2.27.5+neoforge-1.21.7-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6737755}, {"filename": "XaeroPlus-2.27.5+forge-1.21.7-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6737754}, {"filename": "XaeroPlus-2.27.5+fabric-1.21.7-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6737753}, {"filename": "XaeroPlus-2.27.5+fabric-1.21.7-WM1.39.12-MM25.2.10.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6737753}, {"filename": "XaeroPlus-2.27.3+neoforge-1.21.7-WM1.39.11-MM25.2.8.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6731189}, {"filename": "XaeroPlus-2.27.3+fabric-1.21.7-WM1.39.10-MM25.2.7.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6731187}, {"filename": "XaeroPlus-2.27.3+fabric-1.21.7-WM1.39.10-MM25.2.7.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6731187}, {"filename": "XaeroPlus-2.9+neoforge-1.20.5-WM1.38.5-MM24.1.2.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5291819}, {"filename": "XaeroPlus-2.9+fabric-1.20.5-WM1.38.5-MM24.1.2.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5291817}, {"filename": "XaeroPlus-2.9+fabric-1.20.5-WM1.38.5-MM24.1.2.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5291817}, {"filename": "XaeroPlus-Fabric-1.20.4-15-WM1.37.8-MM23.9.7.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5150139}, {"filename": "XaeroPlus-Fabric-1.20.4-15-WM1.37.8-MM23.9.7.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5150139}, {"filename": "XaeroPlus-Forge-1.20.4-14-WM1.37.8-MM23.9.7.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5150138}, {"filename": "XaeroPlus-NeoForge-1.20.4-3-WM1.38.0-MM24.0.0.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5150136}], "dateCreated": "2023-05-28T09:02:01.413Z", "logo": {"description": "", "id": 822958, "title": "638206522373034391.jpeg", "modId": 866084, "url": "https://media.forgecdn.net/avatars/822/958/638206522373034391.jpeg", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/822/958/256/256/638206522373034391.jpeg"}, "links": {"sourceUrl": "https://github.com/rfresh2/XaeroPlus", "issuesUrl": "https://github.com/rfresh2/XaeroPlus/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/xaeroplus", "wikiUrl": ""}, "dateReleased": "2025-10-01T16:30:05.463Z", "id": 866084, "categories": [{"gameId": 432, "classId": 6, "name": "Map and Information", "dateModified": "2014-05-08T17:42:23.74Z", "parentCategoryId": 6, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Player Transport", "dateModified": "2014-05-08T17:38:58.357Z", "parentCategoryId": 412, "id": 414, "iconUrl": "https://media.forgecdn.net/avatars/6/29/635351495383551178.png", "slug": "technology-player-transport", "url": "https://www.curseforge.com/minecraft/mc-mods/technology/technology-player-transport", "displayIndex": 0, "class": false}], "slug": "x<PERSON><PERSON><PERSON>", "gameId": 432, "summary": "Xaero WorldMap / Minimap Extra Features", "latestFiles": [{"gameId": 432, "fileName": "XaeroPlus-2.28.2+forge-1.21.3-WM1.39.12-MM25.2.10.jar", "gameVersions": ["NeoForge", "Forge", "1.21.3"], "displayName": "2.28.2 (<PERSON>ge) (WM1.39.12 MM25.2.10)", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000003", "gameVersion": "1.21.3", "gameVersionReleaseDate": "2024-10-23T13:16:45.71Z", "gameVersionName": "1.21.3", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/7021/83/XaeroPlus-2.28.2%2bforge-1.21.3-WM1.39.12-MM25.2.10.jar", "fileDate": "2025-09-22T01:26:07.543Z", "exposeAsAlternative": null, "modId": 866084, "modules": [{"name": "META-INF", "fingerprint": 1914502906}, {"name": "assets", "fingerprint": 2127475301}, {"name": "org", "fingerprint": *********}, {"name": "pack.mcmeta", "fingerprint": 3609686347}, {"name": "sqlite-jdbc.properties", "fingerprint": 2453815297}, {"name": "xaeroplus-forge.mixins.json", "fingerprint": 1901959047}, {"name": "xaeroplus.mixins.json", "fingerprint": 1972980041}, {"name": "x<PERSON><PERSON><PERSON>", "fingerprint": 771540871}], "dependencies": [{"relationType": 3, "modId": 317780}, {"relationType": 2, "modId": 909868}, {"relationType": 3, "modId": 263420}, {"relationType": 2, "modId": 245755}], "fileFingerprint": 409218411, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "5663cd903352bafea3142805e742e915b2a1877a", "algo": 1}, {"value": "469ce9d779e0a775b1dddd5d9d359513", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7021083, "fileLength": 7926739, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "XaeroPlus-2.28.4+fabric-1.21.9-WM1.39.16-MM25.2.14.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.9", "Quilt"], "displayName": "2.28.4 (<PERSON><PERSON><PERSON>) (WM MM25.2.14)", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/7054/206/XaeroPlus-2.28.4%2bfabric-1.21.9-WM1.39.16-MM25.2.14.jar", "fileDate": "2025-10-01T16:29:56.393Z", "exposeAsAlternative": null, "modId": 866084, "modules": [{"name": "META-INF", "fingerprint": 2501083859}, {"name": "XaeroPlus-common-refmap.json", "fingerprint": *********}, {"name": "XaeroPlus-fabric-refmap.json", "fingerprint": *********}, {"name": "architectury_inject_XaeroPlus_common_d53765b7a42d42759a2c25cec3106175_e93e02908b9affe73006ab5d050d1b6cf6a1a54a0c8ee9b9171c4fa12705046cXaeroPlus2284devjar", "fingerprint": 2385954450}, {"name": "assets", "fingerprint": 1855414430}, {"name": "com", "fingerprint": *********}, {"name": "fabric.mod.json", "fingerprint": *********}, {"name": "kaptainwutax", "fingerprint": 2014782872}, {"name": "net", "fingerprint": 2046507328}, {"name": "org", "fingerprint": *********}, {"name": "sqlite-jdbc.properties", "fingerprint": 2453815297}, {"name": "xaeroplus-fabric.mixins.json", "fingerprint": 2755271738}, {"name": "xaeroplus.accesswidener", "fingerprint": *********}, {"name": "xaeroplus.mixins.json", "fingerprint": 3365170374}, {"name": "x<PERSON><PERSON><PERSON>", "fingerprint": 3299976912}], "dependencies": [{"relationType": 2, "modId": 909868}, {"relationType": 3, "modId": 317780}, {"relationType": 3, "modId": 263420}, {"relationType": 3, "modId": 306612}, {"relationType": 2, "modId": 245755}, {"relationType": 2, "modId": 410902}], "fileFingerprint": 2469350675, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "9710e8562ff2e819d76931eac8a78a29711ce609", "algo": 1}, {"value": "0827fc26298272f1744ee8f0f3f7750f", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7054206, "fileLength": 7758153, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "XaeroPlus-2.28.4+neoforge-1.21.9-WM1.39.16-MM25.2.14.jar", "gameVersions": ["1.21.9", "NeoForge"], "displayName": "2.28.4 (<PERSON><PERSON><PERSON><PERSON>) (WM1.39.16 MM25.2.14)", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000021.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/7054/207/XaeroPlus-2.28.4%2bneoforge-1.21.9-WM1.39.16-MM25.2.14.jar", "fileDate": "2025-10-01T16:30:05.463Z", "exposeAsAlternative": null, "modId": 866084, "modules": [{"name": "META-INF", "fingerprint": 4068391646}, {"name": "assets", "fingerprint": 1855414430}, {"name": "org", "fingerprint": *********}, {"name": "pack.mcmeta", "fingerprint": 3609686347}, {"name": "sqlite-jdbc.properties", "fingerprint": 2453815297}, {"name": "xaeroplus-neo.mixins.json", "fingerprint": 1713712974}, {"name": "xaeroplus.mixins.json", "fingerprint": 3696102226}, {"name": "x<PERSON><PERSON><PERSON>", "fingerprint": 1328030368}], "dependencies": [{"relationType": 2, "modId": 909868}, {"relationType": 2, "modId": 245755}, {"relationType": 3, "modId": 263420}, {"relationType": 3, "modId": 317780}], "fileFingerprint": 2745700206, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "9faf63bd2ece4020c68fad46086196a5d99c090a", "algo": 1}, {"value": "74f539cb01be99bc51b4a12f9a594b65", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7054207, "fileLength": 7746278, "downloadCount": 88, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-10-01T16:32:55.5Z", "gamePopularityRank": 847, "thumbsUpCount": 0, "name": "XaeroPlus", "mainFileId": 7054207, "primaryCategoryId": 423, "downloadCount": 15658485, "status": 4, "authors": [{"name": "CoinRS", "id": 103613768, "url": "https://www.curseforge.com/members/coinrs"}], "available": false, "featured": false}