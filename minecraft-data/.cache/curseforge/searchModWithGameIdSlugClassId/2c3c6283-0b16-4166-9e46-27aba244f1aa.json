{"allowModDistribution": true, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "ItemPhysicLite_NEOFORGE_v1.6.10_mc1.21.9.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7057670}, {"filename": "ItemPhysicLite_FABRIC_v1.6.10_mc1.21.8.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6850389}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.9_mc1.21.8.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6850322}, {"filename": "ItemPhysicLite_FABRIC_v1.6.9_mc1.21.7.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6743910}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.9_mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6700958}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.9_mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6450974}, {"filename": "ItemPhysicLite_FABRIC_v1.6.9_mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6450968}, {"filename": "ItemPhysicLite_FABRIC_v1.6.8_mc1.21.1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6374730}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.8_mc1.21.1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6374702}, {"filename": "ItemPhysicLite_FABRIC_v1.6.7_mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6119343}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.7_mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6119337}, {"filename": "ItemPhysicLite_FABRIC_v1.6.6_mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6045999}, {"filename": "ItemPhysicLite_FORGE_v1.6.6_mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6045994}, {"filename": "ItemPhysicLite_FORGE_v1.6.6_mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6045994}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.5_mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5516289}, {"filename": "ItemPhysicLite_FABRIC_v1.6.5_mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5516269}, {"filename": "ItemPhysicLite_FORGE_v1.5.3_mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5491933}, {"filename": "ItemPhysicLite_FORGE_v1.5.3_mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 6, "fileId": 5491933}, {"filename": "ItemPhysicLite_FORGE_v1.5.1_mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 5335410}, {"filename": "ItemPhysicLite_FORGE_v1.5.1_mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 6, "fileId": 5335410}, {"filename": "ItemPhysicLite_FABRIC_v1.6.4_mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5323476}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.4_mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5312789}, {"filename": "ItemPhysicLite_FABRIC_v1.6.4_mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5281988}, {"filename": "ItemPhysicLite_NEOFORGE_v1.6.4_mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5281655}, {"filename": "ItemPhysicLite_FABRIC_v1.6.3_mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4779234}, {"filename": "ItemPhysicLite_FABRIC_v1.6.2_mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4605672}, {"filename": "ItemPhysicLite_FORGE_v1.6.1_mc1.20.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4578892}, {"filename": "ItemPhysicLite_FORGE_v1.6.1_mc1.20.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4578892}, {"filename": "ItemPhysicLite_FORGE_v1.6.1_mc1.19.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4438543}, {"filename": "ItemPhysicLite_FABRIC_v1.6.1_mc1.19.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4438535}, {"filename": "ItemPhysicLite_FORGE_v1.6.1_mc1.19.3.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4369245}, {"filename": "ItemPhysicLite_FABRIC_v1.6.0_mc1.19.3.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4210872}, {"filename": "ItemPhysicLite_FABRIC_v1.5.2_mc1.19.1.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3905692}, {"filename": "ItemPhysicLite_FABRIC_v1.5.2_mc1.19.1.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3905692}, {"filename": "ItemPhysicLite_FABRIC_v1.5.2_mc1.19.1.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 3905692}, {"filename": "ItemPhysicLite_FORGE_v1.5.2_mc1.19.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3898841}, {"filename": "ItemPhysicLite_FORGE_v1.5.2_mc1.19.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3898841}, {"filename": "ItemPhysicLite_FABRIC_v1.5.0_mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 3827355}, {"filename": "ItemPhysicLite_v1.4.8_mc1.18.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3543951}, {"filename": "ItemPhysicLite_v1.4.8_mc1.18.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3543951}, {"filename": "ItemPhysicLite_v1.4.8_mc1.17.1.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": null, "fileId": 3412811}, {"filename": "ItemPhysicLite_v1.4.8_mc1.16.5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3285730}, {"filename": "ItemPhysicLite_v1.4.7_mc1.16.4.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3146163}, {"filename": "ItemPhysicLite_v1.4.7_mc1.16.4.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3146163}, {"filename": "ItemPhysicLite_v1.4.7_mc1.16.4.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3146163}, {"filename": "ItemPhysicLite_v1.4.1_mc1.15.2.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": null, "fileId": 2914011}, {"filename": "ItemPhysic Lite 1.3.7 mc1.12.2.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2491098}, {"filename": "ItemPhysic Lite 1.3.7 mc1.12.1.jar", "releaseType": 1, "gameVersion": "1.12.1", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2467397}, {"filename": "ItemPhysic Lite 1.1.5 mc1.7.10.jar", "releaseType": 1, "gameVersion": "1.7.10", "gameVersionTypeId": 5, "modLoader": null, "fileId": 2462371}, {"filename": "ItemPhysic Lite 1.3.7 mc1.12.jar", "releaseType": 1, "gameVersion": "1.12", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2448485}, {"filename": "ItemPhysic Lite 1.3.7 mc1.11.2.jar", "releaseType": 1, "gameVersion": "1.11.2", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2448482}, {"filename": "ItemPhysic Lite 1.3.6 mc1.10.2.jar", "releaseType": 1, "gameVersion": "1.10.2", "gameVersionTypeId": 572, "modLoader": null, "fileId": 2439697}, {"filename": "ItemPhysic Lite 1.3 mc1.9.4.jar", "releaseType": 1, "gameVersion": "1.9.4", "gameVersionTypeId": 552, "modLoader": null, "fileId": 2439696}, {"filename": "ItemPhysic Lite 1.3 mc1.8.9.jar", "releaseType": 1, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2439695}], "dateCreated": "2017-06-27T23:34:13.093Z", "logo": {"description": "", "id": 103313, "title": "636337267977446572.png", "modId": 270441, "url": "https://media.forgecdn.net/avatars/103/313/636337267977446572.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/103/313/256/256/636337267977446572.png"}, "links": {"sourceUrl": "https://github.com/CreativeMD/ItemPhysicLite", "issuesUrl": "https://github.com/CreativeMD/ItemPhysicLite/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/itemphysic-lite", "wikiUrl": ""}, "dateReleased": "2025-10-02T13:19:18.763Z", "id": 270441, "categories": [{"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}], "slug": "itemphysic-lite", "gameId": 432, "summary": "#spinning", "latestFiles": [{"gameId": 432, "fileName": "ItemPhysicLite_v1.4.8_mc1.17.1.jar", "gameVersions": ["1.17.1"], "displayName": "ItemPhysicLite_v1.4.8_mc1.17.1.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000017.0000000001", "gameVersion": "1.17.1", "gameVersionReleaseDate": "2021-07-06T14:16:03.97Z", "gameVersionName": "1.17.1", "gameVersionTypeId": 73242}], "downloadUrl": "https://edge.forgecdn.net/files/3412/811/ItemPhysicLite_v1.4.8_mc1.17.1.jar", "fileDate": "2021-08-04T13:28:40.220Z", "exposeAsAlternative": null, "modId": 270441, "modules": [{"name": "itemphysic.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": 553130841}, {"name": "META-INF", "fingerprint": 3197001312}, {"name": "team", "fingerprint": 3562091840}], "dependencies": [], "fileFingerprint": 614410070, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "beca5881089bc277d3b1e98ffd05fbd9f8c68e9b", "algo": 1}, {"value": "441795d93e3eeb11236ace4dd17f16da", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3412811, "fileLength": 16783, "downloadCount": 15740, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ItemPhysicLite_FORGE_v1.6.6_mc1.20.1.jar", "gameVersions": ["NeoForge", "1.20.1", "Forge"], "displayName": "ItemPhysicLite_FORGE_v1.6.6_mc1.20.1.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000020.0000000001", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6045/994/ItemPhysicLite_FORGE_v1.6.6_mc1.20.1.jar", "fileDate": "2025-01-02T15:19:28.153Z", "exposeAsAlternative": null, "modId": 270441, "modules": [{"name": "META-INF", "fingerprint": 1826355383}, {"name": "fabric.mod.json", "fingerprint": 324347720}, {"name": "itemphysic.png", "fingerprint": **********}, {"name": "itemphysiclite.mixin.refmap.json", "fingerprint": 628011937}, {"name": "itemphysiclite.mixins.json", "fingerprint": 1054365591}, {"name": "pack.mcmeta", "fingerprint": 3213796469}, {"name": "team", "fingerprint": 3420225419}], "dependencies": [{"relationType": 3, "modId": 257814}], "fileFingerprint": 1934443491, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "4fc87af53afb8610e77710b30340783fc6bd0ccf", "algo": 1}, {"value": "83c19033092e5eab8c00e0ab056a783a", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6045994, "fileLength": 19487, "downloadCount": 1342466, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ItemPhysicLite_FABRIC_v1.6.10_mc1.21.8.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.8", "Client", "Server"], "displayName": "ItemPhysicLite_FABRIC_v1.6.10_mc1.21.8.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000008", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Server", "gameVersionTypeId": 75208}], "downloadUrl": "https://edge.forgecdn.net/files/6850/389/ItemPhysicLite_FABRIC_v1.6.10_mc1.21.8.jar", "fileDate": "2025-08-05T10:52:23.177Z", "exposeAsAlternative": null, "modId": 270441, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "itemphysic.png", "fingerprint": **********}, {"name": "itemphysiclite.mixin.refmap.json", "fingerprint": 481938485}, {"name": "itemphysiclite.mixins.json", "fingerprint": 287657556}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "team", "fingerprint": 96769767}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "3fae56cf239964446a1b57c94755a692f2a53de0", "algo": 1}, {"value": "e2fe29cc3b8673947a669d25e1cf4003", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6850389, "fileLength": 26720, "downloadCount": 70, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "ItemPhysicLite_NEOFORGE_v1.6.10_mc1.21.9.jar", "gameVersions": ["Client", "1.21.9", "NeoForge", "Server"], "displayName": "ItemPhysicLite_NEOFORGE_v1.6.10_mc1.21.9.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0000000001.0000000021.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Server", "gameVersionTypeId": 75208}], "downloadUrl": "https://edge.forgecdn.net/files/7057/670/ItemPhysicLite_NEOFORGE_v1.6.10_mc1.21.9.jar", "fileDate": "2025-10-02T13:19:18.097Z", "exposeAsAlternative": null, "modId": 270441, "modules": [{"name": "META-INF", "fingerprint": 3536228515}, {"name": "team", "fingerprint": 3456824536}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "itemphysic.png", "fingerprint": **********}, {"name": "itemphysiclite.mixins.json", "fingerprint": 287657556}, {"name": "pack.mcmeta", "fingerprint": 4154101537}], "dependencies": [{"relationType": 3, "modId": 257814}], "fileFingerprint": 2797147099, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "8df5a0d33e8db2a55ad9990b34fb52568ca9f3d8", "algo": 1}, {"value": "f268af38d5cb6bf15560942490fa69e0", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7057670, "fileLength": 23160, "downloadCount": 24, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-10-02T13:21:58.23Z", "gamePopularityRank": 980, "thumbsUpCount": 0, "name": "ItemPhysic Lite", "mainFileId": 7057670, "primaryCategoryId": 424, "downloadCount": 19435839, "status": 4, "authors": [{"name": "CreativeMD", "id": 8685730, "url": "https://www.curseforge.com/members/creativemd"}], "available": false, "featured": false}