{"allowModDistribution": true, "screenshots": [{"description": "Playing survival mode with Controllable", "id": 1091550, "title": "Survival Mode", "modId": 317269, "url": "https://media.forgecdn.net/attachments/1091/550/processed_image-png.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1091/550/310/172/processed_image-png.png"}, {"description": "Easily navigate and interact with items with slot snapping and DPAD navigation", "id": 1091554, "title": "Player Inventory", "modId": 317269, "url": "https://media.forgecdn.net/attachments/1091/554/processed_image-png.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1091/554/310/172/processed_image-png.png"}, {"description": "Select one or more controllers. Selecting more than one will create a linked controller.", "id": 1091553, "title": "Select Controller", "modId": 317269, "url": "https://media.forgecdn.net/attachments/1091/553/processed_image-png.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1091/553/310/172/processed_image-png.png"}, {"description": "Configure the mod with controller friendly interactions", "id": 1091551, "title": "<PERSON><PERSON><PERSON>", "modId": 317269, "url": "https://media.forgecdn.net/attachments/1091/551/processed_image-png.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1091/551/310/172/processed_image-png.png"}, {"description": "Remap any button to create your own comfortable layout", "id": 1091552, "title": "Re<PERSON><PERSON>", "modId": 317269, "url": "https://media.forgecdn.net/attachments/1091/552/processed_image-png.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1091/552/310/172/processed_image-png.png"}, {"description": "", "id": 621619, "title": "Documentation Toast", "modId": 317269, "url": "https://media.forgecdn.net/attachments/621/619/documentationtoast.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/621/619/310/172/documentationtoast.png"}, {"description": "", "id": 670633, "title": "Banner", "modId": 317269, "url": "https://media.forgecdn.net/attachments/670/633/controllable_banner.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/670/633/310/172/controllable_banner.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "controllable-neoforge-1.21.1-0.25.3.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6850105}, {"filename": "controllable-fabric-1.21.1-0.25.3.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6850103}, {"filename": "controllable-neoforge-1.21.8-0.25.3.jar", "releaseType": 2, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6850090}, {"filename": "controllable-fabric-1.21.8-0.25.3.jar", "releaseType": 2, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6850088}, {"filename": "controllable-neoforge-1.21.7-0.25.2.jar", "releaseType": 2, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6715876}, {"filename": "controllable-fabric-1.21.7-0.25.2.jar", "releaseType": 2, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6715873}, {"filename": "controllable-neoforge-1.21.6-0.25.2.jar", "releaseType": 2, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6685374}, {"filename": "controllable-fabric-1.21.6-0.25.2.jar", "releaseType": 2, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6685372}, {"filename": "controllable-neoforge-1.21.5-0.24.0.jar", "releaseType": 2, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6413243}, {"filename": "controllable-fabric-1.21.5-0.24.0.jar", "releaseType": 2, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6413241}, {"filename": "controllable-neoforge-1.21.4-0.23.3.jar", "releaseType": 2, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6277391}, {"filename": "controllable-fabric-1.21.4-0.23.3.jar", "releaseType": 2, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6277389}, {"filename": "controllable-neoforge-1.20.4-0.21.7.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6171432}, {"filename": "controllable-forge-1.20.4-0.21.7.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6171430}, {"filename": "controllable-fabric-1.20.4-0.21.7.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6171425}, {"filename": "controllable-forge-1.20.1-0.21.7.jar", "releaseType": 2, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6171424}, {"filename": "controllable-fabric-1.20.1-0.21.7.jar", "releaseType": 2, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6171420}, {"filename": "controllable-0.19.3-1.19.4.jar", "releaseType": 2, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4511169}, {"filename": "controllable-0.18.0-1.19.2.jar", "releaseType": 2, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4445137}, {"filename": "controllable-0.17.0-1.19.3.jar", "releaseType": 2, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4376757}, {"filename": "controllable-0.17.0-1.18.2.jar", "releaseType": 2, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 4376751}, {"filename": "controllable-0.16.5-1.16.5.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3937166}, {"filename": "controllable-0.16.4-1.19.1.jar", "releaseType": 2, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3907445}, {"filename": "controllable-0.16.3-1.19.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 3873303}, {"filename": "controllable-0.15.1-1.18.jar", "releaseType": 2, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3546297}, {"filename": "controllable-0.15.0-1.17.1.jar", "releaseType": 2, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 3528563}, {"filename": "controllable-0.13.3-1.16.3.jar", "releaseType": 2, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3335987}, {"filename": "controllable-0.13.3-1.16.3.jar", "releaseType": 2, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3335987}, {"filename": "controllable-0.11.2-1.12.2.jar", "releaseType": 2, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 3222475}, {"filename": "controllable-0.11.2-1.15.2.jar", "releaseType": 2, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 3209217}, {"filename": "controllable-1.16.1-0.8.1.jar", "releaseType": 2, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3008760}, {"filename": "controllable-1.15.2-0.8.0.jar", "releaseType": 2, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 2880167}, {"filename": "controllable-1.14.4-0.8.0.jar", "releaseType": 2, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 2880165}, {"filename": "controllable-1.15-0.7.1.jar", "releaseType": 2, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 2849608}, {"filename": "controllable-1.12.2-0.3.1.jar", "releaseType": 2, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2693365}], "dateCreated": "2019-03-13T15:02:32.56Z", "logo": {"description": "", "id": 871319, "title": "638288811093619347.png", "modId": 317269, "url": "https://media.forgecdn.net/avatars/871/319/638288811093619347.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/871/319/256/256/638288811093619347.png"}, "links": {"sourceUrl": "https://github.com/MrCrayfish/Controllable", "issuesUrl": "https://github.com/MrCrayfish/Controllable/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/controllable", "wikiUrl": ""}, "dateReleased": "2025-08-05T09:11:35.33Z", "id": 317269, "categories": [{"gameId": 432, "classId": 6, "name": "API and Library", "dateModified": "2014-05-23T03:21:44.06Z", "parentCategoryId": 6, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}], "slug": "controllable", "gameId": 432, "summary": "Play Minecraft Java Edition with a Controller / Gamepad", "latestFiles": [{"gameId": 432, "fileName": "controllable-1.12.2-0.3.1.jar", "gameVersions": ["1.12.2"], "displayName": "Controllable 0.3.1", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000012.0000000002", "gameVersion": "1.12.2", "gameVersionReleaseDate": "2017-09-18T05:00:00Z", "gameVersionName": "1.12.2", "gameVersionTypeId": 628}], "downloadUrl": "https://edge.forgecdn.net/files/2693/365/controllable-1.12.2-0.3.1.jar", "fileDate": "2019-03-30T16:01:13.830Z", "exposeAsAlternative": null, "modId": 317269, "modules": [{"name": "META-INF", "fingerprint": 570334891}, {"name": "assets", "fingerprint": 590796813}, {"name": "com", "fingerprint": 611942253}, {"name": "gamecontrollerdb.txt", "fingerprint": 948496238}, {"name": "jamepad.dll", "fingerprint": 4122620921}, {"name": "jamepad64.dll", "fingerprint": 1950886261}, {"name": "libjamepad64.dylib", "fingerprint": 2471036701}, {"name": "libjamepad64.so", "fingerprint": 1080089241}, {"name": "libjamepadArm.so", "fingerprint": 3578832415}, {"name": "mappings", "fingerprint": 434841338}, {"name": "mcmod.info", "fingerprint": 1599515833}, {"name": "pack.mcmeta", "fingerprint": 3685809640}], "dependencies": [], "fileFingerprint": 3670586974, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "1a45705f8ab45f7cc62ca81ec69492da4a36f2d8", "algo": 1}, {"value": "fae2be4dbfad21683181f82167d34702", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2693365, "fileLength": 9480379, "downloadCount": 44483, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "controllable-forge-1.20.4-0.21.7.jar", "gameVersions": ["Forge", "1.20.4"], "displayName": "Controllable 0.21.7", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.20.4", "gameVersionReleaseDate": "2023-12-07T15:17:47.907Z", "gameVersionName": "1.20.4", "gameVersionTypeId": 75125}], "downloadUrl": "https://edge.forgecdn.net/files/6171/430/controllable-forge-1.20.4-0.21.7.jar", "fileDate": "2025-02-08T15:20:10.620Z", "exposeAsAlternative": null, "modId": 317269, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE_Controllable", "fingerprint": **********}, {"name": "LICENSE_ControllableSDL", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "controllable.common.mixins.json", "fingerprint": **********}, {"name": "controllable.forge.mixins.json", "fingerprint": **********}, {"name": "controllable.refmap.json", "fingerprint": **********}, {"name": "controllable_background.png", "fingerprint": **********}, {"name": "controllable_banner.png", "fingerprint": 677197258}, {"name": "controllable_icon.png", "fingerprint": **********}, {"name": "gamecontrollerdb.txt", "fingerprint": **********}, {"name": "mappings", "fingerprint": 434841338}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 549225}, {"relationType": 2, "modId": 459701}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "0bd937fc525df8307a5453eae2df6f6d83e93a85", "algo": 1}, {"value": "b8a429c251dd0c186b10078d48b22242", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6171430, "fileLength": 7567767, "downloadCount": 9820, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "controllable-fabric-1.21.1-0.25.3.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1"], "displayName": "Controllable 0.25.3", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6850/103/controllable-fabric-1.21.1-0.25.3.jar", "fileDate": "2025-08-05T09:11:10.397Z", "exposeAsAlternative": null, "modId": 317269, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE_Controllable", "fingerprint": **********}, {"name": "LICENSE_ControllableSDL", "fingerprint": **********}, {"name": "LICENSE_GameControllerDB", "fingerprint": 912473422}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "controllable.accesswidener", "fingerprint": **********}, {"name": "controllable.common.mixins.json", "fingerprint": **********}, {"name": "controllable.fabric.mixins.json", "fingerprint": **********}, {"name": "controllable.refmap.json", "fingerprint": 400477393}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "gamecontrollerdb.txt", "fingerprint": **********}, {"name": "mappings", "fingerprint": 434841338}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 549225}, {"relationType": 2, "modId": 459701}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "f08c8e1b8421b1f3206a01259bcffb47cb983fbd", "algo": 1}, {"value": "1bc1b0e7cc346e5576fba634c2fab014", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6850103, "fileLength": 7146243, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "controllable-neoforge-1.21.1-0.25.3.jar", "gameVersions": ["NeoForge", "1.21.1"], "displayName": "Controllable 0.25.3", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6850/105/controllable-neoforge-1.21.1-0.25.3.jar", "fileDate": "2025-08-05T09:11:35.330Z", "exposeAsAlternative": null, "modId": 317269, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE_Controllable", "fingerprint": **********}, {"name": "LICENSE_ControllableSDL", "fingerprint": **********}, {"name": "LICENSE_GameControllerDB", "fingerprint": 912473422}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "controllable.common.mixins.json", "fingerprint": **********}, {"name": "controllable.neoforge.mixins.json", "fingerprint": **********}, {"name": "gamecontrollerdb.txt", "fingerprint": **********}, {"name": "mappings", "fingerprint": 434841338}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [{"relationType": 2, "modId": 459701}, {"relationType": 3, "modId": 549225}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "2965f403c98d797e786bbd58ffebc3e936b18e21", "algo": 1}, {"value": "d612c1001504cef2210cdfe8a3352f46", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6850105, "fileLength": 7140617, "downloadCount": 711, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-08-05T09:18:35.677Z", "gamePopularityRank": 1014, "thumbsUpCount": 0, "name": "Controllable", "mainFileId": 6850105, "primaryCategoryId": 5191, "downloadCount": 13014810, "status": 4, "authors": [{"name": "MrCrayfish", "id": 7244595, "url": "https://www.curseforge.com/members/mrcrayfish"}], "available": false, "featured": false}