{"allowModDistribution": true, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "sodiumdynamiclights-fabric-1.0.10-1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6408534}, {"filename": "sodiumdynamiclights-neoforge-1.0.10-1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6408533}, {"filename": "sodiumdynamiclights-fabric-1.0.10-1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6044482}, {"filename": "sodiumdynamiclights-forge-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6044481}, {"filename": "sodiumdynamiclights-forge-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6044481}, {"filename": "sodiumdynamiclights-fabric-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6044480}, {"filename": "sodiumdynamiclights-fabric-1.0.10-1.20.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6044480}, {"filename": "sodiumdynamiclights-neoforge-1.0.10-1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6044479}, {"filename": "sodiumdynamiclights-fabric-1.0.10-1.21.1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6044478}, {"filename": "sodiumdynamiclights-neoforge-1.0.10-1.21.1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6044477}, {"filename": "sodiumdynamiclights-fabric-1.0.3-1.21.1.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 5701737}, {"filename": "dynamiclightsreforged-1.20.1_v1.6.0.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4731947}, {"filename": "dynamiclightsreforged-1.19.2_v1.4.0.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4285414}, {"filename": "dynamiclightsreforged-1.18.2_v1.3.3.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3817266}, {"filename": "dynamiclightsreforged-mc1.16.5_v1.0.1.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3574974}], "dateCreated": "2021-12-19T08:51:23.243Z", "logo": {"description": "", "id": 1068309, "title": "638602793082566240.png", "modId": 551736, "url": "https://media.forgecdn.net/avatars/1068/309/638602793082566240.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1068/309/256/256/638602793082566240.png"}, "links": {"sourceUrl": "https://github.com/txnimc/SodiumDynamicLights", "issuesUrl": "https://github.com/txnimc/SodiumDynamicLights/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/dynamiclights-reforged", "wikiUrl": ""}, "dateReleased": "2025-04-10T21:19:54.253Z", "id": 551736, "categories": [{"gameId": 432, "classId": 6, "name": "Performance", "dateModified": "2024-01-16T06:56:01.057Z", "parentCategoryId": 6, "id": 6814, "iconUrl": "https://media.forgecdn.net/avatars/933/987/638409849610531091.png", "slug": "performance", "url": "https://www.curseforge.com/minecraft/mc-mods/performance", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}], "slug": "dynamiclights-reforged", "gameId": 432, "summary": "Multiloader port of LambDynLights that adds Sodium/Embeddium settings support", "latestFiles": [{"gameId": 432, "fileName": "sodiumdynamiclights-forge-1.0.10-1.20.1.jar", "gameVersions": ["1.20.1", "Forge", "1.20"], "displayName": "SodiumDynamicLights Forge 1.0.10 for 1.20.1", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.20", "gameVersionReleaseDate": "2023-06-07T00:00:00Z", "gameVersionName": "1.20", "gameVersionTypeId": 75125}], "downloadUrl": "https://edge.forgecdn.net/files/6044/481/sodiumdynamiclights-forge-1.0.10-1.20.1.jar", "fileDate": "2025-01-02T01:22:43.027Z", "exposeAsAlternative": null, "modId": 551736, "modules": [{"name": "META-INF", "fingerprint": 108037934}, {"name": "LambDynamicLights.LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "mixins.sodiumdynamiclights.json", "fingerprint": 887425713}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "sodiumdynamiclights-forge-1.20.1-forge-refmap.json", "fingerprint": **********}, {"name": "sodiumdynamiclights.accesswidener", "fingerprint": **********}, {"name": "sodiumdynamiclights.toml", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.21.1.accesswidener", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.21.4.accesswidener", "fingerprint": **********}, {"name": "toni", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 1103431}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "d27524e85bed0f0af83c03be46f9ca3eb02a1be9", "algo": 1}, {"value": "687c18cefa558ca8101ef97914d9ba6b", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6044481, "fileLength": 511601, "downloadCount": 5978144, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "sodiumdynamiclights-neoforge-1.0.10-1.21.5.jar", "gameVersions": ["1.21.5", "NeoForge"], "displayName": "SodiumDynamicLights Neoforge 1.0.10 for 1.21.5", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000021.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6408/533/sodiumdynamiclights-neoforge-1.0.10-1.21.5.jar", "fileDate": "2025-04-10T21:19:54.090Z", "exposeAsAlternative": null, "modId": 551736, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LambDynamicLights.LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "mixins.sodiumdynamiclights.json", "fingerprint": 77541815}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "sodiumdynamiclights.toml", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.20.1.accesswidener", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.21.1.accesswidener", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.21.4.accesswidener", "fingerprint": **********}, {"name": "toni", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "95f63a7bf7b4824ab677a3a7f1c068c5c3b94b34", "algo": 1}, {"value": "57fa144c19adac0d105b9ec594b2ab07", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6408533, "fileLength": 375981, "downloadCount": 13133, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "sodiumdynamiclights-fabric-1.0.10-1.21.5.jar", "gameVersions": ["1.21.5", "<PERSON><PERSON><PERSON>"], "displayName": "SodiumDynamicLights Fabric 1.0.10 for 1.21.5", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000021.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6408/534/sodiumdynamiclights-fabric-1.0.10-1.21.5.jar", "fileDate": "2025-04-10T21:19:54.253Z", "exposeAsAlternative": null, "modId": 551736, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LambDynamicLights.LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": 716053157}, {"name": "mixins.sodiumdynamiclights.json", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "sodiumdynamiclights-fabric-1.21.5-fabric-refmap.json", "fingerprint": **********}, {"name": "sodiumdynamiclights.accesswidener", "fingerprint": **********}, {"name": "sodiumdynamiclights.toml", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.20.1.accesswidener", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.21.1.accesswidener", "fingerprint": **********}, {"name": "sodiumdynamiclights_1.21.4.accesswidener", "fingerprint": **********}, {"name": "toni", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 306612}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "5f16e822b5b32569dc71721e76f1bbf110c3e02e", "algo": 1}, {"value": "a3a1ad756392f90f183557f281a65f86", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6408534, "fileLength": 981684, "downloadCount": 92932, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-04-10T21:24:23.3Z", "gamePopularityRank": 98, "thumbsUpCount": 0, "name": "Sodium/Embeddium Dynamic Lights", "mainFileId": 6408534, "primaryCategoryId": 424, "downloadCount": 51677155, "status": 4, "authors": [{"name": "Txni", "id": 102782844, "url": "https://www.curseforge.com/members/txni"}], "available": false, "featured": false}