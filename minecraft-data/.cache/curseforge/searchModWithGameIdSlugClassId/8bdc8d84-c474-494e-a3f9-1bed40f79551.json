{"allowModDistribution": true, "screenshots": [{"description": "Small build I used during testing. Shows flame jets over mature netherwart and from lava pools.  Steam jets can occur over water blocks adjacent to hot blocks (in this case lava under the cauldron).", "id": 1049068, "title": "Testing", "modId": 238891, "url": "https://media.forgecdn.net/attachments/1049/68/2021-10-10_17-1.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1049/68/310/172/2021-10-10_17-1.png"}, {"description": "Morning fog in Biomes O'Plenty redwood forest", "id": 1059651, "title": "Morning Fog", "modId": 238891, "url": "https://media.forgecdn.net/attachments/1059/651/bop_morningfog.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1059/651/310/172/bop_morningfog.png"}, {"description": "Dense biome fog in the Biomes O'Plenty Ominous Woods", "id": 1059655, "title": "Dense Biome <PERSON>", "modId": 238891, "url": "https://media.forgecdn.net/attachments/1059/655/2025-01-07_10.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1059/655/310/172/2025-01-07_10.png"}, {"description": "Fire Jets spawn over lava source blocks.  They do not harm the player or any mobs.  The height and duration is based on the number of lava source blocks beneath the fire jet.", "id": 203025, "title": "Fire Jets", "modId": 238891, "url": "https://media.forgecdn.net/attachments/203/25/firejets.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/203/25/310/172/firejets.png"}, {"description": "Steam Jets will be generated around areas where lava meets water.", "id": 203033, "title": "Steam Jets", "modId": 238891, "url": "https://media.forgecdn.net/attachments/203/33/steam.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/203/33/310/172/steam.png"}, {"description": "Water that flows downward can generate splash particle effects as well as sound.  The splashes are larger if the water falls a greater distance.", "id": 203034, "title": "Waterfalls", "modId": 238891, "url": "https://media.forgecdn.net/attachments/203/34/watersplash.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/203/34/310/172/watersplash.png"}, {"description": "Naturally generated waterfall that I discovered wandering around in my test world.  (The clouds are generated by Weather2.)", "id": 209457, "title": "Large Waterfall", "modId": 238891, "url": "https://media.forgecdn.net/attachments/209/457/2017-05-18_07.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/209/457/310/172/2017-05-18_07.png"}, {"description": "Testing some updates with Compitum Magia and came across this lava lake.  It showcases steam jets, lava jets, and water splash particles from water falls.  Not sure what that bat was thinking.", "id": 223150, "title": "Lava Lake in Compitum Magia", "modId": 238891, "url": "https://media.forgecdn.net/attachments/223/150/2018-01-20_12.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/223/150/310/172/2018-01-20_12.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "dynamicsurroundings-neoforge-1.21.1-0.4.2.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6063191}, {"filename": "dynamicsurroundings-fabric-1.21.1-0.4.2.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6063186}, {"filename": "dynamicsurroundings-neoforge-1.21.1-0.4.0.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6017866}, {"filename": "dynamicsurroundings-fabric-1.21.1-0.4.0.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6017862}, {"filename": "DynamicSurroundings-1.16.5-4.0.5.0.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3502320}, {"filename": "DynamicSurroundings-1.12.2-3.6.3.jar", "releaseType": 2, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 3497269}, {"filename": "DynamicSurroundings-1.16.4-4.0.4.2.jar", "releaseType": 2, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3288746}, {"filename": "DynamicSurroundings-1.16.4-4.0.3.12.jar", "releaseType": 2, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3221841}, {"filename": "DynamicSurroundings-1.16.4-4.0.3.12.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3221841}, {"filename": "DynamicSurroundings-1.16.4-4.0.1.4.jar", "releaseType": 3, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3150554}, {"filename": "DynamicSurroundings-1.12.2-3.6.2.1.jar", "releaseType": 2, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": null, "fileId": 3098835}, {"filename": "DynamicSurroundings-1.12.2-3.6.1.0.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 2922489}, {"filename": "DynamicSurroundings-1.12.2-3.5.4.3.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2664392}, {"filename": "DynamicSurroundings-1.7.10-1.0.6.4.jar", "releaseType": 1, "gameVersion": "1.7.10", "gameVersionTypeId": 5, "modLoader": null, "fileId": 2642381}, {"filename": "DynamicSurroundings-1.11.2-3.4.9.17.jar", "releaseType": 1, "gameVersion": "1.11.2", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2631076}, {"filename": "DynamicSurroundings-1.10.2-3.4.9.17.jar", "releaseType": 1, "gameVersion": "1.10.2", "gameVersionTypeId": 572, "modLoader": null, "fileId": 2631075}, {"filename": "DynamicSurroundings-1.11.2-3.4.9.14.jar", "releaseType": 1, "gameVersion": "1.11", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2571987}, {"filename": "DynamicSurroundings-1.11.2-3.4.9.14.jar", "releaseType": 1, "gameVersion": "1.11.1", "gameVersionTypeId": 599, "modLoader": null, "fileId": 2571987}, {"filename": "DynamicSurroundings-1.12.2-*******.jar", "releaseType": 1, "gameVersion": "1.12.1", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2482521}, {"filename": "DynamicSurroundings-1.12.2-*******.jar", "releaseType": 1, "gameVersion": "1.12", "gameVersionTypeId": 628, "modLoader": null, "fileId": 2482521}, {"filename": "DynamicSurroundings-1.8.9-*******.jar", "releaseType": 1, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": null, "fileId": 2290471}], "dateCreated": "2015-12-17T04:34:09.057Z", "logo": {"description": "", "id": 33213, "title": "635892355993800985.png", "modId": 238891, "url": "https://media.forgecdn.net/avatars/33/213/635892355993800985.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/33/213/256/256/635892355993800985.png"}, "links": {"sourceUrl": "https://github.com/OreCruncher/DynamicSurroundingsFabric", "issuesUrl": "https://github.com/OreCruncher/DynamicSurroundingsFabric/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/dynamic-surroundings", "wikiUrl": "https://dynamic-surroundings.readthedocs.io"}, "dateReleased": "2025-01-07T17:04:48.18Z", "id": 238891, "categories": [{"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Adventure and RPG", "dateModified": "2014-05-08T17:42:09.54Z", "parentCategoryId": 6, "id": 422, "iconUrl": "https://media.forgecdn.net/avatars/6/37/635351497295252123.png", "slug": "adventure-rpg", "url": "https://www.curseforge.com/minecraft/mc-mods/adventure-rpg", "displayIndex": 0, "class": false}], "slug": "dynamic-surroundings", "gameId": 432, "summary": "Alters the fabric of Minecraft experience by weaving a tapestry of sound and visual effects", "latestFiles": [{"gameId": 432, "fileName": "DynamicSurroundings-1.12.2-3.5.4.3.jar", "gameVersions": ["1.12.2"], "displayName": "DynamicSurroundings-1.12.2-3.5.4.3.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000012.0000000002", "gameVersion": "1.12.2", "gameVersionReleaseDate": "2017-09-18T05:00:00Z", "gameVersionName": "1.12.2", "gameVersionTypeId": 628}], "downloadUrl": "https://edge.forgecdn.net/files/2664/392/DynamicSurroundings-1.12.2-3.5.4.3.jar", "fileDate": "2019-01-24T17:47:16.727Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 2794240201}, {"name": "CREDITS.md", "fingerprint": 179288411}, {"name": "assets", "fingerprint": 2371952905}, {"name": "mcmod.info", "fingerprint": 2104625066}, {"name": "org", "fingerprint": 3216078206}, {"name": "pack.mcmeta", "fingerprint": 2137018394}], "dependencies": [{"relationType": 3, "modId": 307806}], "fileFingerprint": 547702170, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "8669e65a8725a8b02aab813b2ae2b3e365ba98f4", "algo": 1}, {"value": "649280d416e51509eedfb1e1004bb5ef", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2664392, "fileLength": 14779288, "downloadCount": 4531910, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "DynamicSurroundings-1.12.2-3.6.1.0.jar", "gameVersions": ["Forge", "1.12.2"], "displayName": "DynamicSurroundings-1.12.2-3.6.1.0.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000012.0000000002", "gameVersion": "1.12.2", "gameVersionReleaseDate": "2017-09-18T05:00:00Z", "gameVersionName": "1.12.2", "gameVersionTypeId": 628}], "downloadUrl": "https://edge.forgecdn.net/files/2922/489/DynamicSurroundings-1.12.2-3.6.1.0.jar", "fileDate": "2020-04-06T11:11:32.390Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 3823998824}, {"name": "CREDITS.md", "fingerprint": 179288411}, {"name": "assets", "fingerprint": 2534433262}, {"name": "mcmod.info", "fingerprint": 3801765877}, {"name": "mixins.dsurround.json", "fingerprint": 3257810605}, {"name": "mixins.dsurround.refmap.json", "fingerprint": 566310331}, {"name": "org", "fingerprint": 3790299174}, {"name": "pack.mcmeta", "fingerprint": 2137018394}], "dependencies": [{"relationType": 3, "modId": 307806}], "fileFingerprint": 1977391340, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "3d5d62c9d44517991d19e165d2051209b2d06bf3", "algo": 1}, {"value": "1a0d50a34941865ff091a492e0612502", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 2922489, "fileLength": 15929762, "downloadCount": 12757190, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "DynamicSurroundings-1.16.4-4.0.1.4.jar", "gameVersions": ["Forge", "1.16.4"], "displayName": "DynamicSurroundings-1.16.4-4.0.1.4.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000016.0000000004", "gameVersion": "1.16.4", "gameVersionReleaseDate": "2020-11-02T18:40:51.49Z", "gameVersionName": "1.16.4", "gameVersionTypeId": 70886}], "downloadUrl": "https://edge.forgecdn.net/files/3150/554/DynamicSurroundings-1.16.4-4.0.1.4.jar", "fileDate": "2020-12-27T19:19:13.037Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 2945840152}, {"name": "assets", "fingerprint": 2371471167}, {"name": "logo.png", "fingerprint": 272076045}, {"name": "mixins.dsurround.refmap.json", "fingerprint": 128540924}, {"name": "mixins.environs.json", "fingerprint": 1098960403}, {"name": "mixins.mobeffects.json", "fingerprint": 3568797176}, {"name": "mixins.sndctrl.json", "fingerprint": 1406853253}, {"name": "org", "fingerprint": 917297948}, {"name": "pack.mcmeta", "fingerprint": 3457028693}], "dependencies": [], "fileFingerprint": 3028266233, "fileStatus": 4, "releaseType": 3, "hashes": [{"value": "a741e09a2971d2eeb9b25183da86dc8c9d54ad7e", "algo": 1}, {"value": "fc2636aad029a0c5239e9f53d93b5357", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3150554, "fileLength": 14666032, "downloadCount": 14456, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "DynamicSurroundings-1.16.4-4.0.3.12.jar", "gameVersions": ["1.16.5", "1.16.4"], "displayName": "DynamicSurroundings-1.16.4-4.0.3.12.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "0000000001.0000000016.0000000004", "gameVersion": "1.16.4", "gameVersionReleaseDate": "2020-11-02T18:40:51.49Z", "gameVersionName": "1.16.4", "gameVersionTypeId": 70886}], "downloadUrl": "https://edge.forgecdn.net/files/3221/841/DynamicSurroundings-1.16.4-4.0.3.12.jar", "fileDate": "2021-02-28T17:32:32.100Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 3602814371}, {"name": "assets", "fingerprint": 1453879785}, {"name": "logo.png", "fingerprint": 272076045}, {"name": "mixins.dsurround.json", "fingerprint": 3520738230}, {"name": "mixins.dsurround.refmap.json", "fingerprint": 1279673114}, {"name": "mixins.environs.json", "fingerprint": 888557490}, {"name": "mixins.mobeffects.json", "fingerprint": 3568797176}, {"name": "mixins.sndctrl.json", "fingerprint": 1406853253}, {"name": "org", "fingerprint": 3315050670}, {"name": "pack.mcmeta", "fingerprint": 3863688199}], "dependencies": [{"relationType": 2, "modId": 348521}], "fileFingerprint": 3764385869, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "196026b786f0a5a6aab6f9f59952f02bfd069852", "algo": 1}, {"value": "cf60e64af697fe979a10af8ea53b634d", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3221841, "fileLength": 14848584, "downloadCount": 345564, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "DynamicSurroundings-1.16.5-4.0.5.0.jar", "gameVersions": ["1.16.5", "Forge"], "displayName": "DynamicSurroundings-1.16.5-4.0.5.0.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/3502/320/DynamicSurroundings-1.16.5-4.0.5.0.jar", "fileDate": "2021-10-24T15:24:00.503Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 2905263782}, {"name": "org", "fingerprint": 4247164416}, {"name": "assets", "fingerprint": 1318945966}, {"name": "logo.png", "fingerprint": 272076045}, {"name": "mixins.dsurround.json", "fingerprint": 3520738230}, {"name": "mixins.environs.json", "fingerprint": 888557490}, {"name": "mixins.mobeffects.json", "fingerprint": 3568797176}, {"name": "mixins.sndctrl.json", "fingerprint": 1406853253}, {"name": "pack.mcmeta", "fingerprint": 3863688199}, {"name": "mixins.dsurround.refmap.json", "fingerprint": 1279673114}], "dependencies": [], "fileFingerprint": 312192805, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "e36566f26398ac76847b912dcd165591170a088b", "algo": 1}, {"value": "047b3dc132f9e591104af6a63c2ac2e3", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3502320, "fileLength": 14746751, "downloadCount": 12190992, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "dynamicsurroundings-fabric-1.21.1-0.4.0.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "Client", "1.21.1"], "displayName": "dynamicsurroundings-fabric-1.21.1-0.4.0.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0000000001.0000000021.0000000001", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6017/862/dynamicsurroundings-fabric-1.21.1-0.4.0.jar", "fileDate": "2024-12-23T22:45:05.140Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 800245718}, {"name": "architectury_inject_dsurround_common_bca3d4d5a9f84f0d89567aab7a034384_56aae64cecd57fdab13dfba1658753ad27b64c804abc82687c7e7243238734a9dynamicsurroundingscommon1211040devjar", "fingerprint": 1307699365}, {"name": "assets", "fingerprint": 1951445950}, {"name": "dsurround.accesswidener", "fingerprint": 476935859}, {"name": "dsurround.mixins.json", "fingerprint": 1771348366}, {"name": "dynamicsurroundings-common-1.21.1-common-refmap.json", "fingerprint": 1350197469}, {"name": "fabric.mod.json", "fingerprint": 1257993158}, {"name": "org", "fingerprint": 749816456}], "dependencies": [{"relationType": 3, "modId": 306612}, {"relationType": 3, "modId": 419699}], "fileFingerprint": 2503314206, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "6aef368f76f0346b68bd1674b587536baf15ee1e", "algo": 1}, {"value": "a1bee309ac730f2b52b1a113ffa0c59d", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6017862, "fileLength": 14231818, "downloadCount": 2044, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "dynamicsurroundings-neoforge-1.21.1-0.4.0.jar", "gameVersions": ["Client", "NeoForge", "1.21.1"], "displayName": "dynamicsurroundings-neoforge-1.21.1-0.4.0.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000001", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6017/866/dynamicsurroundings-neoforge-1.21.1-0.4.0.jar", "fileDate": "2024-12-23T22:46:45.920Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 2985299527}, {"name": "architectury.common.json", "fingerprint": 969790645}, {"name": "architectury_inject_dsurround_common_bca3d4d5a9f84f0d89567aab7a034384_56aae64cecd57fdab13dfba1658753ad27b64c804abc82687c7e7243238734a9dynamicsurroundingscommon1211040devjar", "fingerprint": 2966457311}, {"name": "assets", "fingerprint": 1951445950}, {"name": "dsurround.mixins.json", "fingerprint": 636075315}, {"name": "dynamicsurroundings-common-1.21.1-common-refmap.json", "fingerprint": 1350197469}, {"name": "org", "fingerprint": 674138832}], "dependencies": [{"relationType": 3, "modId": 419699}], "fileFingerprint": 1918001217, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "c0bb9930a7577a51ddbe059d8cf42911bf02af7e", "algo": 1}, {"value": "325ce6c4cd054d04ab2737f0b7c6022f", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6017866, "fileLength": 12228762, "downloadCount": 1394, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "dynamicsurroundings-fabric-1.21.1-0.4.2.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "Client", "1.21.1"], "displayName": "dynamicsurroundings-fabric-1.21.1-0.4.2.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0000000001.0000000021.0000000001", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6063/186/dynamicsurroundings-fabric-1.21.1-0.4.2.jar", "fileDate": "2025-01-07T17:04:10.637Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 800245718}, {"name": "CREDITS.md", "fingerprint": 2443528580}, {"name": "architectury_inject_dsurround_common_bca3d4d5a9f84f0d89567aab7a034384_c9f48380a0dc82188e06a408404bee630279906c1e79953deba5d7e1c7ba0cf9dynamicsurroundingscommon1211042devjar", "fingerprint": 3026925342}, {"name": "assets", "fingerprint": 1464586015}, {"name": "dsurround.accesswidener", "fingerprint": 3444160766}, {"name": "dsurround.mixins.fabric.json", "fingerprint": 1464443034}, {"name": "dsurround.mixins.json", "fingerprint": 3317714671}, {"name": "dynamicsurroundings-common-1.21.1-common-refmap.json", "fingerprint": 2968557173}, {"name": "dynamicsurroundings-fabric-1.21.1-fabric-refmap.json", "fingerprint": 525764194}, {"name": "fabric.mod.json", "fingerprint": 1477455307}, {"name": "org", "fingerprint": 2877183646}], "dependencies": [{"relationType": 3, "modId": 419699}], "fileFingerprint": 1947226021, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "bffadbe66ca57be50bc9afb2de9be1e88a67944e", "algo": 1}, {"value": "c0dbe9c0d4c420bf951f3bad068112d3", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6063186, "fileLength": 16849254, "downloadCount": 67199, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "dynamicsurroundings-neoforge-1.21.1-0.4.2.jar", "gameVersions": ["Client", "NeoForge", "1.21.1"], "displayName": "dynamicsurroundings-neoforge-1.21.1-0.4.2.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000001", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6063/191/dynamicsurroundings-neoforge-1.21.1-0.4.2.jar", "fileDate": "2025-01-07T17:04:48.180Z", "exposeAsAlternative": null, "modId": 238891, "modules": [{"name": "META-INF", "fingerprint": 2820159428}, {"name": "CREDITS.md", "fingerprint": 2443528580}, {"name": "architectury.common.json", "fingerprint": 969790645}, {"name": "architectury_inject_dsurround_common_bca3d4d5a9f84f0d89567aab7a034384_c9f48380a0dc82188e06a408404bee630279906c1e79953deba5d7e1c7ba0cf9dynamicsurroundingscommon1211042devjar", "fingerprint": 1500356586}, {"name": "assets", "fingerprint": 1464586015}, {"name": "dsurround.mixins.json", "fingerprint": 171398299}, {"name": "dynamicsurroundings-common-1.21.1-common-refmap.json", "fingerprint": 2968557173}, {"name": "org", "fingerprint": 2405101101}], "dependencies": [{"relationType": 3, "modId": 419699}], "fileFingerprint": 3064256288, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "675e9d0ed4ea3ac1e97539ee5c1d1e8c5cdca14d", "algo": 1}, {"value": "4221c837d3eda35e30949c7811172c94", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6063191, "fileLength": 14844663, "downloadCount": 29244, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-01-07T18:38:25.547Z", "gamePopularityRank": 899, "thumbsUpCount": 0, "name": "Dynamic Surroundings", "mainFileId": 6063191, "primaryCategoryId": 424, "downloadCount": 68505967, "status": 4, "authors": [{"name": "OreCruncher", "id": 12671817, "url": "https://www.curseforge.com/members/orecruncher"}], "available": false, "featured": false}