{"allowModDistribution": false, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "entityculling-forge-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780252}, {"filename": "entityculling-forge-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780252}, {"filename": "entityculling-forge-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780252}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780251}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780251}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780251}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780250}, {"filename": "entityculling-fabric-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780249}, {"filename": "entityculling-fabric-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780249}, {"filename": "entityculling-fabric-1.8.2-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780249}, {"filename": "entityculling-forge-1.8.2-mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780248}, {"filename": "entityculling-fabric-1.8.2-mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780247}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780245}, {"filename": "entityculling-forge-1.8.2-mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780244}, {"filename": "entityculling-fabric-1.8.2-mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780243}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780242}, {"filename": "entityculling-forge-1.8.2-mc1.21.3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780241}, {"filename": "entityculling-fabric-1.8.2-mc1.21.3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780239}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780238}, {"filename": "entityculling-neoforge-1.8.2-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6780238}, {"filename": "entityculling-forge-1.8.2-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780237}, {"filename": "entityculling-forge-1.8.2-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6780237}, {"filename": "entityculling-fabric-1.8.2-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780236}, {"filename": "entityculling-fabric-1.8.2-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6780236}, {"filename": "entityculling-neoforge-1.8.2-mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6780235}, {"filename": "entityculling-forge-1.8.2-mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6780234}, {"filename": "entityculling-fabric-1.8.2-mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6780233}, {"filename": "entityculling-neoforge-1.8.2-mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6780232}, {"filename": "entityculling-forge-1.8.2-mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6780231}, {"filename": "entityculling-fabric-1.8.2-mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6780230}, {"filename": "entityculling-neoforge-1.8.2-mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6780229}, {"filename": "entityculling-forge-1.8.2-mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6780228}, {"filename": "entityculling-fabric-1.8.2-mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6780227}, {"filename": "entityculling-forge-1.8.2-mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6780226}, {"filename": "entityculling-fabric-1.8.2-mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6780225}, {"filename": "entityculling-fabric-1.8.2-mc1.19.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6780224}, {"filename": "entityculling-forge-1.8.2-mc1.19.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6780223}, {"filename": "entityculling-forge-1.7.3-mc1.21.4.jar", "releaseType": 2, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6236208}, {"filename": "entityculling-neoforge-1.7.3-mc1.21.4.jar", "releaseType": 2, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6236207}, {"filename": "entityculling-forge-1.7.3-mc1.21.3.jar", "releaseType": 2, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6236205}, {"filename": "entityculling-neoforge-1.7.3-mc1.21.3.jar", "releaseType": 2, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6236203}, {"filename": "entityculling-neoforge-1.7.3-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6236200}, {"filename": "entityculling-neoforge-1.7.3-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6236200}, {"filename": "entityculling-forge-1.7.3-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6236197}, {"filename": "entityculling-forge-1.7.3-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6236197}, {"filename": "entityculling-neoforge-1.7.3-mc1.20.6.jar", "releaseType": 2, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6236193}, {"filename": "entityculling-forge-1.7.3-mc1.20.6.jar", "releaseType": 2, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6236191}, {"filename": "entityculling-neoforge-1.7.3-mc1.20.4.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6236189}, {"filename": "entityculling-forge-1.7.3-mc1.20.4.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6236188}, {"filename": "entityculling-neoforge-1.7.3-mc1.20.2.jar", "releaseType": 2, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6236061}, {"filename": "entityculling-forge-1.7.3-mc1.20.2.jar", "releaseType": 2, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6236060}, {"filename": "entityculling-forge-1.7.3-mc1.20.1.jar", "releaseType": 2, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6236056}, {"filename": "entityculling-forge-1.7.3-mc1.19.4.jar", "releaseType": 2, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6236054}, {"filename": "entityculling-fabric-1.7.3-mc1.19.3.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6236053}, {"filename": "entityculling-forge-1.7.3-mc1.19.3.jar", "releaseType": 2, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6236052}, {"filename": "entityculling-1.6.4-mc1.7.10.jar", "releaseType": 1, "gameVersion": "1.7.10", "gameVersionTypeId": 5, "modLoader": 1, "fileId": 5351930}, {"filename": "entityculling-fabric-1.6.4-mc1.20.5.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5286297}, {"filename": "entityculling-1.12.2-1.6.3.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 5170009}, {"filename": "entityculling-fabric-1.6.2-mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4763646}, {"filename": "entityculling-forge-1.6.2-mc1.20.1.jar", "releaseType": 2, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4763645}, {"filename": "entityculling-fabric-1.6.1-mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 4406218}, {"filename": "entityculling-forge-1.6.1-mc1.18.2.jar", "releaseType": 2, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 4406217}, {"filename": "entityculling-fabric-1.6.1-mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4404950}, {"filename": "entityculling-fabric-1.6.1-mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4404950}, {"filename": "entityculling-fabric-1.6.1-mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4404950}, {"filename": "entityculling-forge-1.6.1-mc1.19.2.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4404949}, {"filename": "entityculling-forge-1.6.1-mc1.19.2.jar", "releaseType": 2, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4404949}, {"filename": "entityculling-forge-1.6.1-mc1.19.2.jar", "releaseType": 2, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4404949}, {"filename": "entityculling-fabric-mc1.16.5-1.5.2.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3760390}, {"filename": "entityculling-fabric-mc1.16.5-1.5.2.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3760390}, {"filename": "entityculling-fabric-mc1.16.5-1.5.2.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3760390}, {"filename": "entityculling-fabric-mc1.16.5-1.5.2.jar", "releaseType": 1, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3760390}, {"filename": "entityculling-fabric-mc1.16.5-1.5.2.jar", "releaseType": 1, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3760390}, {"filename": "entityculling-fabric-mc1.16.5-1.5.2.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3760390}, {"filename": "entityculling-forge-mc1.16.5-1.5.2.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3760389}, {"filename": "entityculling-forge-mc1.16.5-1.5.2.jar", "releaseType": 2, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3760389}, {"filename": "entityculling-forge-mc1.16.5-1.5.2.jar", "releaseType": 2, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3760389}, {"filename": "entityculling-forge-mc1.16.5-1.5.2.jar", "releaseType": 2, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3760389}, {"filename": "entityculling-forge-mc1.16.5-1.5.2.jar", "releaseType": 2, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3760389}, {"filename": "entityculling-forge-mc1.16.5-1.5.2.jar", "releaseType": 2, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3760389}, {"filename": "entityculling-fabric-mc1.18-1.5.1.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 3743933}, {"filename": "entityculling-fabric-mc1.18-1.5.1.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 3743933}, {"filename": "entityculling-forge-mc1.18-1.5.1.jar", "releaseType": 2, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3743929}, {"filename": "entityculling-forge-mc1.18-1.5.1.jar", "releaseType": 2, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3743929}, {"filename": "entityculling-fabric-mc1.17.1-1.5.0.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 3642141}, {"filename": "entityculling-fabric-mc1.17.1-1.5.0.jar", "releaseType": 1, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 3642141}, {"filename": "entityculling-forge-mc1.17.1-1.5.0.jar", "releaseType": 2, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 3642139}, {"filename": "entityculling-forge-mc1.17.1-1.5.0.jar", "releaseType": 2, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 3642139}, {"filename": "entityculling-forge-mc1.8.9-1.5.0.jar", "releaseType": 2, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": 1, "fileId": 3642136}, {"filename": "EntityCulling-Fabric-1.0.1-vanilla-tags.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3208944}, {"filename": "EntityCulling-Fabric-1.0.1-vanilla-tags.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": null, "fileId": 3208944}], "dateCreated": "2021-02-21T09:31:33.887Z", "logo": {"description": "", "id": 345318, "title": "637492631463701682.png", "modId": 448233, "url": "https://media.forgecdn.net/avatars/345/318/637492631463701682.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/345/318/256/256/637492631463701682.png"}, "links": {"sourceUrl": "https://github.com/tr7zw/EntityCulling-Fabric", "issuesUrl": "https://github.com/tr7zw/EntityCulling/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/entityculling", "wikiUrl": ""}, "dateReleased": "2025-07-17T19:31:46.883Z", "id": 448233, "categories": [{"gameId": 432, "classId": 6, "name": "Miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "parentCategoryId": 6, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}], "slug": "entityculling", "gameId": 432, "summary": "Using async path-tracing to hide Tiles/Entities that are not visible", "latestFiles": [{"gameId": 432, "fileName": "EntityCulling-Fabric-1.0.1-vanilla-tags.jar", "gameVersions": ["1.16.5", "1.16.4"], "displayName": "1.0.1 vanilla tags", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.16.4", "gameVersionReleaseDate": "2020-11-02T18:40:51.49Z", "gameVersionName": "1.16.4", "gameVersionTypeId": 70886}], "downloadUrl": null, "fileDate": "2021-02-18T23:00:10Z", "exposeAsAlternative": null, "modId": 448233, "modules": [{"name": "LICENSE_EntityCulling-Fabric", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "entityculling.mixins.json", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "EntityCulling-Fabric-refmap.json", "fingerprint": **********}, {"name": "META-INF", "fingerprint": **********}, {"name": "dev", "fingerprint": 340040029}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "b9935546220635faa33f3450b8a85251e1b3d6e6", "algo": 1}, {"value": "cfe88ea2e20a6782e0ebd8800a44d647", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3208944, "fileLength": 38217, "downloadCount": 315662, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "entityculling-neoforge-1.7.3-mc1.21.4.jar", "gameVersions": ["1.21.4", "NeoForge"], "displayName": "1.7.3-1.21.4 - <PERSON><PERSON><PERSON><PERSON>", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": null, "fileDate": "2025-02-25T20:17:40.577Z", "exposeAsAlternative": null, "modId": 448233, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "LICENSE-EntityCulling", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "entityculling.mixins.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "936fdead69a3b44c6660c94093b6fe81430831b0", "algo": 1}, {"value": "ebbefa8c98a24eb9bbf67a08769908e4", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6236207, "fileLength": 74856, "downloadCount": 71358, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "entityculling-forge-1.7.3-mc1.21.4.jar", "gameVersions": ["1.21.4", "Forge"], "displayName": "1.7.3-1.21.4 - Forge", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": null, "fileDate": "2025-02-25T20:17:45.233Z", "exposeAsAlternative": null, "modId": 448233, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "LICENSE-EntityCulling", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "entityculling.mixins.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "7e44f753177174b667a01a562106cb1539b9dc87", "algo": 1}, {"value": "3e57455a4f11257ea4c7dbe31fad6a7a", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6236208, "fileLength": 75799, "downloadCount": 24102, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "entityculling-fabric-1.8.2-mc1.21.6.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.8", "1.21.7", "1.21.6"], "displayName": "1.8.2-1.21.6 - <PERSON><PERSON><PERSON>", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}], "downloadUrl": null, "fileDate": "2025-07-17T19:31:45.250Z", "exposeAsAlternative": null, "modId": 448233, "modules": [{"name": "META-INF", "fingerprint": 850377798}, {"name": "LICENSE", "fingerprint": **********}, {"name": "LICENSE-EntityCulling", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "entityculling.mixins.json", "fingerprint": **********}, {"name": "entityculling.refmap.mixins.json", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 306612}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "56c5a0617863c53cf7e338d6ff862d02801a1f75", "algo": 1}, {"value": "65b3d8aba6a4f04b8673b4f0a8a6fc8d", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6780249, "fileLength": 473028, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "entityculling-neoforge-1.8.2-mc1.21.6.jar", "gameVersions": ["1.21.8", "NeoForge", "1.21.7", "1.21.6"], "displayName": "1.8.2-1.21.6 - <PERSON><PERSON><PERSON><PERSON>", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}], "downloadUrl": null, "fileDate": "2025-07-17T19:31:46.580Z", "exposeAsAlternative": null, "modId": 448233, "modules": [{"name": "META-INF", "fingerprint": 916949941}, {"name": "LICENSE", "fingerprint": **********}, {"name": "LICENSE-EntityCulling", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "entityculling.mixins.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [], "fileFingerprint": 235165191, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "77cdf8d7c49d52c9baff1250b63065162b629d32", "algo": 1}, {"value": "4ec38f7aa67d283a378e6d8627e40670", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6780251, "fileLength": 477604, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "entityculling-forge-1.8.2-mc1.21.6.jar", "gameVersions": ["1.21.8", "Forge", "1.21.7", "1.21.6"], "displayName": "1.8.2-1.21.6 - Forge", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}], "downloadUrl": null, "fileDate": "2025-07-17T19:31:46.883Z", "exposeAsAlternative": null, "modId": 448233, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "LICENSE-EntityCulling", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "entityculling.mixins.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "b14b121c2f97ae5f50597f131cf0bb49bc5c3f73", "algo": 1}, {"value": "bff6381ac2ddf2e5a1e3977a6c7906fe", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6780252, "fileLength": 478306, "downloadCount": 1072, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-07-17T19:34:53.057Z", "gamePopularityRank": 26, "thumbsUpCount": 0, "name": "En<PERSON><PERSON> Culling Fabric/Forge", "mainFileId": 6780252, "primaryCategoryId": 425, "downloadCount": 138859145, "status": 4, "authors": [{"name": "tr7zw", "id": 100212189, "url": "https://www.curseforge.com/members/tr7zw"}], "available": false, "featured": false}