{"allowModDistribution": true, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "Iceberg-1.21.1-forge-1.3.2.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6423866}, {"filename": "Iceberg-1.21.1-neoforge-1.3.2.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6423863}, {"filename": "Iceberg-1.21.4-forge-1.2.13.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6186850}, {"filename": "Iceberg-1.21.4-neoforge-1.2.13.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6186848}, {"filename": "Iceberg-1.21.3-forge-1.2.10.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5991535}, {"filename": "Iceberg-1.21.3-neoforge-1.2.10.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5991534}, {"filename": "Iceberg-1.20.1-forge-1.1.25.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5838149}, {"filename": "Iceberg-1.20.1-forge-1.1.25.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5838149}, {"filename": "Iceberg-1.21-neoforge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5625109}, {"filename": "Iceberg-1.21-forge-1.2.5.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5625103}, {"filename": "Iceberg-1.20.6-forge-1.1.23.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5365013}, {"filename": "Iceberg-1.20.4-forge-1.1.20.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5012668}, {"filename": "Iceberg-1.20.1-forge-1.1.18.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4953572}, {"filename": "Iceberg-1.20.1-forge-1.1.18.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4953572}, {"filename": "Iceberg-1.20.2-forge-1.1.17.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4918151}, {"filename": "Iceberg-1.19.4-forge-1.1.10.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4548283}, {"filename": "Iceberg-1.19.4-forge-1.1.7.jar", "releaseType": 2, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4484740}, {"filename": "Iceberg-1.19.3-forge-1.1.6.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4483013}, {"filename": "Iceberg-1.19.2-forge-1.1.4.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4300529}, {"filename": "Iceberg-1.19.2-forge-1.1.4.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4300529}, {"filename": "Iceberg-1.19.2-forge-1.1.4.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4300529}, {"filename": "Iceberg-1.18.2-forge-1.0.49.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 4035917}, {"filename": "Iceberg-1.16.5-1.0.45.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 3943132}, {"filename": "Iceberg-1.18.1-1.0.40.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3632396}, {"filename": "Iceberg-1.18-1.0.30.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 3553159}, {"filename": "Iceberg-1.17.1-1.0.27.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 3538160}, {"filename": "Iceberg-1.17.1-1.0.16.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": null, "fileId": 3496382}], "dateCreated": "2021-08-26T09:07:03.47Z", "logo": {"description": "", "id": 425207, "title": "637653794535451378.png", "modId": 520110, "url": "https://media.forgecdn.net/avatars/425/207/637653794535451378.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/425/207/256/256/637653794535451378.png"}, "links": {"sourceUrl": "https://github.com/AHilyard/Iceberg", "issuesUrl": "https://discord.gg/UCKjnamDaW", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/iceberg", "wikiUrl": ""}, "dateReleased": "2025-04-14T22:14:29.873Z", "id": 520110, "categories": [{"gameId": 432, "classId": 6, "name": "API and Library", "dateModified": "2014-05-23T03:21:44.06Z", "parentCategoryId": 6, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "displayIndex": 0, "class": false}], "slug": "iceberg", "gameId": 432, "summary": "A modding library that contains new events, helpers, and utilities to make modder's lives easier.", "latestFiles": [{"gameId": 432, "fileName": "Iceberg-1.17.1-1.0.16.jar", "gameVersions": ["1.17.1"], "displayName": "Iceberg-1.17.1-1.0.16.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000017.0000000001", "gameVersion": "1.17.1", "gameVersionReleaseDate": "2021-07-06T14:16:03.97Z", "gameVersionName": "1.17.1", "gameVersionTypeId": 73242}], "downloadUrl": "https://edge.forgecdn.net/files/3496/382/Iceberg-1.17.1-1.0.16.jar", "fileDate": "2021-10-19T17:56:28.023Z", "exposeAsAlternative": null, "modId": 520110, "modules": [{"name": "META-INF", "fingerprint": 1496386813}, {"name": "com", "fingerprint": 3338182354}, {"name": "iceberg.mixins.json", "fingerprint": 1406697861}, {"name": "icon.png", "fingerprint": 577472019}, {"name": "pack.mcmeta", "fingerprint": 2638007011}, {"name": "iceberg.refmap.json", "fingerprint": 4246990719}], "dependencies": [], "fileFingerprint": 1363465101, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "afbca851a04b6962db2709dc008b1b8c3ae0c4da", "algo": 1}, {"value": "0055323744b8e0f0a105f3ed607e8290", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 3496382, "fileLength": 50719, "downloadCount": 53057, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Iceberg-1.19.4-forge-1.1.7.jar", "gameVersions": ["1.19.4", "Client", "Forge", "Server"], "displayName": "Iceberg-1.19.4-forge-1.1.7.jar", "sortableGameVersions": [{"gameVersionPadded": "0000000001.0000000019.0000000004", "gameVersion": "1.19.4", "gameVersionReleaseDate": "2023-03-09T00:00:00Z", "gameVersionName": "1.19.4", "gameVersionTypeId": 73407}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Server", "gameVersionTypeId": 75208}], "downloadUrl": "https://edge.forgecdn.net/files/4484/740/Iceberg-1.19.4-forge-1.1.7.jar", "fileDate": "2023-04-10T19:43:50.963Z", "exposeAsAlternative": null, "modId": 520110, "modules": [{"name": "META-INF", "fingerprint": 2893594662}, {"name": "com", "fingerprint": 665770255}, {"name": "iceberg.mixins.json", "fingerprint": 3556340450}, {"name": "icon.png", "fingerprint": 577472019}, {"name": "pack.mcmeta", "fingerprint": 2922289509}, {"name": "iceberg.refmap.json", "fingerprint": 4065013294}], "dependencies": [{"relationType": 2, "modId": 457570}], "fileFingerprint": 275880376, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "a27299748c44189cc9da634d3cc935c59b24e009", "algo": 1}, {"value": "4c5ee15b7320279e4bbcb841f6d655b7", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 4484740, "fileLength": 131712, "downloadCount": 2061, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Iceberg-1.21.1-neoforge-1.3.2.jar", "gameVersions": ["Client", "NeoForge", "1.21.1", "Server"], "displayName": "Iceberg-1.21.1-neoforge-1.3.2.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000001", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Server", "gameVersionTypeId": 75208}], "downloadUrl": "https://edge.forgecdn.net/files/6423/863/Iceberg-1.21.1-neoforge-1.3.2.jar", "fileDate": "2025-04-14T22:14:11.070Z", "exposeAsAlternative": null, "modId": 520110, "modules": [{"name": "META-INF", "fingerprint": 1307467203}, {"name": "Iceberg-1.21.1-common-common-refmap.json", "fingerprint": 1216419446}, {"name": "architectury_inject_Iceberg_common_2684b2a2577b40d29157a0302d02334e_dc66f61b145a2ccb9871c0644dd2fbc0cbbf2fba1692f3b7b20b45f1c66269d1I<PERSON>berg1211common132devjar", "fingerprint": 2817635932}, {"name": "com", "fingerprint": 804829002}, {"name": "iceberg.mixins.json", "fingerprint": 2269834361}, {"name": "iceberg.neoforge.mixins.json", "fingerprint": 4248487057}, {"name": "icon.png", "fingerprint": 577472019}], "dependencies": [], "fileFingerprint": 1913368417, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "f6014d198be9503573cd9cb41cc789d282768ad8", "algo": 1}, {"value": "68473a4ca9a827669c13d3855c8dc0e2", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6423863, "fileLength": 213853, "downloadCount": 820785, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Iceberg-1.21.1-forge-1.3.2.jar", "gameVersions": ["Client", "Forge", "1.21.1", "Server"], "displayName": "Iceberg-1.21.1-forge-1.3.2.jar", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000021.0000000001", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Server", "gameVersionTypeId": 75208}], "downloadUrl": "https://edge.forgecdn.net/files/6423/866/Iceberg-1.21.1-forge-1.3.2.jar", "fileDate": "2025-04-14T22:14:29.873Z", "exposeAsAlternative": null, "modId": 520110, "modules": [{"name": "META-INF", "fingerprint": 28537767}, {"name": "Iceberg-1.21.1-common-common-refmap.json", "fingerprint": 1216419446}, {"name": "Iceberg-1.21.1-forge-forge-refmap.json", "fingerprint": 1493072104}, {"name": "architectury_inject_Iceberg_common_2684b2a2577b40d29157a0302d02334e_dc66f61b145a2ccb9871c0644dd2fbc0cbbf2fba1692f3b7b20b45f1c66269d1I<PERSON>berg1211common132devjar", "fingerprint": 3381030787}, {"name": "com", "fingerprint": 2946640491}, {"name": "iceberg.forge.mixins.json", "fingerprint": 711910532}, {"name": "iceberg.mixins.json", "fingerprint": 2269834361}, {"name": "icon.png", "fingerprint": 577472019}, {"name": "pack.mcmeta", "fingerprint": 3761754025}], "dependencies": [], "fileFingerprint": 2911132457, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "034ab6d4648232c3fda00e001894a34427499f88", "algo": 1}, {"value": "21f33a938cf9ebe3ab5ebc39f6b8b615", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6423866, "fileLength": 201858, "downloadCount": 8601, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-04-14T22:24:01.507Z", "gamePopularityRank": 62, "thumbsUpCount": 0, "name": "Iceberg [Neo/Forge]", "mainFileId": 6423866, "primaryCategoryId": 421, "downloadCount": 103533311, "status": 4, "authors": [{"name": "Grend_G", "id": 100663802, "url": "https://www.curseforge.com/members/grend_g"}], "available": false, "featured": false}