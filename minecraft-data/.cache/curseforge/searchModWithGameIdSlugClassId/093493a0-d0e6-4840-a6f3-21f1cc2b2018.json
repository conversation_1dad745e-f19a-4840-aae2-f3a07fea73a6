{"allowModDistribution": true, "screenshots": [{"description": "", "id": 735237, "title": "Fully compatible with Catalogue!", "modId": 917285, "url": "https://media.forgecdn.net/attachments/735/237/catalogue_compat.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/735/237/310/172/catalogue_compat.png"}, {"description": "Now you can finally make the background pink wool instead of dirt, just like you always wanted!", "id": 735235, "title": "Customized menu backgrounds", "modId": 917285, "url": "https://media.forgecdn.net/attachments/735/235/pink_wool_bg.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/735/235/310/172/pink_wool_bg.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "YungsMenuTweaks-1.21.4-NeoForge-2.4.0.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6424568}, {"filename": "YungsMenuTweaks-1.21.1-Forge-2.1.2.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6158373}, {"filename": "YungsMenuTweaks-1.21.1-Forge-2.1.2.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6158373}, {"filename": "YungsMenuTweaks-1.20.4-Forge-1.4.1.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5331583}, {"filename": "YungsMenuTweaks-1.20.1-Forge-1.0.2.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5113568}], "dateCreated": "2023-09-28T08:52:38.503Z", "logo": {"description": "", "id": 882601, "title": "638314585094908731.png", "modId": 917285, "url": "https://media.forgecdn.net/avatars/882/601/638314585094908731.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/882/601/256/256/638314585094908731.png"}, "links": {"sourceUrl": "https://github.com/yungnickyoung/YUNGs-Menu-Tweaks", "issuesUrl": "https://github.com/yungnickyoung/YUNGs-Menu-Tweaks/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/yungs-menu-tweaks", "wikiUrl": ""}, "dateReleased": "2025-04-15T03:43:52.47Z", "id": 917285, "categories": [{"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}], "slug": "yungs-menu-tweaks", "gameId": 432, "summary": "A small, lightweight mod that makes browsing menus a lot easier", "latestFiles": [{"gameId": 432, "fileName": "YungsMenuTweaks-1.21.1-Forge-2.1.2.jar", "gameVersions": ["1.21", "Forge", "1.21.1"], "displayName": "[1.21.1] YUNG's Menu Tweaks v2.1.2 (<PERSON><PERSON>)", "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6158/373/YungsMenuTweaks-1.21.1-Forge-2.1.2.jar", "fileDate": "2025-02-04T20:17:25.787Z", "exposeAsAlternative": null, "modId": 917285, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "catalogue_background.png", "fingerprint": **********}, {"name": "com", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "logo.png", "fingerprint": 78224509}, {"name": "pack.mcmeta", "fingerprint": 839919078}, {"name": "yungsmenutweaks.mixins.json", "fingerprint": **********}, {"name": "yungsmenutweaks_forge.mixins.json", "fingerprint": **********}, {"name": "LICENSE_YungsMenuTweaks", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 421850}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "60a9c4590c4ca0ac8d810ed61b235c15ccc33cb5", "algo": 1}, {"value": "81b81d390ea09ec7c80e3f65d0e957ae", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6158373, "fileLength": 157820, "downloadCount": 7461, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "YungsMenuTweaks-1.21.4-NeoForge-2.4.0.jar", "gameVersions": ["1.21.4", "NeoForge"], "displayName": "[1.21.4] YUNG's Menu Tweaks v2.4.0 (<PERSON>Forge)", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6424/568/YungsMenuTweaks-1.21.4-NeoForge-2.4.0.jar", "fileDate": "2025-04-15T03:43:52.470Z", "exposeAsAlternative": null, "modId": 917285, "modules": [{"name": "META-INF", "fingerprint": 356379968}, {"name": "com", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "catalogue_background.png", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "logo.png", "fingerprint": 78224509}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "yungsmenutweaks.mixins.json", "fingerprint": **********}, {"name": "yungsmenutweaks_neoforge.mixins.json", "fingerprint": 218290451}, {"name": "LICENSE_YungsMenuTweaks", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 421850}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "b5214d18cfa9633a5eb940774b00f959e11f7f40", "algo": 1}, {"value": "1e65605dcf1b9f38bb0af88e21cd656a", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6424568, "fileLength": 156273, "downloadCount": 715, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-04-15T03:49:44.077Z", "gamePopularityRank": 687, "thumbsUpCount": 0, "name": "YUNG's <PERSON><PERSON> Tweaks (Forge/NeoForge)", "mainFileId": 6424568, "primaryCategoryId": 5191, "downloadCount": 12336679, "status": 4, "authors": [{"name": "YUNGNICKYOUNG", "id": 40421595, "url": "https://www.curseforge.com/members/yungnickyoung"}], "available": false, "featured": false}