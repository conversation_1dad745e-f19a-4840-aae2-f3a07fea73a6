{"allowModDistribution": true, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "embeddium-1.0.15+mc1.21.1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6118392}, {"filename": "embeddium-1.0.12-beta.9999+mc1.21.4.jar", "releaseType": 2, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6116910}, {"filename": "embeddium-0.3.31+mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5681725}, {"filename": "embeddium-0.3.31+mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5681725}, {"filename": "embeddium-1.0.7+mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5556146}, {"filename": "embeddium-0.3.18.1+mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5539178}, {"filename": "embeddium-fabric-0.3.25+mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5522040}, {"filename": "embeddium-0.3.25+mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5521869}, {"filename": "embeddium-1.0.0-beta.1+mc1.21.jar", "releaseType": 2, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5426046}, {"filename": "embeddium-fabric-0.3.20+mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5410245}, {"filename": "embeddium-0.3.20+mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5410236}, {"filename": "embeddium-0.3.18+mc1.16.5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 5322311}, {"filename": "embeddium-0.3.18+mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 5322305}, {"filename": "embeddium-fabric-0.3.15+mc1.20.5.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5288097}, {"filename": "embeddium-0.3.14+mc1.20.5.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5286000}, {"filename": "embeddium-fabric-0.3.14+mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5265349}, {"filename": "embeddium-0.2.12+mc1.20.3.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4934261}, {"filename": "embeddium-0.2.12+mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 4931291}, {"filename": "embeddium-0.2.10+mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4894293}], "dateCreated": "2023-09-03T05:58:24.46Z", "logo": {"description": "", "id": 893778, "title": "638336829931216743.png", "modId": 908741, "url": "https://media.forgecdn.net/avatars/893/778/638336829931216743.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/893/778/256/256/638336829931216743.png"}, "links": {"sourceUrl": "https://github.com/FiniteReality/embeddium", "issuesUrl": "https://github.com/FiniteReality/embeddium/issues", "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/embeddium", "wikiUrl": "https://github.com/FiniteReality/embeddium/wiki"}, "dateReleased": "2025-01-24T01:04:56.523Z", "id": 908741, "categories": [{"gameId": 432, "classId": 6, "name": "Performance", "dateModified": "2024-01-16T06:56:01.057Z", "parentCategoryId": 6, "id": 6814, "iconUrl": "https://media.forgecdn.net/avatars/933/987/638409849610531091.png", "slug": "performance", "url": "https://www.curseforge.com/minecraft/mc-mods/performance", "displayIndex": 0, "class": false}], "slug": "embeddium", "gameId": 432, "summary": "A powerful, mod-friendly, FOSS client performance mod for NeoForge", "latestFiles": [{"gameId": 432, "fileName": "embeddium-fabric-0.3.25+mc1.20.1.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.20.1"], "displayName": "[1.20.1] Embeddium-Fabric 0.3.25", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}], "downloadUrl": "https://edge.forgecdn.net/files/5522/40/embeddium-fabric-0.3.25%2bmc1.20.1.jar", "fileDate": "2024-07-11T02:20:57.607Z", "exposeAsAlternative": null, "modId": 908741, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "COPYING", "fingerprint": **********}, {"name": "COPYING.LESSER", "fingerprint": **********}, {"name": "README.md", "fingerprint": 859634317}, {"name": "assets", "fingerprint": **********}, {"name": "embeddium-refmap.json", "fingerprint": 770910756}, {"name": "embeddium.mixins.json", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": 318634973}, {"name": "icon.svg", "fingerprint": **********}, {"name": "licenses", "fingerprint": **********}, {"name": "me", "fingerprint": **********}, {"name": "net", "fingerprint": 893157440}, {"name": "org", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "sodium.accesswidener", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "9ff40c2c9c4d8c63e6c579f9918f1e5e78b6faef", "algo": 1}, {"value": "ce2a31ed552ae0d8aa0fc9aeed363aea", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5522040, "fileLength": 1414664, "downloadCount": 285204, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "embeddium-0.3.31+mc1.20.1.jar", "gameVersions": ["NeoForge", "1.20.1", "Forge"], "displayName": "[1.20.1] Embeddium 0.3.31", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/5681/725/embeddium-0.3.31%2bmc1.20.1.jar", "fileDate": "2024-08-31T23:11:49.240Z", "exposeAsAlternative": null, "modId": 908741, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "COPYING", "fingerprint": **********}, {"name": "COPYING.LESSER", "fingerprint": **********}, {"name": "README.md", "fingerprint": 859634317}, {"name": "assets", "fingerprint": 589792934}, {"name": "embeddium-refmap.json", "fingerprint": **********}, {"name": "embeddium.mixins.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": 318634973}, {"name": "icon.svg", "fingerprint": **********}, {"name": "licenses", "fingerprint": **********}, {"name": "me", "fingerprint": 952022123}, {"name": "net", "fingerprint": **********}, {"name": "org", "fingerprint": 942367315}, {"name": "pack.mcmeta", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "bb2fa8f3e493af16af9160d049f96c614a1faf2f", "algo": 1}, {"value": "1dfb2ee49ce9ad5d484ff3eea0d628b7", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5681725, "fileLength": 1320675, "downloadCount": 18937005, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "embeddium-1.0.12-beta.9999+mc1.21.4.jar", "gameVersions": ["1.21.4", "NeoForge"], "displayName": "[1.21.4] Embeddium 1.0.12-beta.1", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": "https://edge.forgecdn.net/files/6116/910/embeddium-1.0.12-beta.9999%2bmc1.21.4.jar", "fileDate": "2025-01-23T16:07:57.133Z", "exposeAsAlternative": null, "modId": 908741, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "me", "fingerprint": **********}, {"name": "org", "fingerprint": **********}, {"name": "icon.svg", "fingerprint": **********}, {"name": "assets", "fingerprint": 936418389}, {"name": "licenses", "fingerprint": **********}, {"name": "embeddium.mixins.json", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "COPYING", "fingerprint": **********}, {"name": "COPYING.LESSER", "fingerprint": **********}, {"name": "README.md", "fingerprint": 351447236}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "7eff48150c6e97f1284e90dfb17eb32217173303", "algo": 1}, {"value": "2a3ac1d53d5c65d9f96ffc44f514e04e", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6116910, "fileLength": 1043365, "downloadCount": 29513, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "embeddium-1.0.15+mc1.21.1.jar", "gameVersions": ["NeoForge", "1.21.1"], "displayName": "[1.21.1] Embeddium 1.0.15", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6118/392/embeddium-1.0.15%2bmc1.21.1.jar", "fileDate": "2025-01-24T01:04:56.523Z", "exposeAsAlternative": null, "modId": 908741, "modules": [{"name": "META-INF", "fingerprint": 508988495}, {"name": "org", "fingerprint": **********}, {"name": "me", "fingerprint": **********}, {"name": "licenses", "fingerprint": **********}, {"name": "assets", "fingerprint": 152724918}, {"name": "embeddium.mixins.json", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "icon.svg", "fingerprint": **********}, {"name": "COPYING", "fingerprint": **********}, {"name": "COPYING.LESSER", "fingerprint": **********}, {"name": "README.md", "fingerprint": 351447236}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "56063839172a000d079272794bad366f2ad4cbbd", "algo": 1}, {"value": "55d23daccc9bf63447a4cb10e3c21db1", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6118392, "fileLength": 1048745, "downloadCount": 1927996, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-01-24T01:08:26.3Z", "gamePopularityRank": 47, "thumbsUpCount": 0, "name": "Embeddium", "mainFileId": 6118392, "primaryCategoryId": 6814, "downloadCount": 86541465, "status": 4, "authors": [{"name": "FiniteReality", "id": 114509189, "url": "https://www.curseforge.com/members/finitereality"}], "available": false, "featured": false}