{"allowModDistribution": false, "screenshots": [], "classId": 6, "latestFilesIndexes": [{"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879708}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879708}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879708}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879707}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879707}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879707}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879706}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879706}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.6.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879706}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879705}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879703}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879702}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879701}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879700}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879699}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879698}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879697}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879696}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879695}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6879695}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879694}, {"filename": "skinlayers3d-forge-1.9.0-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6879694}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879693}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.21.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6879693}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6879692}, {"filename": "skinlayers3d-forge-1.9.0-mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6879691}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.20.6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6879690}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6879689}, {"filename": "skinlayers3d-forge-1.9.0-mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6879688}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.20.4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6879687}, {"filename": "skinlayers3d-neoforge-1.9.0-mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6879686}, {"filename": "skinlayers3d-forge-1.9.0-mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6879685}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.20.2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6879684}, {"filename": "skinlayers3d-forge-1.9.0-mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6879683}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6879681}, {"filename": "skinlayers3d-forge-1.9.0-mc1.19.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6879680}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.19.4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6879679}, {"filename": "skinlayers3d-forge-1.9.0-mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6879678}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.19.2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6879677}, {"filename": "skinlayers3d-forge-1.9.0-mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 6879675}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.16.5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 6879674}, {"filename": "skinlayers3d-fabric-1.9.0-mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 6879673}, {"filename": "skinlayers3d-forge-1.7.5-mc1.16.5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 6356161}, {"filename": "skinlayers3d-neoforge-1.7.4-mc1.21.4.jar", "releaseType": 2, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5970943}, {"filename": "skinlayers3d-forge-1.7.4-mc1.21.4.jar", "releaseType": 2, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5970942}, {"filename": "skinlayers3d-neoforge-1.7.4-mc1.21.3.jar", "releaseType": 2, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5970939}, {"filename": "skinlayers3d-forge-1.7.4-mc1.21.3.jar", "releaseType": 2, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5970938}, {"filename": "skinlayers3d-neoforge-1.7.4-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5970934}, {"filename": "skinlayers3d-neoforge-1.7.4-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 5970934}, {"filename": "skinlayers3d-forge-1.7.4-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5970933}, {"filename": "skinlayers3d-forge-1.7.4-mc1.21.jar", "releaseType": 2, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 5970933}, {"filename": "skinlayers3d-neoforge-1.7.4-mc1.20.6.jar", "releaseType": 2, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5970930}, {"filename": "skinlayers3d-forge-1.7.4-mc1.20.6.jar", "releaseType": 2, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5970929}, {"filename": "skinlayers3d-neoforge-1.7.4-mc1.20.4.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5970926}, {"filename": "skinlayers3d-forge-1.7.4-mc1.20.4.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5970924}, {"filename": "skinlayers3d-neoforge-1.7.4-mc1.20.2.jar", "releaseType": 2, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5970921}, {"filename": "skinlayers3d-forge-1.7.4-mc1.20.2.jar", "releaseType": 2, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5970919}, {"filename": "skinlayers3d-forge-1.7.4-mc1.20.1.jar", "releaseType": 2, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5970916}, {"filename": "skinlayers3d-forge-1.7.4-mc1.19.4.jar", "releaseType": 2, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5970913}, {"filename": "skinlayers3d-fabric-1.7.4-mc1.19.3.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 5970912}, {"filename": "skinlayers3d-forge-1.7.4-mc1.19.3.jar", "releaseType": 2, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5970910}, {"filename": "skinlayers3d-forge-1.7.4-mc1.19.2.jar", "releaseType": 2, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5970907}, {"filename": "skinlayers3d-forge-1.7.4-mc1.18.2.jar", "releaseType": 2, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 5970904}, {"filename": "skinlayers3d-fabric-1.7.4-mc1.17.1.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 5970903}, {"filename": "skinlayers3d-forge-1.7.4-mc1.17.1.jar", "releaseType": 2, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 5970901}, {"filename": "skinlayers3d-forge-1.7.4-mc1.16.5.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 5970894}, {"filename": "skinlayers3d-fabric-1.6.3-mc1.20.5.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5285224}, {"filename": "3dskinlayers-fabric-1.5.4-mc1.20.1.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 4630615}, {"filename": "3dskinlayers-forge-1.5.4-mc1.20.1.jar", "releaseType": 2, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 4630614}, {"filename": "3dskinlayers-fabric-1.5.2-mc1.19.1.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4001981}, {"filename": "3dskinlayers-fabric-1.5.2-mc1.19.1.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 4001981}, {"filename": "3dskinlayers-forge-1.5.2-mc1.19.1.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4001980}, {"filename": "3dskinlayers-forge-1.5.2-mc1.19.1.jar", "releaseType": 2, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 4001980}, {"filename": "3dskinlayers-fabric-1.5.2-mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 4001978}, {"filename": "3dskinlayers-fabric-1.5.2-mc1.18.2.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 4001978}, {"filename": "3dskinlayers-forge-1.5.2-mc1.18.2.jar", "releaseType": 2, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 4001976}, {"filename": "3dskinlayers-forge-1.5.2-mc1.18.2.jar", "releaseType": 2, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 4001976}, {"filename": "3dSkinLayers-forge-mc1.12.2-1.2.0.jar", "releaseType": 1, "gameVersion": "1.12.1", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 3779887}, {"filename": "3dSkinLayers-forge-mc1.12.2-1.2.0.jar", "releaseType": 1, "gameVersion": "1.12", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 3779887}, {"filename": "3dSkinLayers-forge-mc1.12.2-1.2.0.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 3779887}, {"filename": "3dSkinLayers-forge-mc1.8.9-1.2.0.jar", "releaseType": 1, "gameVersion": "1.8.8", "gameVersionTypeId": 4, "modLoader": 1, "fileId": 3779886}, {"filename": "3dSkinLayers-forge-mc1.8.9-1.2.0.jar", "releaseType": 1, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": 1, "fileId": 3779886}, {"filename": "3dSkinLayers-Fabric-1.4.3.jar", "releaseType": 1, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 3739535}, {"filename": "3dSkinLayers-forge-1.4.3.jar", "releaseType": 2, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 3739534}, {"filename": "3dSkinLayers-forge-mc1.8.9-1.0.0.jar", "releaseType": 2, "gameVersion": "1.8.8", "gameVersionTypeId": 4, "modLoader": 1, "fileId": 3634350}, {"filename": "3dSkinLayers-forge-mc1.8.9-1.0.0.jar", "releaseType": 2, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": 1, "fileId": 3634350}, {"filename": "3dSkinLayers-forge-1.1.0.jar", "releaseType": 2, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 3456570}, {"filename": "3dSkinLayers-forge-1.1.0.jar", "releaseType": 2, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 3456570}, {"filename": "3dSkinLayers-forge-1.1.0.jar", "releaseType": 2, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 3456570}, {"filename": "3dSkinLayers-Fabric-1.1.0.jar", "releaseType": 1, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 3456569}, {"filename": "3dSkinLayers-Fabric-1.1.0.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 3456569}, {"filename": "3dSkinLayers-Fabric-1.1.0.jar", "releaseType": 1, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 3456569}, {"filename": "3dSkinLayers-Fabric-1.1.0.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 3456513}], "dateCreated": "2021-08-29T11:23:02.697Z", "logo": {"description": "", "id": 426742, "title": "637657627491599401.png", "modId": 521480, "url": "https://media.forgecdn.net/avatars/426/742/637657627491599401.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/426/742/256/256/637657627491599401.png"}, "links": {"sourceUrl": "https://github.com/tr7zw/3d-Skin-Layers", "issuesUrl": null, "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/skin-layers-3d", "wikiUrl": ""}, "dateReleased": "2025-08-12T18:59:30.123Z", "id": 521480, "categories": [{"gameId": 432, "classId": 6, "name": "Adventure and RPG", "dateModified": "2014-05-08T17:42:09.54Z", "parentCategoryId": 6, "id": 422, "iconUrl": "https://media.forgecdn.net/avatars/6/37/635351497295252123.png", "slug": "adventure-rpg", "url": "https://www.curseforge.com/minecraft/mc-mods/adventure-rpg", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}], "slug": "skin-layers-3d", "gameId": 432, "summary": "Render the player skin layer in 3d!", "latestFiles": [{"gameId": 432, "fileName": "skinlayers3d-forge-1.7.4-mc1.21.4.jar", "gameVersions": ["1.21.4", "Forge"], "displayName": "1.7.4-1.21.4 - Forge", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}], "downloadUrl": null, "fileDate": "2024-12-07T13:05:50.947Z", "exposeAsAlternative": null, "modId": 521480, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": 772629976}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "skinlayers3d.mixins.json", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "768a980a1d86f6648757702a7586f156f45f802b", "algo": 1}, {"value": "2f96637454d0ffcf25f2a95e29c1a682", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5970942, "fileLength": 473532, "downloadCount": 133955, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "skinlayers3d-neoforge-1.7.4-mc1.21.4.jar", "gameVersions": ["1.21.4", "NeoForge"], "displayName": "1.7.4-1.21.4 - <PERSON><PERSON><PERSON><PERSON>", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}], "downloadUrl": null, "fileDate": "2024-12-07T13:06:01.337Z", "exposeAsAlternative": null, "modId": 521480, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": 400317814}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "skinlayers3d.mixins.json", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "3f881753b6ffb99a0c04e8a5af8b0ce264a52a2b", "algo": 1}, {"value": "8035cc895aba23c509a4743253045422", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5970943, "fileLength": 473863, "downloadCount": 160110, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "skinlayers3d-fabric-1.9.0-mc1.21.6.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.8", "1.21.7", "1.21.6"], "displayName": "1.9.0-1.21.6 - <PERSON><PERSON><PERSON>", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}], "downloadUrl": null, "fileDate": "2025-08-12T18:59:28.723Z", "exposeAsAlternative": null, "modId": 521480, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": 205160386}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "skinlayers3d.mixins.json", "fingerprint": **********}, {"name": "skinlayers3d.refmap.mixins.json", "fingerprint": **********}], "dependencies": [{"relationType": 3, "modId": 306612}], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "73d48e688d28d4f1a342177801822ef28eb88dda", "algo": 1}, {"value": "fd60042eb5ac96818e093441ea49355f", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6879706, "fileLength": 867372, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "skinlayers3d-forge-1.9.0-mc1.21.6.jar", "gameVersions": ["1.21.8", "Forge", "1.21.7", "1.21.6"], "displayName": "1.9.0-1.21.6 - Forge", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}], "downloadUrl": null, "fileDate": "2025-08-12T18:59:29.517Z", "exposeAsAlternative": null, "modId": 521480, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": 317406363}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "skinlayers3d.mixins.json", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "c7d20543bd68581e27ad42b279f625862a950630", "algo": 1}, {"value": "fa3e4b2c5a159a5c63b9f26e21231129", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6879707, "fileLength": 871906, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "skinlayers3d-neoforge-1.9.0-mc1.21.6.jar", "gameVersions": ["1.21.8", "NeoForge", "1.21.7", "1.21.6"], "displayName": "1.9.0-1.21.6 - <PERSON><PERSON><PERSON><PERSON>", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}], "downloadUrl": null, "fileDate": "2025-08-12T18:59:30.123Z", "exposeAsAlternative": null, "modId": 521480, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "dev", "fingerprint": **********}, {"name": "icon.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": **********}, {"name": "skinlayers3d.mixins.json", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "9c2236c0be32a29fd8f707afbe5fe21ccb5ec1fd", "algo": 1}, {"value": "8bb056d538d2f2dcf1e4897f35c033dd", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6879708, "fileLength": 871834, "downloadCount": 0, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-08-12T19:04:36.613Z", "gamePopularityRank": 119, "thumbsUpCount": 0, "name": "Skin Layers 3D", "mainFileId": 6879708, "primaryCategoryId": 424, "downloadCount": 72905158, "status": 4, "authors": [{"name": "tr7zw", "id": 100212189, "url": "https://www.curseforge.com/members/tr7zw"}], "available": false, "featured": false}