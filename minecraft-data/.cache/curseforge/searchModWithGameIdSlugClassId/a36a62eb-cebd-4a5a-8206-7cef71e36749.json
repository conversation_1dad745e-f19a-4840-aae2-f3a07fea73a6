{"allowModDistribution": true, "screenshots": [{"description": "End Portal Room", "id": 891398, "title": "End Portal Room", "modId": 915902, "url": "https://media.forgecdn.net/attachments/891/398/2024-06-17_01.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/891/398/310/172/2024-06-17_01.png"}, {"description": "Stylized Water", "id": 883845, "title": "Stylized Water", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/845/2024-05-22_20.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/845/310/172/2024-05-22_20.png"}, {"description": "Distant Horizons", "id": 883844, "title": "Distant Horizons", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/844/2024-05-14_20.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/844/310/172/2024-05-14_20.png"}, {"description": "End Crystal <PERSON>", "id": 883843, "title": "End Crystal <PERSON>", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/843/2024-05-09_10.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/843/310/172/2024-05-09_10.png"}, {"description": "Colored blocklight with the Multi-Colored Blocklight option", "id": 733101, "title": "Colored Lighting (again)", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/101/2023-09-03_20.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/101/310/172/2023-09-03_20.png"}, {"description": "Black and white halftone", "id": 733105, "title": "Halftone Effect", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/105/2023-08-27_22.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/105/310/172/2023-08-27_22.png"}, {"description": "Scanline option, one of many retro camera settings (screenshot by Candymanmax)\r\n\r\n", "id": 733114, "title": "Scanline Effect", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/114/2023-08-29_13.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/114/310/172/2023-08-29_13.png"}, {"description": "Makes all fire-related blocks and entities blue in the soul sand valley\r\n\r\n", "id": 733117, "title": "Soul Sand Valley Overhaul", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/117/2023-08-17_11.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/117/310/172/2023-08-17_11.png"}, {"description": "A screenshot of our winter feature\r\n\r\n", "id": 733115, "title": "Snow", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/115/5a2f2e1ad27bcdda9624ecf664a9831ebf7fea41.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/115/310/172/5a2f2e1ad27bcdda9624ecf664a9831ebf7fea41.png"}, {"description": "Makes the nether sky less boring (screenshot by <PERSON><PERSON>)\r\n\r\n", "id": 733113, "title": "Nether Sky Noise", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/113/2023-09-02_17.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/113/310/172/2023-09-02_17.png"}, {"description": "Tilt Shift", "id": 883840, "title": "Tilt Shift", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/840/2024-04-09_00.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/840/310/172/2024-04-09_00.png"}, {"description": "Intense Deep Dark option making sculk more emissive with the darkness effect (screenshot by minimiscus)\n\n", "id": 733107, "title": "Intense Deep Dark", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/107/image.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/107/310/172/image.png"}, {"description": "Colored lighting with the Multi-Colored Blocklight option", "id": 733102, "title": "Colored Lighting Glass", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/102/2023-09-10_14.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/102/310/172/2023-09-10_14.png"}, {"description": "End Crystal <PERSON>", "id": 883838, "title": "End Crystal <PERSON>", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/838/2024-04-29_16.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/838/310/172/2024-04-29_16.png"}, {"description": "A stone statue with our moss floor noise enabled (screenshot by <PERSON><PERSON><PERSON><PERSON>)\r\n\r\n", "id": 733110, "title": "Moss Giant", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/110/moss_giant.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/110/310/172/moss_giant.png"}, {"description": "<PERSON>va <PERSON>", "id": 883842, "title": "<PERSON>va <PERSON>", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/842/2024-04-14_13.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/842/310/172/2024-04-14_13.png"}, {"description": "Long Exposure", "id": 883837, "title": "Long Exposure", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/837/2024-04-03_21.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/837/310/172/2024-04-03_21.png"}, {"description": "With Euphoria Patches the Complementary End is fully customizable (screenshot by <PERSON><PERSON>)", "id": 733104, "title": "Customizable End", "modId": 915902, "url": "https://media.forgecdn.net/attachments/733/104/2022-11-29_13.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/733/104/310/172/2022-11-29_13.png"}, {"description": "Tilt Shift", "id": 883841, "title": "Tilt Shift", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/841/2024-04-10_13.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/841/310/172/2024-04-10_13.png"}, {"description": "Long Exposure", "id": 883839, "title": "Long Exposure", "modId": 915902, "url": "https://media.forgecdn.net/attachments/883/839/2024-04-03_21.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/883/839/310/172/2024-04-03_21.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.1", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.1", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.3", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.3", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.2", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.14.2", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 5, "fileId": 6987510}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.13.1", "gameVersionTypeId": 55023, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.14.1", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.14.3", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.13", "gameVersionTypeId": 55023, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.14", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.14.2", "gameVersionTypeId": 64806, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.13.2", "gameVersionTypeId": 55023, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 6987509}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "releaseType": 1, "gameVersion": "1.21.9", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 6987503}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge1.20.1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 6987500}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.9.1", "gameVersionTypeId": 552, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.9.3", "gameVersionTypeId": 552, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.9.4", "gameVersionTypeId": 552, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.9.2", "gameVersionTypeId": 552, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.12.1", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.12", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.10.2", "gameVersionTypeId": 572, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.11", "gameVersionTypeId": 599, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.10", "gameVersionTypeId": 572, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.10.1", "gameVersionTypeId": 572, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.11.1", "gameVersionTypeId": 599, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.11.2", "gameVersionTypeId": 599, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.9", "gameVersionTypeId": 552, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forgeLegacy.jar", "releaseType": 1, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": 1, "fileId": 6987498}, {"filename": "EuphoriaPatcher-1.6.8-r5.5.1-forge1.7.10.jar", "releaseType": 1, "gameVersion": "1.7.10", "gameVersionTypeId": 5, "modLoader": 1, "fileId": 6987497}, {"filename": "EuphoriaPatcher-0.3.4-neoforge.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5511961}, {"filename": "EuphoriaPatcher-0.3.4-neoforge.jar", "releaseType": 1, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5511961}, {"filename": "EuphoriaPatcher-0.3.4-neoforge.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 5511961}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-forge.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 5016112}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.3", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.1", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.5", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.4", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.1", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.1", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.17", "gameVersionTypeId": 73242, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.3", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.3", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.15", "gameVersionTypeId": 68722, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.3", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.15.2", "gameVersionTypeId": 68722, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.4", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.2", "gameVersionTypeId": 64806, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.14.2", "gameVersionTypeId": 64806, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.16.2", "gameVersionTypeId": 70886, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 5, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 4, "fileId": 5016102}, {"filename": "EuphoriaPatcher-0.3.0-fabric.jar", "releaseType": 2, "gameVersion": "1.15.1", "gameVersionTypeId": 68722, "modLoader": 5, "fileId": 5016102}], "dateCreated": "2023-09-26T07:45:10.983Z", "logo": {"description": "", "id": 1198673, "title": "638774916393051591.png", "modId": 915902, "url": "https://media.forgecdn.net/avatars/1198/673/638774916393051591.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1198/673/256/256/638774916393051591.png"}, "links": {"sourceUrl": "https://github.com/EuphoriaPatches/EuphoriaPatcher", "issuesUrl": null, "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/euphoria-patches", "wikiUrl": "https://euphoriapatches.com"}, "dateReleased": "2025-09-11T21:37:00.937Z", "id": 915902, "categories": [{"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "parentCategoryId": 6, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Addons", "dateModified": "2014-05-08T17:09:48.63Z", "parentCategoryId": 6, "id": 426, "iconUrl": "https://media.forgecdn.net/avatars/5/998/635351477886290676.png", "slug": "mc-addons", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-addons", "displayIndex": 0, "class": false}], "slug": "euphoria-patches", "gameId": 432, "summary": "Euphoria Patches is an add-on for Complementary Shaders, extending it with more optional features and settings.", "latestFiles": [{"gameId": 432, "fileName": "EuphoriaPatcher-0.3.0-fabric.jar", "gameVersions": ["1.17.1", "1.16.3", "1.19.3", "1.20.2", "1.16.1", "1.18.1", "1.20.5", "1.14.4", "1.14.1", "1.17", "1.14.3", "1.15", "1.16", "1.19.4", "<PERSON><PERSON><PERSON>", "Client", "1.16.5", "1.18.2", "1.19.2", "1.20.3", "1.15.2", "1.14", "1.20.1", "1.20", "1.20.6", "1.19.1", "1.20.4", "1.18", "1.16.4", "1.14.2", "1.16.2", "Quilt", "1.19", "1.15.1"], "displayName": "Euphoria <PERSON> 0.3.0-fabric", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000017.**********", "gameVersion": "1.17.1", "gameVersionReleaseDate": "2021-07-06T14:16:03.97Z", "gameVersionName": "1.17.1", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000016.0000000003", "gameVersion": "1.16.3", "gameVersionReleaseDate": "2020-09-10T14:44:20.443Z", "gameVersionName": "1.16.3", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000019.0000000003", "gameVersion": "1.19.3", "gameVersionReleaseDate": "2022-10-19T00:00:00Z", "gameVersionName": "1.19.3", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000002", "gameVersion": "1.20.2", "gameVersionReleaseDate": "2023-09-21T15:25:15.053Z", "gameVersionName": "1.20.2", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000016.**********", "gameVersion": "1.16.1", "gameVersionReleaseDate": "2020-06-24T12:41:11.823Z", "gameVersionName": "1.16.1", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000018.**********", "gameVersion": "1.18.1", "gameVersionReleaseDate": "2021-12-10T10:37:38.06Z", "gameVersionName": "1.18.1", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000020.0000000005", "gameVersion": "1.20.5", "gameVersionReleaseDate": "2024-04-23T15:00:48.803Z", "gameVersionName": "1.20.5", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000014.0000000004", "gameVersion": "1.14.4", "gameVersionReleaseDate": "2019-07-19T00:00:00Z", "gameVersionName": "1.14.4", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000014.**********", "gameVersion": "1.14.1", "gameVersionReleaseDate": "2019-05-13T00:00:00Z", "gameVersionName": "1.14.1", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000017", "gameVersion": "1.17", "gameVersionReleaseDate": "2020-11-12T00:00:00Z", "gameVersionName": "1.17", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000014.0000000003", "gameVersion": "1.14.3", "gameVersionReleaseDate": "2019-06-24T00:00:00Z", "gameVersionName": "1.14.3", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.15", "gameVersionReleaseDate": "2019-12-10T15:26:51.027Z", "gameVersionName": "1.15", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000016", "gameVersion": "1.16", "gameVersionReleaseDate": "2020-06-23T13:41:08.75Z", "gameVersionName": "1.16", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000019.0000000004", "gameVersion": "1.19.4", "gameVersionReleaseDate": "2023-03-09T00:00:00Z", "gameVersionName": "1.19.4", "gameVersionTypeId": 73407}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "**********.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000018.0000000002", "gameVersion": "1.18.2", "gameVersionReleaseDate": "2022-02-28T14:23:37.723Z", "gameVersionName": "1.18.2", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000003", "gameVersion": "1.20.3", "gameVersionReleaseDate": "2023-12-05T17:24:10.113Z", "gameVersionName": "1.20.3", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.15.2", "gameVersionReleaseDate": "2020-01-22T00:00:00Z", "gameVersionName": "1.15.2", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000014", "gameVersion": "1.14", "gameVersionReleaseDate": "2019-04-23T00:00:00Z", "gameVersionName": "1.14", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020", "gameVersion": "1.20", "gameVersionReleaseDate": "2023-06-07T00:00:00Z", "gameVersionName": "1.20", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000006", "gameVersion": "1.20.6", "gameVersionReleaseDate": "2024-04-29T15:15:44.67Z", "gameVersionName": "1.20.6", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000019.**********", "gameVersion": "1.19.1", "gameVersionReleaseDate": "2022-06-28T00:00:00Z", "gameVersionName": "1.19.1", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000004", "gameVersion": "1.20.4", "gameVersionReleaseDate": "2023-12-07T15:17:47.907Z", "gameVersionName": "1.20.4", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000018", "gameVersion": "1.18", "gameVersionReleaseDate": "2021-11-30T16:23:01.427Z", "gameVersionName": "1.18", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000016.0000000004", "gameVersion": "1.16.4", "gameVersionReleaseDate": "2020-11-02T18:40:51.49Z", "gameVersionName": "1.16.4", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000014.0000000002", "gameVersion": "1.14.2", "gameVersionReleaseDate": "2019-05-27T00:00:00Z", "gameVersionName": "1.14.2", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000016.0000000002", "gameVersion": "1.16.2", "gameVersionReleaseDate": "2020-08-11T16:42:21.863Z", "gameVersionName": "1.16.2", "gameVersionTypeId": 70886}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000019", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-06-07T15:38:07.377Z", "gameVersionName": "1.19", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.15.1", "gameVersionReleaseDate": "2019-12-17T00:00:00Z", "gameVersionName": "1.15.1", "gameVersionTypeId": 68722}], "downloadUrl": "https://edge.forgecdn.net/files/5016/102/EuphoriaPatcher-0.3.0-fabric.jar", "fileDate": "2024-01-08T00:14:02.340Z", "exposeAsAlternative": null, "modId": 915902, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "LICENSE_EuphoriaPatcher", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "EuphoriaPatches_1.2.patch", "fingerprint": **********}, {"name": "de", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "c2c47f5da0a29fc7cfbacad7f20bc4e157e25456", "algo": 1}, {"value": "9dbcce58e8a50a0e335a1c5bb4fc7f6c", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5016102, "fileLength": 297661, "downloadCount": 2547108, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "EuphoriaPatcher-0.3.0-forge.jar", "gameVersions": ["1.19.3", "1.20.2", "1.20.5", "1.19.4", "Client", "1.18.2", "1.19.2", "1.20.3", "1.20.1", "Forge", "1.20", "1.20.6", "1.19.1", "1.20.4", "1.19"], "displayName": "<PERSON><PERSON><PERSON><PERSON> 0.3.0-forge", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000019.0000000003", "gameVersion": "1.19.3", "gameVersionReleaseDate": "2022-10-19T00:00:00Z", "gameVersionName": "1.19.3", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000002", "gameVersion": "1.20.2", "gameVersionReleaseDate": "2023-09-21T15:25:15.053Z", "gameVersionName": "1.20.2", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000005", "gameVersion": "1.20.5", "gameVersionReleaseDate": "2024-04-23T15:00:48.803Z", "gameVersionName": "1.20.5", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000019.0000000004", "gameVersion": "1.19.4", "gameVersionReleaseDate": "2023-03-09T00:00:00Z", "gameVersionName": "1.19.4", "gameVersionTypeId": 73407}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client", "gameVersionTypeId": 75208}, {"gameVersionPadded": "**********.0000000018.0000000002", "gameVersion": "1.18.2", "gameVersionReleaseDate": "2022-02-28T14:23:37.723Z", "gameVersionName": "1.18.2", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000003", "gameVersion": "1.20.3", "gameVersionReleaseDate": "2023-12-05T17:24:10.113Z", "gameVersionName": "1.20.3", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000020", "gameVersion": "1.20", "gameVersionReleaseDate": "2023-06-07T00:00:00Z", "gameVersionName": "1.20", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000006", "gameVersion": "1.20.6", "gameVersionReleaseDate": "2024-04-29T15:15:44.67Z", "gameVersionName": "1.20.6", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000019.**********", "gameVersion": "1.19.1", "gameVersionReleaseDate": "2022-06-28T00:00:00Z", "gameVersionName": "1.19.1", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000004", "gameVersion": "1.20.4", "gameVersionReleaseDate": "2023-12-07T15:17:47.907Z", "gameVersionName": "1.20.4", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000019", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-06-07T15:38:07.377Z", "gameVersionName": "1.19", "gameVersionTypeId": 73407}], "downloadUrl": "https://edge.forgecdn.net/files/5016/112/EuphoriaPatcher-0.3.0-forge.jar", "fileDate": "2024-01-08T00:17:13.897Z", "exposeAsAlternative": null, "modId": 915902, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "de", "fingerprint": **********}, {"name": "EuphoriaPatches_1.2.patch", "fingerprint": **********}, {"name": "icon.jpg", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": 5613916}, {"name": "commons-compress-LICENSE.txt", "fingerprint": **********}, {"name": "io", "fingerprint": **********}, {"name": "jbsdiff-LICENSE.txt", "fingerprint": **********}], "dependencies": [], "fileFingerprint": 413100579, "fileStatus": 4, "releaseType": 2, "hashes": [{"value": "0e7fe92767d397fc724b130ac9ac5036d2626d34", "algo": 1}, {"value": "068d6f548845909662b78f05bad0bc4f", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 5016112, "fileLength": 293936, "downloadCount": 2302273, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "gameVersions": ["1.21.2-<PERSON><PERSON><PERSON>", "1.21", "1.21.6-snapshot", "1.20.5", "1.21.5", "1.21.8", "1.21.1-<PERSON><PERSON><PERSON>", "1.21.9", "1.21.4", "1.21.5-Snap<PERSON>", "NeoForge", "1.20.6", "1.21-<PERSON><PERSON><PERSON>", "1.21.3", "1.21.7", "1.21.1", "1.21.6", "1.21.4-<PERSON><PERSON><PERSON>", "1.21.2", "1.21.9-snapshot"], "displayName": "EuphoriaPatcher-1.6.8-r5.5.1-neoforge", "sortableGameVersions": [{"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.21.2", "gameVersionReleaseDate": "2024-08-16T00:00:00Z", "gameVersionName": "1.21.2-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000006", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-04-09T00:00:00Z", "gameVersionName": "1.21.6-snapshot", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000020.0000000005", "gameVersion": "1.20.5", "gameVersionReleaseDate": "2024-04-23T15:00:48.803Z", "gameVersionName": "1.20.5", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000008", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-07T00:00:00Z", "gameVersionName": "1.21.1-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-01-10T00:00:00Z", "gameVersionName": "1.21.5-Snap<PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000020.0000000006", "gameVersion": "1.20.6", "gameVersionReleaseDate": "2024-04-29T15:15:44.67Z", "gameVersionName": "1.20.6", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-05-04T00:00:00Z", "gameVersionName": "1.21-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000003", "gameVersion": "1.21.3", "gameVersionReleaseDate": "2024-10-23T13:16:45.71Z", "gameVersionName": "1.21.3", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000006", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-11-01T00:00:00Z", "gameVersionName": "1.21.4-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.21.2", "gameVersionReleaseDate": "2024-10-22T00:00:00Z", "gameVersionName": "1.21.2", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-07-29T00:00:00Z", "gameVersionName": "1.21.9-snapshot", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6987/503/EuphoriaPatcher-1.6.8-r5.5.1-neoforge.jar", "fileDate": "2025-09-11T21:34:36.097Z", "exposeAsAlternative": null, "modId": 915902, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "mc", "fingerprint": **********}, {"name": "EuphoriaPatches_1.6.8.patch", "fingerprint": **********}, {"name": "euphoria_patcher.mixins.json", "fingerprint": 820872701}, {"name": "icon32x.png", "fingerprint": **********}, {"name": "logo.png", "fingerprint": **********}, {"name": "pack.mcmeta", "fingerprint": 24391317}, {"name": "randomUtil.json", "fingerprint": **********}, {"name": "settingsConversions.txt", "fingerprint": **********}, {"name": "commons-compress-LICENSE.txt", "fingerprint": **********}, {"name": "jbsdiff-LICENSE.txt", "fingerprint": **********}, {"name": "LICENSE_EuphoriaPatcher.txt", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "6d87073b15c95e1adf91a0ff4a79293fb4465162", "algo": 1}, {"value": "314d6a73a5b95c4b4522a288c4463c21", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6987503, "fileLength": 2042409, "downloadCount": 355700, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "gameVersions": ["1.16-<PERSON><PERSON><PERSON>", "1.17.1", "1.16.3", "1.13.1", "1.19.3", "1.20.2", "1.21.2-<PERSON><PERSON><PERSON>", "1.21", "1.21.6-snapshot", "1.15-<PERSON><PERSON><PERSON>", "1.16.1", "1.18.1", "1.19.4-<PERSON><PERSON><PERSON>", "1.20.5", "1.21.5", "1.14.4", "1.14.1", "1.17", "1.20.2-<PERSON>nap<PERSON>", "1.20.5-Snap<PERSON>", "1.14.3", "1.15", "1.16", "1.13", "1.19.4", "1.21.8", "1.19-<PERSON><PERSON><PERSON>", "1.21.1-<PERSON><PERSON><PERSON>", "1.21.9", "1.16.5", "1.21.4", "1.21.5-Snap<PERSON>", "1.18.2", "1.19.2", "1.20.3-<PERSON><PERSON><PERSON>", "1.20.3", "1.15.2", "1.14", "1.20.1", "Forge", "1.20", "1.20.6", "1.19.1", "1.20.4", "1.21-<PERSON><PERSON><PERSON>", "1.21.3", "1.21.7", "1.20-<PERSON><PERSON><PERSON>", "1.21.1", "1.21.6", "1.17-<PERSON><PERSON><PERSON>", "1.18", "1.16.4", "1.14.2", "1.18-<PERSON><PERSON><PERSON>", "1.16.2", "1.19", "1.21.4-<PERSON><PERSON><PERSON>", "1.14-<PERSON><PERSON><PERSON>", "1.13.2", "1.15.1", "1.19.3-<PERSON><PERSON><PERSON>", "1.21.2", "1.21.9-snapshot"], "displayName": "EuphoriaPatcher-1.6.8-r5.5.1-forge", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000016", "gameVersion": "1.16", "gameVersionReleaseDate": "2020-02-06T00:00:00Z", "gameVersionName": "1.16-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000017.**********", "gameVersion": "1.17.1", "gameVersionReleaseDate": "2021-07-06T14:16:03.97Z", "gameVersionName": "1.17.1", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000016.0000000003", "gameVersion": "1.16.3", "gameVersionReleaseDate": "2020-09-10T14:44:20.443Z", "gameVersionName": "1.16.3", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000013.**********", "gameVersion": "1.13.1", "gameVersionReleaseDate": "2018-08-22T00:00:00Z", "gameVersionName": "1.13.1", "gameVersionTypeId": 55023}, {"gameVersionPadded": "**********.0000000019.0000000003", "gameVersion": "1.19.3", "gameVersionReleaseDate": "2022-10-19T00:00:00Z", "gameVersionName": "1.19.3", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000002", "gameVersion": "1.20.2", "gameVersionReleaseDate": "2023-09-21T15:25:15.053Z", "gameVersionName": "1.20.2", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.21.2", "gameVersionReleaseDate": "2024-08-16T00:00:00Z", "gameVersionName": "1.21.2-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000006", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-04-09T00:00:00Z", "gameVersionName": "1.21.6-snapshot", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.15", "gameVersionReleaseDate": "2019-08-22T00:00:00Z", "gameVersionName": "1.15-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000016.**********", "gameVersion": "1.16.1", "gameVersionReleaseDate": "2020-06-24T12:41:11.823Z", "gameVersionName": "1.16.1", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000018.**********", "gameVersion": "1.18.1", "gameVersionReleaseDate": "2021-12-10T10:37:38.06Z", "gameVersionName": "1.18.1", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000019.0000000004", "gameVersion": "1.19.4", "gameVersionReleaseDate": "2023-04-19T00:00:00Z", "gameVersionName": "1.19.4-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000005", "gameVersion": "1.20.5", "gameVersionReleaseDate": "2024-04-23T15:00:48.803Z", "gameVersionName": "1.20.5", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000014.0000000004", "gameVersion": "1.14.4", "gameVersionReleaseDate": "2019-07-19T00:00:00Z", "gameVersionName": "1.14.4", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000014.**********", "gameVersion": "1.14.1", "gameVersionReleaseDate": "2019-05-13T00:00:00Z", "gameVersionName": "1.14.1", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000017", "gameVersion": "1.17", "gameVersionReleaseDate": "2020-11-12T00:00:00Z", "gameVersionName": "1.17", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000020.0000000002", "gameVersion": "1.20.2", "gameVersionReleaseDate": "2023-08-06T00:00:00Z", "gameVersionName": "1.20.2-<PERSON>nap<PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000005", "gameVersion": "1.20.5", "gameVersionReleaseDate": "2023-12-18T00:00:00Z", "gameVersionName": "1.20.5-Snap<PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000014.0000000003", "gameVersion": "1.14.3", "gameVersionReleaseDate": "2019-06-24T00:00:00Z", "gameVersionName": "1.14.3", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.15", "gameVersionReleaseDate": "2019-12-10T15:26:51.027Z", "gameVersionName": "1.15", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000016", "gameVersion": "1.16", "gameVersionReleaseDate": "2020-06-23T13:41:08.75Z", "gameVersionName": "1.16", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000013", "gameVersion": "1.13", "gameVersionReleaseDate": "2018-07-18T00:00:00Z", "gameVersionName": "1.13", "gameVersionTypeId": 55023}, {"gameVersionPadded": "**********.0000000019.0000000004", "gameVersion": "1.19.4", "gameVersionReleaseDate": "2023-03-09T00:00:00Z", "gameVersionName": "1.19.4", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.0000000008", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000019", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-02-21T00:00:00Z", "gameVersionName": "1.19-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-07T00:00:00Z", "gameVersionName": "1.21.1-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.**********.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-01-10T00:00:00Z", "gameVersionName": "1.21.5-Snap<PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000018.0000000002", "gameVersion": "1.18.2", "gameVersionReleaseDate": "2022-02-28T14:23:37.723Z", "gameVersionName": "1.18.2", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000003", "gameVersion": "1.20.3", "gameVersionReleaseDate": "2023-10-05T00:00:00Z", "gameVersionName": "1.20.3-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000003", "gameVersion": "1.20.3", "gameVersionReleaseDate": "2023-12-05T17:24:10.113Z", "gameVersionName": "1.20.3", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.15.2", "gameVersionReleaseDate": "2020-01-22T00:00:00Z", "gameVersionName": "1.15.2", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000014", "gameVersion": "1.14", "gameVersionReleaseDate": "2019-04-23T00:00:00Z", "gameVersionName": "1.14", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000020", "gameVersion": "1.20", "gameVersionReleaseDate": "2023-06-07T00:00:00Z", "gameVersionName": "1.20", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000006", "gameVersion": "1.20.6", "gameVersionReleaseDate": "2024-04-29T15:15:44.67Z", "gameVersionName": "1.20.6", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000019.**********", "gameVersion": "1.19.1", "gameVersionReleaseDate": "2022-06-28T00:00:00Z", "gameVersionName": "1.19.1", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000004", "gameVersion": "1.20.4", "gameVersionReleaseDate": "2023-12-07T15:17:47.907Z", "gameVersionName": "1.20.4", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-05-04T00:00:00Z", "gameVersionName": "1.21-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000003", "gameVersion": "1.21.3", "gameVersionReleaseDate": "2024-10-23T13:16:45.71Z", "gameVersionName": "1.21.3", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000020", "gameVersion": "1.20", "gameVersionReleaseDate": "2022-10-27T00:00:00Z", "gameVersionName": "1.20-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000006", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000017", "gameVersion": "1.17", "gameVersionReleaseDate": "2021-03-18T00:00:00Z", "gameVersionName": "1.17-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000018", "gameVersion": "1.18", "gameVersionReleaseDate": "2021-11-30T16:23:01.427Z", "gameVersionName": "1.18", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000016.0000000004", "gameVersion": "1.16.4", "gameVersionReleaseDate": "2020-11-02T18:40:51.49Z", "gameVersionName": "1.16.4", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000014.0000000002", "gameVersion": "1.14.2", "gameVersionReleaseDate": "2019-05-27T00:00:00Z", "gameVersionName": "1.14.2", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000018", "gameVersion": "1.18", "gameVersionReleaseDate": "2021-08-12T00:00:00Z", "gameVersionName": "1.18-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000016.0000000002", "gameVersion": "1.16.2", "gameVersionReleaseDate": "2020-08-11T16:42:21.863Z", "gameVersionName": "1.16.2", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000019", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-06-07T15:38:07.377Z", "gameVersionName": "1.19", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-11-01T00:00:00Z", "gameVersionName": "1.21.4-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000014", "gameVersion": "1.14", "gameVersionReleaseDate": "2018-10-24T00:00:00Z", "gameVersionName": "1.14-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000013.0000000002", "gameVersion": "1.13.2", "gameVersionReleaseDate": "2018-10-22T00:00:00Z", "gameVersionName": "1.13.2", "gameVersionTypeId": 55023}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.15.1", "gameVersionReleaseDate": "2019-12-17T00:00:00Z", "gameVersionName": "1.15.1", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000019.0000000003", "gameVersion": "1.19.3", "gameVersionReleaseDate": "2023-01-01T00:00:00Z", "gameVersionName": "1.19.3-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.21.2", "gameVersionReleaseDate": "2024-10-22T00:00:00Z", "gameVersionName": "1.21.2", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-07-29T00:00:00Z", "gameVersionName": "1.21.9-snapshot", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6987/509/EuphoriaPatcher-1.6.8-r5.5.1-forge.jar", "fileDate": "2025-09-11T21:36:04.783Z", "exposeAsAlternative": null, "modId": 915902, "modules": [{"name": "META-INF", "fingerprint": **********}, {"name": "EuphoriaPatches_1.6.8.patch", "fingerprint": **********}, {"name": "icon32x.png", "fingerprint": **********}, {"name": "logo.png", "fingerprint": **********}, {"name": "mc", "fingerprint": 208608260}, {"name": "pack.mcmeta", "fingerprint": 5613916}, {"name": "randomUtil.json", "fingerprint": **********}, {"name": "settingsConversions.txt", "fingerprint": **********}, {"name": "commons-compress-LICENSE.txt", "fingerprint": **********}, {"name": "jbsdiff-LICENSE.txt", "fingerprint": **********}, {"name": "LICENSE_EuphoriaPatcher.txt", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "a9b749db7f2196f06043a86191d6961f2636f52f", "algo": 1}, {"value": "dda777d943b210008972f94b1f6edfd3", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6987509, "fileLength": 2015459, "downloadCount": 387919, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "gameVersions": ["1.16-<PERSON><PERSON><PERSON>", "1.17.1", "1.16.3", "1.19.3", "1.20.2", "1.21.2-<PERSON><PERSON><PERSON>", "1.21", "1.21.6-snapshot", "1.15-<PERSON><PERSON><PERSON>", "1.16.1", "1.18.1", "1.19.4-<PERSON><PERSON><PERSON>", "1.20.5", "1.21.5", "1.14.4", "1.14.1", "1.17", "1.20.2-<PERSON>nap<PERSON>", "1.20.5-Snap<PERSON>", "1.14.3", "1.15", "1.16", "1.19.4", "<PERSON><PERSON><PERSON>", "1.21.8", "1.19-<PERSON><PERSON><PERSON>", "1.21.1-<PERSON><PERSON><PERSON>", "1.21.9", "1.16.5", "1.21.4", "1.21.5-Snap<PERSON>", "1.18.2", "1.19.2", "1.20.3-<PERSON><PERSON><PERSON>", "1.20.3", "1.15.2", "1.14", "1.20.1", "1.20", "1.20.6", "1.19.1", "1.20.4", "1.21-<PERSON><PERSON><PERSON>", "1.21.3", "1.21.7", "1.20-<PERSON><PERSON><PERSON>", "1.21.1", "1.21.6", "1.17-<PERSON><PERSON><PERSON>", "1.18", "1.16.4", "1.14.2", "1.16.2", "Quilt", "1.19", "1.21.4-<PERSON><PERSON><PERSON>", "1.15.1", "1.19.3-<PERSON><PERSON><PERSON>", "1.21.2", "1.21.9-snapshot"], "displayName": "EuphoriaPatcher-1.6.8-r5.5.1-fabric", "sortableGameVersions": [{"gameVersionPadded": "**********.0000000016", "gameVersion": "1.16", "gameVersionReleaseDate": "2020-02-06T00:00:00Z", "gameVersionName": "1.16-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000017.**********", "gameVersion": "1.17.1", "gameVersionReleaseDate": "2021-07-06T14:16:03.97Z", "gameVersionName": "1.17.1", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000016.0000000003", "gameVersion": "1.16.3", "gameVersionReleaseDate": "2020-09-10T14:44:20.443Z", "gameVersionName": "1.16.3", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000019.0000000003", "gameVersion": "1.19.3", "gameVersionReleaseDate": "2022-10-19T00:00:00Z", "gameVersionName": "1.19.3", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000002", "gameVersion": "1.20.2", "gameVersionReleaseDate": "2023-09-21T15:25:15.053Z", "gameVersionName": "1.20.2", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.21.2", "gameVersionReleaseDate": "2024-08-16T00:00:00Z", "gameVersionName": "1.21.2-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000006", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-04-09T00:00:00Z", "gameVersionName": "1.21.6-snapshot", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.15", "gameVersionReleaseDate": "2019-08-22T00:00:00Z", "gameVersionName": "1.15-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000016.**********", "gameVersion": "1.16.1", "gameVersionReleaseDate": "2020-06-24T12:41:11.823Z", "gameVersionName": "1.16.1", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000018.**********", "gameVersion": "1.18.1", "gameVersionReleaseDate": "2021-12-10T10:37:38.06Z", "gameVersionName": "1.18.1", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000019.0000000004", "gameVersion": "1.19.4", "gameVersionReleaseDate": "2023-04-19T00:00:00Z", "gameVersionName": "1.19.4-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000005", "gameVersion": "1.20.5", "gameVersionReleaseDate": "2024-04-23T15:00:48.803Z", "gameVersionName": "1.20.5", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-03-25T16:46:25.94Z", "gameVersionName": "1.21.5", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000014.0000000004", "gameVersion": "1.14.4", "gameVersionReleaseDate": "2019-07-19T00:00:00Z", "gameVersionName": "1.14.4", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000014.**********", "gameVersion": "1.14.1", "gameVersionReleaseDate": "2019-05-13T00:00:00Z", "gameVersionName": "1.14.1", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000017", "gameVersion": "1.17", "gameVersionReleaseDate": "2020-11-12T00:00:00Z", "gameVersionName": "1.17", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000020.0000000002", "gameVersion": "1.20.2", "gameVersionReleaseDate": "2023-08-06T00:00:00Z", "gameVersionName": "1.20.2-<PERSON>nap<PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000005", "gameVersion": "1.20.5", "gameVersionReleaseDate": "2023-12-18T00:00:00Z", "gameVersionName": "1.20.5-Snap<PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000014.0000000003", "gameVersion": "1.14.3", "gameVersionReleaseDate": "2019-06-24T00:00:00Z", "gameVersionName": "1.14.3", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.15", "gameVersionReleaseDate": "2019-12-10T15:26:51.027Z", "gameVersionName": "1.15", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000016", "gameVersion": "1.16", "gameVersionReleaseDate": "2020-06-23T13:41:08.75Z", "gameVersionName": "1.16", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000019.0000000004", "gameVersion": "1.19.4", "gameVersionReleaseDate": "2023-03-09T00:00:00Z", "gameVersionName": "1.19.4", "gameVersionTypeId": 73407}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.**********.0000000008", "gameVersion": "1.21.8", "gameVersionReleaseDate": "2025-07-17T14:01:29.007Z", "gameVersionName": "1.21.8", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000019", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-02-21T00:00:00Z", "gameVersionName": "1.19-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-07T00:00:00Z", "gameVersionName": "1.21.1-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-09-30T15:50:17.583Z", "gameVersionName": "1.21.9", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.**********.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-12-03T16:26:15.35Z", "gameVersionName": "1.21.4", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000005", "gameVersion": "1.21.5", "gameVersionReleaseDate": "2025-01-10T00:00:00Z", "gameVersionName": "1.21.5-Snap<PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000018.0000000002", "gameVersion": "1.18.2", "gameVersionReleaseDate": "2022-02-28T14:23:37.723Z", "gameVersionName": "1.18.2", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000019.0000000002", "gameVersion": "1.19.2", "gameVersionReleaseDate": "2022-08-05T14:12:22.413Z", "gameVersionName": "1.19.2", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000003", "gameVersion": "1.20.3", "gameVersionReleaseDate": "2023-10-05T00:00:00Z", "gameVersionName": "1.20.3-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000003", "gameVersion": "1.20.3", "gameVersionReleaseDate": "2023-12-05T17:24:10.113Z", "gameVersionName": "1.20.3", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.15.2", "gameVersionReleaseDate": "2020-01-22T00:00:00Z", "gameVersionName": "1.15.2", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000014", "gameVersion": "1.14", "gameVersionReleaseDate": "2019-04-23T00:00:00Z", "gameVersionName": "1.14", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000020.**********", "gameVersion": "1.20.1", "gameVersionReleaseDate": "2023-06-12T14:26:38.477Z", "gameVersionName": "1.20.1", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020", "gameVersion": "1.20", "gameVersionReleaseDate": "2023-06-07T00:00:00Z", "gameVersionName": "1.20", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000020.0000000006", "gameVersion": "1.20.6", "gameVersionReleaseDate": "2024-04-29T15:15:44.67Z", "gameVersionName": "1.20.6", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.0000000019.**********", "gameVersion": "1.19.1", "gameVersionReleaseDate": "2022-06-28T00:00:00Z", "gameVersionName": "1.19.1", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.0000000020.0000000004", "gameVersion": "1.20.4", "gameVersionReleaseDate": "2023-12-07T15:17:47.907Z", "gameVersionName": "1.20.4", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-05-04T00:00:00Z", "gameVersionName": "1.21-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000003", "gameVersion": "1.21.3", "gameVersionReleaseDate": "2024-10-23T13:16:45.71Z", "gameVersionName": "1.21.3", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000007", "gameVersion": "1.21.7", "gameVersionReleaseDate": "2025-06-30T14:45:05.54Z", "gameVersionName": "1.21.7", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000020", "gameVersion": "1.20", "gameVersionReleaseDate": "2022-10-27T00:00:00Z", "gameVersionName": "1.20-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 75125}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000006", "gameVersion": "1.21.6", "gameVersionReleaseDate": "2025-06-17T19:44:59.623Z", "gameVersionName": "1.21.6", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.0000000017", "gameVersion": "1.17", "gameVersionReleaseDate": "2021-03-18T00:00:00Z", "gameVersionName": "1.17-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73242}, {"gameVersionPadded": "**********.0000000018", "gameVersion": "1.18", "gameVersionReleaseDate": "2021-11-30T16:23:01.427Z", "gameVersionName": "1.18", "gameVersionTypeId": 73250}, {"gameVersionPadded": "**********.0000000016.0000000004", "gameVersion": "1.16.4", "gameVersionReleaseDate": "2020-11-02T18:40:51.49Z", "gameVersionName": "1.16.4", "gameVersionTypeId": 70886}, {"gameVersionPadded": "**********.0000000014.0000000002", "gameVersion": "1.14.2", "gameVersionReleaseDate": "2019-05-27T00:00:00Z", "gameVersionName": "1.14.2", "gameVersionTypeId": 64806}, {"gameVersionPadded": "**********.0000000016.0000000002", "gameVersion": "1.16.2", "gameVersionReleaseDate": "2020-08-11T16:42:21.863Z", "gameVersionName": "1.16.2", "gameVersionTypeId": 70886}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt", "gameVersionTypeId": 68441}, {"gameVersionPadded": "**********.0000000019", "gameVersion": "1.19", "gameVersionReleaseDate": "2022-06-07T15:38:07.377Z", "gameVersionName": "1.19", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.0000000004", "gameVersion": "1.21.4", "gameVersionReleaseDate": "2024-11-01T00:00:00Z", "gameVersionName": "1.21.4-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.15.1", "gameVersionReleaseDate": "2019-12-17T00:00:00Z", "gameVersionName": "1.15.1", "gameVersionTypeId": 68722}, {"gameVersionPadded": "**********.0000000019.0000000003", "gameVersion": "1.19.3", "gameVersionReleaseDate": "2023-01-01T00:00:00Z", "gameVersionName": "1.19.3-<PERSON><PERSON><PERSON>", "gameVersionTypeId": 73407}, {"gameVersionPadded": "**********.**********.0000000002", "gameVersion": "1.21.2", "gameVersionReleaseDate": "2024-10-22T00:00:00Z", "gameVersionName": "1.21.2", "gameVersionTypeId": 77784}, {"gameVersionPadded": "**********.**********.0000000009", "gameVersion": "1.21.9", "gameVersionReleaseDate": "2025-07-29T00:00:00Z", "gameVersionName": "1.21.9-snapshot", "gameVersionTypeId": 77784}], "downloadUrl": "https://edge.forgecdn.net/files/6987/510/EuphoriaPatcher-1.6.8-r5.5.1-fabric.jar", "fileDate": "2025-09-11T21:37:00.937Z", "exposeAsAlternative": null, "modId": 915902, "modules": [{"name": "META-INF", "fingerprint": 427690959}, {"name": "EuphoriaPatcher-refmap.json", "fingerprint": **********}, {"name": "EuphoriaPatches_1.6.8.patch", "fingerprint": **********}, {"name": "LICENSE_EuphoriaPatcher.txt", "fingerprint": **********}, {"name": "assets", "fingerprint": **********}, {"name": "commons-compress-LICENSE.txt", "fingerprint": **********}, {"name": "euphoria_patcher.mixins.json", "fingerprint": **********}, {"name": "fabric.mod.json", "fingerprint": **********}, {"name": "icon32x.png", "fingerprint": **********}, {"name": "jbsdiff-LICENSE.txt", "fingerprint": **********}, {"name": "mc", "fingerprint": **********}, {"name": "randomUtil.json", "fingerprint": **********}, {"name": "settingsConversions.txt", "fingerprint": **********}], "dependencies": [], "fileFingerprint": **********, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "754240bab180ce4e4d81d61ae90ebb4ee3b77cca", "algo": 1}, {"value": "01f12dedcbe1e103fe4261703caa473f", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 6987510, "fileLength": 2067332, "downloadCount": 58539, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-09-11T21:45:05.04Z", "gamePopularityRank": 87, "thumbsUpCount": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "mainFileId": 6987510, "primaryCategoryId": 425, "downloadCount": 43480956, "status": 4, "authors": [{"name": "SpacEagle17", "id": 100690350, "url": "https://www.curseforge.com/members/spaceagle17"}], "available": false, "featured": false}