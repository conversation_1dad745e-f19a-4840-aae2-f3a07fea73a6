{"allowModDistribution": true, "screenshots": [{"description": "Quickly access Essential's powerful features.", "id": 1081368, "title": "Custom Main Menu", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/368/full-main-mneu.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/368/310/172/full-main-mneu.png"}, {"description": "Edit and share screenshots easily within Minecraft.", "id": 1081367, "title": "Picture Editor", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/367/edit-picutres.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/367/310/172/edit-picutres.png"}, {"description": "See your screenshots and access quick actions.", "id": 1081363, "title": "Screenshot Preview", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/363/in-world-taking-screenshot.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/363/310/172/in-world-taking-screenshot.png"}, {"description": "View, favorite, edit, and share your screenshots.", "id": 1081360, "title": "Picture Browser", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/360/picture-browser.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/360/310/172/picture-browser.png"}, {"description": "Create and collect custom outfits inside the wardrobe.", "id": 1081358, "title": "Outfits Library", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/358/outfits-library.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/358/310/172/outfits-library.png"}, {"description": "Stay connected with DM's and group chats, across all versions of Essential.", "id": 1081357, "title": "Social Menu", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/357/social-menu-chatting-with-friend.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/357/310/172/social-menu-chatting-with-friend.png"}, {"description": "Send game invites for the server you're playing on.", "id": 1081352, "title": "Server Invites", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/352/invite-friends-to-server.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/352/310/172/invite-friends-to-server.png"}, {"description": "Add multiple Minecraft accounts and easily switch between them, all in-game.", "id": 1081347, "title": "Account Manager", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/347/minimal-main-menu-account-switcher-open.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/347/310/172/minimal-main-menu-account-switcher-open.png"}, {"description": "Add skins and easily switch between them on the go.", "id": 1081346, "title": "Skin Library", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/346/skin-library.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/346/310/172/skin-library.png"}, {"description": "No need to pay for a server! Invite friends directly to your world.", "id": 1081342, "title": "World Hosting", "modId": 546670, "url": "https://media.forgecdn.net/attachments/1081/342/in-world-playing-with-friend.png", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/1081/342/310/172/in-world-playing-with-friend.png"}], "classId": 6, "latestFilesIndexes": [{"filename": "Essential_1-3-9-2_neoforge_1-20-4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7048976}, {"filename": "Essential_1-3-9-2_neoforge_1-20-6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 6, "fileId": 7048975}, {"filename": "Essential_1-3-9-2_neoforge_1-21-1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7048974}, {"filename": "Essential_1-3-9-2_neoforge_1-21-3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7048973}, {"filename": "Essential_1-3-9-2_neoforge_1-21-4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7048972}, {"filename": "Essential_1-3-9-2_neoforge_1-21-5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7048971}, {"filename": "Essential_1-3-9-2_neoforge_1-21-8.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7048970}, {"filename": "Essential_1-3-9-2_neoforge_1-21-8.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 6, "fileId": 7048970}, {"filename": "Essential_1-3-9-2_forge_1-8-9.jar", "releaseType": 1, "gameVersion": "1.8.9", "gameVersionTypeId": 4, "modLoader": 1, "fileId": 7048969}, {"filename": "Essential_1-3-9-2_forge_1-12-2.jar", "releaseType": 1, "gameVersion": "1.12.2", "gameVersionTypeId": 628, "modLoader": 1, "fileId": 7048968}, {"filename": "Essential_1-3-9-2_forge_1-16-5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 1, "fileId": 7048967}, {"filename": "Essential_1-3-9-2_forge_1-17-1.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 1, "fileId": 7048966}, {"filename": "Essential_1-3-9-2_forge_1-18-2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 1, "fileId": 7048965}, {"filename": "Essential_1-3-9-2_forge_1-19-2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7048964}, {"filename": "Essential_1-3-9-2_forge_1-19-3.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7048963}, {"filename": "Essential_1-3-9-2_forge_1-19-4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 1, "fileId": 7048962}, {"filename": "Essential_1-3-9-2_forge_1-20-1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7048961}, {"filename": "Essential_1-3-9-2_forge_1-20-2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7048960}, {"filename": "Essential_1-3-9-2_forge_1-20-4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7048959}, {"filename": "Essential_1-3-9-2_forge_1-20-6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 1, "fileId": 7048958}, {"filename": "Essential_1-3-9-2_forge_1-21-1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7048957}, {"filename": "Essential_1-3-9-2_forge_1-21-3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7048956}, {"filename": "Essential_1-3-9-2_forge_1-21-4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7048955}, {"filename": "Essential_1-3-9-2_forge_1-21-5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7048954}, {"filename": "Essential_1-3-9-2_forge_1-21-8.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7048953}, {"filename": "Essential_1-3-9-2_forge_1-21-8.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 1, "fileId": 7048953}, {"filename": "Essential_1-3-9-2_fabric_1-16-5.jar", "releaseType": 1, "gameVersion": "1.16.5", "gameVersionTypeId": 70886, "modLoader": 4, "fileId": 7048952}, {"filename": "Essential_1-3-9-2_fabric_1-17-1.jar", "releaseType": 1, "gameVersion": "1.17.1", "gameVersionTypeId": 73242, "modLoader": 4, "fileId": 7048951}, {"filename": "Essential_1-3-9-2_fabric_1-18-2.jar", "releaseType": 1, "gameVersion": "1.18.2", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 7048949}, {"filename": "Essential_1-3-9-2_fabric_1-19.jar", "releaseType": 1, "gameVersion": "1.19", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 7048948}, {"filename": "Essential_1-3-9-2_fabric_1-19-2.jar", "releaseType": 1, "gameVersion": "1.19.2", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 7048947}, {"filename": "Essential_1-3-9-2_fabric_1-19-2.jar", "releaseType": 1, "gameVersion": "1.19.1", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 7048947}, {"filename": "Essential_1-3-9-2_fabric_1-19-3.jar", "releaseType": 1, "gameVersion": "1.19.3", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 7048945}, {"filename": "Essential_1-3-9-2_fabric_1-19-4.jar", "releaseType": 1, "gameVersion": "1.19.4", "gameVersionTypeId": 73407, "modLoader": 4, "fileId": 7048944}, {"filename": "Essential_1-3-9-2_fabric_1-20.jar", "releaseType": 1, "gameVersion": "1.20", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7048942}, {"filename": "Essential_1-3-9-2_fabric_1-20-1.jar", "releaseType": 1, "gameVersion": "1.20.1", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7048941}, {"filename": "Essential_1-3-9-2_fabric_1-20-2.jar", "releaseType": 1, "gameVersion": "1.20.2", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7048940}, {"filename": "Essential_1-3-9-2_fabric_1-20-4.jar", "releaseType": 1, "gameVersion": "1.20.4", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7048939}, {"filename": "Essential_1-3-9-2_fabric_1-20-6.jar", "releaseType": 1, "gameVersion": "1.20.6", "gameVersionTypeId": 75125, "modLoader": 4, "fileId": 7048938}, {"filename": "Essential_1-3-9-2_fabric_1-21-1.jar", "releaseType": 1, "gameVersion": "1.21", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048937}, {"filename": "Essential_1-3-9-2_fabric_1-21-1.jar", "releaseType": 1, "gameVersion": "1.21.1", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048937}, {"filename": "Essential_1-3-9-2_fabric_1-21-3.jar", "releaseType": 1, "gameVersion": "1.21.3", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048935}, {"filename": "Essential_1-3-9-2_fabric_1-21-3.jar", "releaseType": 1, "gameVersion": "1.21.2", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048935}, {"filename": "Essential_1-3-9-2_fabric_1-21-4.jar", "releaseType": 1, "gameVersion": "1.21.4", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048934}, {"filename": "Essential_1-3-9-2_fabric_1-21-5.jar", "releaseType": 1, "gameVersion": "1.21.5", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048933}, {"filename": "Essential_1-3-9-2_fabric_1-21-6.jar", "releaseType": 1, "gameVersion": "1.21.6", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048930}, {"filename": "Essential_1-3-9-2_fabric_1-21-8.jar", "releaseType": 1, "gameVersion": "1.21.8", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048929}, {"filename": "Essential_1-3-9-2_fabric_1-21-8.jar", "releaseType": 1, "gameVersion": "1.21.7", "gameVersionTypeId": 77784, "modLoader": 4, "fileId": 7048929}, {"filename": "Essential_1-3-9-1_fabric_1-18-1.jar", "releaseType": 1, "gameVersion": "1.18", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 7008150}, {"filename": "Essential_1-3-9-1_fabric_1-18-1.jar", "releaseType": 1, "gameVersion": "1.18.1", "gameVersionTypeId": 73250, "modLoader": 4, "fileId": 7008150}], "dateCreated": "2021-12-06T07:45:27.437Z", "logo": {"description": "", "id": 1004127, "title": "638525051411002219.png", "modId": 546670, "url": "https://media.forgecdn.net/avatars/1004/127/638525051411002219.png", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1004/127/256/256/638525051411002219.png"}, "links": {"sourceUrl": null, "issuesUrl": null, "websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/essential-mod", "wikiUrl": "https://essential.gg/wiki"}, "dateReleased": "2025-09-30T15:14:55.1Z", "id": 546670, "categories": [{"gameId": 432, "classId": 6, "name": "API and Library", "dateModified": "2014-05-23T03:21:44.06Z", "parentCategoryId": 6, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "API and Library", "dateModified": "2014-05-23T03:21:44.06Z", "parentCategoryId": 6, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Utility & QoL", "dateModified": "2021-11-17T11:45:09.143Z", "parentCategoryId": 6, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}, {"gameId": 432, "classId": 6, "name": "Cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "parentCategoryId": 6, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "displayIndex": 0, "class": false}], "slug": "essential-mod", "gameId": 432, "summary": "The best way to play Minecraft. Enhanced Minecraft features in one simple mod.  Fast, friendly, and for everyone!", "latestFiles": [{"gameId": 432, "fileName": "Essential_1-3-9-2_fabric_1-16-5.jar", "gameVersions": ["<PERSON><PERSON><PERSON>", "1.16.5"], "displayName": "Essential 1.3.9.2 [F<PERSON>ric 1.16.5]", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000016.0000000005", "gameVersion": "1.16.5", "gameVersionReleaseDate": "2021-01-15T14:14:48.91Z", "gameVersionName": "1.16.5", "gameVersionTypeId": 70886}], "downloadUrl": "https://edge.forgecdn.net/files/7048/952/Essential_1-3-9-2_fabric_1-16-5.jar", "fileDate": "2025-09-30T15:14:08.563Z", "exposeAsAlternative": null, "modId": 546670, "modules": [{"name": "META-INF", "fingerprint": 2464251221}, {"name": "assets", "fingerprint": 2814284546}, {"name": "essential-loader-stage2-fabric-1.6.6.jar", "fingerprint": 3773070610}, {"name": "essential-loader-stage2.properties", "fingerprint": 1793595182}, {"name": "essential-loader.jar", "fingerprint": 208259638}, {"name": "fabric.mod.json", "fingerprint": 624984319}, {"name": "essential-5b2004bdaeb13a4ac9bd33cb6a261a36.jar", "fingerprint": 2496338223}, {"name": "essential-loader.properties", "fingerprint": 2432383244}], "dependencies": [], "fileFingerprint": 1407250142, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "53177687f74fd597350543fc7a788d3be28462c8", "algo": 1}, {"value": "e93336ca69f28745c66458e9cd492bbd", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7048952, "fileLength": 54321930, "downloadCount": 1, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Essential_1-3-9-2_forge_1-8-9.jar", "gameVersions": ["Forge", "1.8.9"], "displayName": "Essential 1.3.9.2 [Forge 1.8.9]", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-10-01T00:00:00Z", "gameVersionName": "Forge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000008.0000000009", "gameVersion": "1.8.9", "gameVersionReleaseDate": "2015-12-09T06:00:00Z", "gameVersionName": "1.8.9", "gameVersionTypeId": 4}], "downloadUrl": "https://edge.forgecdn.net/files/7048/969/Essential_1-3-9-2_forge_1-8-9.jar", "fileDate": "2025-09-30T15:14:40.790Z", "exposeAsAlternative": null, "modId": 546670, "modules": [{"name": "META-INF", "fingerprint": 420261935}, {"name": "essential-loader-stage2-launchwrapper-1.6.6.jar", "fingerprint": 3899148785}, {"name": "essential-loader-stage2.properties", "fingerprint": 1947285034}, {"name": "gg", "fingerprint": 4006924468}, {"name": "essential-93f4a0148f0748cd9680c5c8b90515e8.jar", "fingerprint": 495896949}, {"name": "essential-loader.properties", "fingerprint": 2781088279}], "dependencies": [], "fileFingerprint": 818067923, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "9f19ba71ed68e222f16c63c23aa1fd605363bc14", "algo": 1}, {"value": "54beaf6a1ef8e5cda034040af1fa5838", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7048969, "fileLength": 57667419, "downloadCount": 1, "serverPackFileId": null, "serverPack": false, "available": false}, {"gameId": 432, "fileName": "Essential_1-3-9-2_neoforge_1-20-4.jar", "gameVersions": ["NeoForge", "1.20.4"], "displayName": "Essential 1.3.9.2 [Neoforge 1.20.4]", "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge", "gameVersionTypeId": 68441}, {"gameVersionPadded": "0000000001.0000000020.0000000004", "gameVersion": "1.20.4", "gameVersionReleaseDate": "2023-12-07T15:17:47.907Z", "gameVersionName": "1.20.4", "gameVersionTypeId": 75125}], "downloadUrl": "https://edge.forgecdn.net/files/7048/976/Essential_1-3-9-2_neoforge_1-20-4.jar", "fileDate": "2025-09-30T15:14:55.100Z", "exposeAsAlternative": null, "modId": 546670, "modules": [{"name": "META-INF", "fingerprint": 2900283013}, {"name": "essential-loader-stage2-modlauncher9-1.6.6.jar", "fingerprint": 1659109102}, {"name": "essential-loader-stage2.properties", "fingerprint": 3701090653}, {"name": "gg", "fingerprint": 3063300824}, {"name": "essential-76c68977784ddaeecfd707f313ea6e93.jar", "fingerprint": 2525547010}, {"name": "essential-loader.properties", "fingerprint": 909784408}], "dependencies": [], "fileFingerprint": 630782793, "fileStatus": 4, "releaseType": 1, "hashes": [{"value": "9483b5c8434d5c34a6ce75924fcad0d97866790c", "algo": 1}, {"value": "f56e096b4733d3d761d0fbf97d230641", "algo": 2}], "parentProjectFileId": null, "alternateFileId": 0, "id": 7048976, "fileLength": 53958357, "downloadCount": 1356, "serverPackFileId": null, "serverPack": false, "available": false}], "dateModified": "2025-10-01T08:53:56.31Z", "gamePopularityRank": 141, "thumbsUpCount": 0, "name": "Essential Mod", "mainFileId": 7048976, "primaryCategoryId": 5191, "downloadCount": 51817919, "status": 4, "authors": [{"name": "SparkUniverse_", "id": 102634440, "url": "https://www.curseforge.com/members/sparkuniverse_"}], "available": false, "featured": false}