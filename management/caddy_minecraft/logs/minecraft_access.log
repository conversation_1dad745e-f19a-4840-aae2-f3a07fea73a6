{"level":"info","ts":1757581329.9716938,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1757581329.9719768,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1757582166.6725419,"logger":"layer4","msg":"handling connection","remote":"*********3:56735","error":"dial tcp ***********:25565: connect: connection refused"}
{"level":"error","ts":1757582166.6725473,"logger":"layer4","msg":"handling connection","remote":"*********3:56551","error":"dial tcp ***********:25565: connect: connection refused"}
{"level":"error","ts":1757582166.6727188,"logger":"layer4","msg":"handling connection","remote":"*********3:56563","error":"dial tcp ***********:25565: connect: connection refused"}
{"level":"error","ts":1757582166.6797175,"logger":"layer4","msg":"handling connection","remote":"*********3:57791","error":"dial tcp ***********:25565: connect: connection refused"}
{"level":"error","ts":1757582166.6797256,"logger":"layer4","msg":"handling connection","remote":"*********3:57789","error":"dial tcp ***********:25565: connect: connection refused"}
{"level":"error","ts":1757582177.0057812,"logger":"layer4","msg":"handling connection","remote":"*********3:35975","error":"dial tcp ***********:25565: connect: connection refused"}
{"level":"info","ts":1757582369.9521143,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//127.0.0.1:2019","//localhost:2019","//[::1]:2019"]}
{"level":"info","ts":1757582369.9523942,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1757601337.6680427,"logger":"layer4","msg":"handling connection","remote":"*********4:35070","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757601383.9309738,"logger":"layer4","msg":"handling connection","remote":"*********4:54282","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757601427.352817,"logger":"layer4","msg":"handling connection","remote":"*********4:36090","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757601599.6638994,"logger":"layer4","msg":"handling connection","remote":"*********4:43158","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757601638.963118,"logger":"layer4","msg":"handling connection","remote":"*********4:35150","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1757601702.19912,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757601702.1991923,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757601702.2839189,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"info","ts":1757601702.2839935,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757601702.2840192,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757601831.9277902,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1757601831.9306521,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1757602178.180382,"logger":"layer4","msg":"handling connection","remote":"*********:55334","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757603361.3140433,"logger":"layer4","msg":"handling connection","remote":"*********:41918","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757605550.158124,"logger":"layer4","msg":"handling connection","remote":"*********:49882","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757605823.3957717,"logger":"layer4","msg":"handling connection","remote":"*********:36820","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757606278.281599,"logger":"layer4","msg":"handling connection","remote":"*********:60220","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757606735.5745597,"logger":"layer4","msg":"handling connection","remote":"*********:42940","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757606735.5749202,"logger":"layer4","msg":"handling connection","remote":"*********:42950","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757606735.7072659,"logger":"layer4","msg":"handling connection","remote":"*********:42952","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757606735.7084131,"logger":"layer4","msg":"handling connection","remote":"*********:42962","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757606737.8422039,"logger":"layer4","msg":"handling connection","remote":"*********:42974","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757606737.8462982,"logger":"layer4","msg":"handling connection","remote":"*********:42980","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757609895.277742,"logger":"layer4","msg":"handling connection","remote":"*********:38262","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757609895.3031905,"logger":"layer4","msg":"handling connection","remote":"*********:38278","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757609900.763298,"logger":"layer4","msg":"handling connection","remote":"*********:48692","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757609900.8404052,"logger":"layer4","msg":"handling connection","remote":"*********:48696","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757610272.2789137,"logger":"layer4","msg":"handling connection","remote":"*********:41862","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757612107.0989313,"logger":"layer4","msg":"handling connection","remote":"*********:58376","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757612328.998757,"logger":"layer4","msg":"handling connection","remote":"*********:44170","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757612415.1794026,"logger":"layer4","msg":"handling connection","remote":"*********:42284","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757612741.7822802,"logger":"layer4","msg":"handling connection","remote":"*********:49370","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757612942.479699,"logger":"layer4","msg":"handling connection","remote":"*********:48254","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757614605.1762893,"logger":"layer4","msg":"handling connection","remote":"*********:57542","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757614698.025862,"logger":"layer4","msg":"handling connection","remote":"*********:41694","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757614840.8673663,"logger":"layer4","msg":"handling connection","remote":"*********:42720","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757616119.3320389,"logger":"layer4","msg":"handling connection","remote":"*********:37746","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757616318.435385,"logger":"layer4","msg":"handling connection","remote":"*********:35712","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757616982.579388,"logger":"layer4","msg":"handling connection","remote":"*********:34684","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1757649614.735463,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757649614.7357397,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757649614.7359524,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757649614.735982,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757673598.2931805,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1757673598.2953417,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1757736014.6518984,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757736014.6522624,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757736014.6524441,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757736014.6524534,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757753187.7169962,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//127.0.0.1:2019","//localhost:2019","//[::1]:2019"]}
{"level":"info","ts":1757753187.7190554,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1757787574.599668,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//127.0.0.1:2019","//localhost:2019","//[::1]:2019"]}
{"level":"info","ts":1757787574.6114151,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1757787647.3832073,"logger":"layer4","msg":"handling connection","remote":"*********:45080","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1757787737.80269,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757787737.802989,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757787737.8031695,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757787737.803183,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757787750.278966,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//[::1]:2019","//127.0.0.1:2019","//localhost:2019"]}
{"level":"info","ts":1757787750.2810605,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1757788249.776991,"logger":"layer4","msg":"handling connection","remote":"**********:60860","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757788351.0854986,"logger":"layer4","msg":"handling connection","remote":"**********:52778","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757788450.9297616,"logger":"layer4","msg":"handling connection","remote":"**********:34622","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757789143.7008111,"logger":"layer4","msg":"handling connection","remote":"**********:50142","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757790059.9707613,"logger":"layer4","msg":"handling connection","remote":"**********:33668","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757790821.2831938,"logger":"layer4","msg":"handling connection","remote":"**********:46740","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757791475.8828995,"logger":"layer4","msg":"handling connection","remote":"**********:58074","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757791586.8783472,"logger":"layer4","msg":"handling connection","remote":"**********:58708","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757791821.9008958,"logger":"layer4","msg":"handling connection","remote":"**********:35958","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792069.4014869,"logger":"layer4","msg":"handling connection","remote":"**********:51850","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792138.1587577,"logger":"layer4","msg":"handling connection","remote":"**********:41148","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792352.039705,"logger":"layer4","msg":"handling connection","remote":"**********:38214","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792366.7623456,"logger":"layer4","msg":"handling connection","remote":"**********:41888","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792545.508868,"logger":"layer4","msg":"handling connection","remote":"**********:43584","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792579.9724228,"logger":"layer4","msg":"handling connection","remote":"**********:33108","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792788.3931866,"logger":"layer4","msg":"handling connection","remote":"**********:43040","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757792998.195112,"logger":"layer4","msg":"handling connection","remote":"**********:36290","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757793024.2972777,"logger":"layer4","msg":"handling connection","remote":"**********:58806","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757793199.719523,"logger":"layer4","msg":"handling connection","remote":"**********:37598","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757793410.946194,"logger":"layer4","msg":"handling connection","remote":"**********:48432","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757793452.2052443,"logger":"layer4","msg":"handling connection","remote":"**********:45866","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1757958712.4947245,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757958712.5360107,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757958712.5363164,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"info","ts":1757958712.536464,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757958712.7158365,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757963337.3531222,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1757963337.355529,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1757963347.3237667,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757963347.3238041,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757963347.3240094,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757963347.3240354,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757968963.3630545,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//127.0.0.1:2019","//localhost:2019","//[::1]:2019"]}
{"level":"info","ts":1757968963.3633883,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1757968982.0689893,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757968982.069017,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757968982.0691552,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757968982.069165,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757969478.963344,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1757969478.9654791,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1757969507.7822838,"logger":"layer4","msg":"handling connection","remote":"*********:44570","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757969568.6972384,"logger":"layer4","msg":"handling connection","remote":"*********:48636","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1757969579.218748,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757969579.2187896,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757969579.2189279,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757969579.2189407,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757969591.7024112,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//127.0.0.1:2019","//localhost:2019","//[::1]:2019"]}
{"level":"info","ts":1757969591.702612,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1757969659.0014584,"logger":"layer4","msg":"handling connection","remote":"**********:52716","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757969679.4965546,"logger":"layer4","msg":"handling connection","remote":"**********:41750","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757969839.6129713,"logger":"layer4","msg":"handling connection","remote":"**********:60486","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757969919.507234,"logger":"layer4","msg":"handling connection","remote":"**********:46318","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757970020.4059386,"logger":"layer4","msg":"handling connection","remote":"**********:38224","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1757970077.6211133,"logger":"layer4","msg":"handling connection","remote":"**********:57148","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1757995221.495692,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1757995221.4959445,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1757995221.4972785,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1757995221.4972906,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1757995233.8800921,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1757995233.8802977,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758117591.253031,"logger":"layer4","msg":"handling connection","remote":"**********:58098","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758117771.5980961,"logger":"layer4","msg":"handling connection","remote":"**********:54726","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758117952.7683694,"logger":"layer4","msg":"handling connection","remote":"**********:56686","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118133.2007682,"logger":"layer4","msg":"handling connection","remote":"**********:52456","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118208.7365773,"logger":"layer4","msg":"handling connection","remote":"**********:37998","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118271.4105332,"logger":"layer4","msg":"handling connection","remote":"**********:32908","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118314.0869837,"logger":"layer4","msg":"handling connection","remote":"**********:38484","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118494.5300117,"logger":"layer4","msg":"handling connection","remote":"**********:58746","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118594.88667,"logger":"layer4","msg":"handling connection","remote":"**********:41988","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118596.7835262,"logger":"layer4","msg":"handling connection","remote":"**********:41996","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758118676.0374236,"logger":"layer4","msg":"handling connection","remote":"**********:59150","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119400.8671417,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:33236","remote_address":"***********:25566","error":"writeto tcp **********:33236->***********:25566: read tcp **********:33236->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758119579.4025705,"logger":"layer4","msg":"handling connection","remote":"**********:37456","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119611.8455813,"logger":"layer4","msg":"handling connection","remote":"**********:52414","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119611.8464441,"logger":"layer4","msg":"handling connection","remote":"**********:52424","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119612.0618067,"logger":"layer4","msg":"handling connection","remote":"**********:52426","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119612.0624607,"logger":"layer4","msg":"handling connection","remote":"**********:52428","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119612.742717,"logger":"layer4","msg":"handling connection","remote":"**********:52430","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119612.7433584,"logger":"layer4","msg":"handling connection","remote":"**********:52442","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119612.811581,"logger":"layer4","msg":"handling connection","remote":"**********:52456","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119613.7442625,"logger":"layer4","msg":"handling connection","remote":"**********:60336","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119613.7492836,"logger":"layer4","msg":"handling connection","remote":"**********:60352","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119613.767281,"logger":"layer4","msg":"handling connection","remote":"**********:60366","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119613.7675104,"logger":"layer4","msg":"handling connection","remote":"**********:60374","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119615.8368576,"logger":"layer4","msg":"handling connection","remote":"**********:60376","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119615.839537,"logger":"layer4","msg":"handling connection","remote":"**********:60380","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119615.8396382,"logger":"layer4","msg":"handling connection","remote":"**********:60390","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119615.8407462,"logger":"layer4","msg":"handling connection","remote":"**********:60392","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119615.845951,"logger":"layer4","msg":"handling connection","remote":"**********:60398","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119615.8484497,"logger":"layer4","msg":"handling connection","remote":"**********:60412","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119615.863948,"logger":"layer4","msg":"handling connection","remote":"**********:60428","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119616.8490055,"logger":"layer4","msg":"handling connection","remote":"**********:60436","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119616.850776,"logger":"layer4","msg":"handling connection","remote":"**********:60450","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119616.8594975,"logger":"layer4","msg":"handling connection","remote":"**********:60456","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119617.8497593,"logger":"layer4","msg":"handling connection","remote":"**********:60466","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119617.8666155,"logger":"layer4","msg":"handling connection","remote":"**********:60480","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119640.5554988,"logger":"layer4","msg":"handling connection","remote":"**********:56130","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119658.0975769,"logger":"layer4","msg":"handling connection","remote":"**********:38760","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119658.11047,"logger":"layer4","msg":"handling connection","remote":"**********:38762","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119658.112995,"logger":"layer4","msg":"handling connection","remote":"**********:38768","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119658.113212,"logger":"layer4","msg":"handling connection","remote":"**********:38776","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119659.1066031,"logger":"layer4","msg":"handling connection","remote":"**********:38778","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119659.1208792,"logger":"layer4","msg":"handling connection","remote":"**********:38784","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119811.4632788,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:56710","remote_address":"***********:25566","error":"writeto tcp **********:56710->***********:25566: read tcp **********:56710->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758119821.903349,"logger":"layer4","msg":"handling connection","remote":"**********:52984","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119821.9324653,"logger":"layer4","msg":"handling connection","remote":"**********:52994","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119825.3236897,"logger":"layer4","msg":"handling connection","remote":"**********:50280","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119825.373796,"logger":"layer4","msg":"handling connection","remote":"**********:50288","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119849.3331397,"logger":"layer4","msg":"handling connection","remote":"**********:59412","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119849.4037383,"logger":"layer4","msg":"handling connection","remote":"**********:59418","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758119940.866039,"logger":"layer4","msg":"handling connection","remote":"**********:35890","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758127163.8324075,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:54184","remote_address":"***********:25566","error":"writeto tcp **********:54184->***********:25566: read tcp **********:54184->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758136422.393427,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758136422.3954763,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758136424.270086,"logger":"layer4","msg":"handling connection","remote":"*********:43034","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1758216709.610203,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758216709.6139305,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758216727.7117202,"logger":"layer4","msg":"handling connection","remote":"*********:41074","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758216908.9289343,"logger":"layer4","msg":"handling connection","remote":"*********:37118","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758216998.7459116,"logger":"layer4","msg":"handling connection","remote":"*********:35170","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217137.6824002,"logger":"layer4","msg":"handling connection","remote":"*********:59098","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217138.2637656,"logger":"layer4","msg":"handling connection","remote":"*********:59104","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217145.5176,"logger":"layer4","msg":"handling connection","remote":"*********:53648","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217146.230203,"logger":"layer4","msg":"handling connection","remote":"*********:53660","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217146.9291728,"logger":"layer4","msg":"handling connection","remote":"*********:53668","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217147.5274487,"logger":"layer4","msg":"handling connection","remote":"*********:53674","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217148.9912162,"logger":"layer4","msg":"handling connection","remote":"*********:53680","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217151.5861325,"logger":"layer4","msg":"handling connection","remote":"*********:50706","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217390.1618197,"logger":"layer4","msg":"handling connection","remote":"*********:54564","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217447.0931854,"logger":"layer4","msg":"handling connection","remote":"*********:49012","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217521.0515156,"logger":"layer4","msg":"handling connection","remote":"*********:55846","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217600.930667,"logger":"layer4","msg":"handling connection","remote":"*********:35066","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758217841.5933595,"logger":"layer4","msg":"handling connection","remote":"*********:55096","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218021.011064,"logger":"layer4","msg":"handling connection","remote":"*********:56122","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218292.7435737,"logger":"layer4","msg":"handling connection","remote":"*********:40924","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218366.580692,"logger":"layer4","msg":"handling connection","remote":"*********:35460","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218478.3190613,"logger":"layer4","msg":"handling connection","remote":"*********:54680","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218503.1508236,"logger":"layer4","msg":"handling connection","remote":"*********:56386","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218713.6625037,"logger":"layer4","msg":"handling connection","remote":"*********:53342","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218740.5298326,"logger":"layer4","msg":"handling connection","remote":"*********:48338","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218848.4883912,"logger":"layer4","msg":"handling connection","remote":"*********:60418","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218892.3463676,"logger":"layer4","msg":"handling connection","remote":"*********:35618","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218924.406036,"logger":"layer4","msg":"handling connection","remote":"*********:50068","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758218952.7674935,"logger":"layer4","msg":"handling connection","remote":"*********:57864","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219048.25129,"logger":"layer4","msg":"handling connection","remote":"*********:60832","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219076.1536088,"logger":"layer4","msg":"handling connection","remote":"*********:49574","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219105.0356514,"logger":"layer4","msg":"handling connection","remote":"*********:41868","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219127.3147426,"logger":"layer4","msg":"handling connection","remote":"*********:58648","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219135.9266908,"logger":"layer4","msg":"handling connection","remote":"*********:48484","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219285.761458,"logger":"layer4","msg":"handling connection","remote":"*********:38828","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219324.1565828,"logger":"layer4","msg":"handling connection","remote":"*********:38234","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1758219450.4085968,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//[::1]:2019","//127.0.0.1:2019","//localhost:2019"]}
{"level":"info","ts":1758219450.4335022,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758219466.4490638,"logger":"layer4","msg":"handling connection","remote":"*********:47882","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219502.7064185,"logger":"layer4","msg":"handling connection","remote":"*********:38626","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219596.0880077,"logger":"layer4","msg":"handling connection","remote":"*********:38312","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219647.1213405,"logger":"layer4","msg":"handling connection","remote":"*********:58036","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219726.6647072,"logger":"layer4","msg":"handling connection","remote":"*********:47398","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219830.1256099,"logger":"layer4","msg":"handling connection","remote":"*********:33418","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758219832.6174014,"logger":"layer4","msg":"handling connection","remote":"*********:33426","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220013.7275634,"logger":"layer4","msg":"handling connection","remote":"*********:44776","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220030.8024268,"logger":"layer4","msg":"handling connection","remote":"*********:38300","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220073.5663512,"logger":"layer4","msg":"handling connection","remote":"*********:44778","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220190.558716,"logger":"layer4","msg":"handling connection","remote":"*********:54624","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220310.5533059,"logger":"layer4","msg":"handling connection","remote":"*********:46822","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220372.9873,"logger":"layer4","msg":"handling connection","remote":"*********:41864","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220445.2454574,"logger":"layer4","msg":"handling connection","remote":"*********:35300","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220501.600084,"logger":"layer4","msg":"handling connection","remote":"*********:58758","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220552.8885772,"logger":"layer4","msg":"handling connection","remote":"*********:33506","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220733.1601653,"logger":"layer4","msg":"handling connection","remote":"*********:37494","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220844.4754472,"logger":"layer4","msg":"handling connection","remote":"*********:41696","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220851.573038,"logger":"layer4","msg":"handling connection","remote":"*********:41698","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758220913.8286846,"logger":"layer4","msg":"handling connection","remote":"*********:49452","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221094.5255222,"logger":"layer4","msg":"handling connection","remote":"*********:38034","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221098.9848268,"logger":"layer4","msg":"handling connection","remote":"*********:38036","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221127.5450935,"logger":"layer4","msg":"handling connection","remote":"*********:39128","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221251.5552433,"logger":"layer4","msg":"handling connection","remote":"*********:54598","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221275.2614863,"logger":"layer4","msg":"handling connection","remote":"*********:58632","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221444.665337,"logger":"layer4","msg":"handling connection","remote":"*********:32996","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221456.119822,"logger":"layer4","msg":"handling connection","remote":"*********:50942","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221636.7561076,"logger":"layer4","msg":"handling connection","remote":"*********:54088","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221648.77063,"logger":"layer4","msg":"handling connection","remote":"*********:41682","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221700.8547046,"logger":"layer4","msg":"handling connection","remote":"*********:42664","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221817.3694248,"logger":"layer4","msg":"handling connection","remote":"*********:42764","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221898.896581,"logger":"layer4","msg":"handling connection","remote":"*********:59086","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758221997.9676604,"logger":"layer4","msg":"handling connection","remote":"*********:49502","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222079.0622728,"logger":"layer4","msg":"handling connection","remote":"*********:51792","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222168.033221,"logger":"layer4","msg":"handling connection","remote":"*********:36036","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222178.7334092,"logger":"layer4","msg":"handling connection","remote":"*********:36316","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222338.0937498,"logger":"layer4","msg":"handling connection","remote":"*********:54456","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222388.4719107,"logger":"layer4","msg":"handling connection","remote":"*********:44086","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222571.0290668,"logger":"layer4","msg":"handling connection","remote":"*********:59106","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222582.7732244,"logger":"layer4","msg":"handling connection","remote":"*********:41166","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222643.1532397,"logger":"layer4","msg":"handling connection","remote":"*********:51526","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222752.061249,"logger":"layer4","msg":"handling connection","remote":"*********:41694","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222789.5183349,"logger":"layer4","msg":"handling connection","remote":"*********:50834","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758222931.9935603,"logger":"layer4","msg":"handling connection","remote":"*********:38504","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223083.8001592,"logger":"layer4","msg":"handling connection","remote":"*********:46040","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223112.3108926,"logger":"layer4","msg":"handling connection","remote":"*********:53912","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223260.8620765,"logger":"layer4","msg":"handling connection","remote":"*********:48954","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223293.0724268,"logger":"layer4","msg":"handling connection","remote":"*********:59334","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223396.0963798,"logger":"layer4","msg":"handling connection","remote":"*********:39842","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223454.0719721,"logger":"layer4","msg":"handling connection","remote":"*********:34860","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223473.5625722,"logger":"layer4","msg":"handling connection","remote":"*********:53226","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223562.2540076,"logger":"layer4","msg":"handling connection","remote":"*********:43482","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223654.2391508,"logger":"layer4","msg":"handling connection","remote":"*********:42902","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223748.6105838,"logger":"layer4","msg":"handling connection","remote":"*********:54186","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223834.970798,"logger":"layer4","msg":"handling connection","remote":"*********:60888","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758223909.6876788,"logger":"layer4","msg":"handling connection","remote":"*********:39748","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224015.536777,"logger":"layer4","msg":"handling connection","remote":"*********:43428","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224096.8191497,"logger":"layer4","msg":"handling connection","remote":"*********:39412","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224195.9672432,"logger":"layer4","msg":"handling connection","remote":"*********:55092","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224202.483979,"logger":"layer4","msg":"handling connection","remote":"*********:55100","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224320.5903027,"logger":"layer4","msg":"handling connection","remote":"*********:36984","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224351.6923623,"logger":"layer4","msg":"handling connection","remote":"*********:57520","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224376.7147722,"logger":"layer4","msg":"handling connection","remote":"*********:59610","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224535.5641377,"logger":"layer4","msg":"handling connection","remote":"*********:36798","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224557.3045413,"logger":"layer4","msg":"handling connection","remote":"*********:46824","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224664.49222,"logger":"layer4","msg":"handling connection","remote":"*********:34714","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224664.5362577,"logger":"layer4","msg":"handling connection","remote":"*********:34722","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224664.6419497,"logger":"layer4","msg":"handling connection","remote":"*********:34724","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224664.6424623,"logger":"layer4","msg":"handling connection","remote":"*********:34738","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.394426,"logger":"layer4","msg":"handling connection","remote":"*********:34750","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.40526,"logger":"layer4","msg":"handling connection","remote":"*********:34754","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.442919,"logger":"layer4","msg":"handling connection","remote":"*********:34756","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.4433107,"logger":"layer4","msg":"handling connection","remote":"*********:34758","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.5618079,"logger":"layer4","msg":"handling connection","remote":"*********:34774","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.5729072,"logger":"layer4","msg":"handling connection","remote":"*********:34780","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.5935876,"logger":"layer4","msg":"handling connection","remote":"*********:34784","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.5939496,"logger":"layer4","msg":"handling connection","remote":"*********:34800","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.7124367,"logger":"layer4","msg":"handling connection","remote":"*********:34806","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.7224753,"logger":"layer4","msg":"handling connection","remote":"*********:34808","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.7455506,"logger":"layer4","msg":"handling connection","remote":"*********:34814","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.7456324,"logger":"layer4","msg":"handling connection","remote":"*********:34826","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.8909907,"logger":"layer4","msg":"handling connection","remote":"*********:34830","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.8931267,"logger":"layer4","msg":"handling connection","remote":"*********:34836","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.8946898,"logger":"layer4","msg":"handling connection","remote":"*********:34852","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224665.945887,"logger":"layer4","msg":"handling connection","remote":"*********:34854","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224668.1128113,"logger":"layer4","msg":"handling connection","remote":"*********:34858","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224669.1140566,"logger":"layer4","msg":"handling connection","remote":"*********:34866","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224670.114651,"logger":"layer4","msg":"handling connection","remote":"*********:34872","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.115703,"logger":"layer4","msg":"handling connection","remote":"*********:34888","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7314036,"logger":"layer4","msg":"handling connection","remote":"*********:34894","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.732134,"logger":"layer4","msg":"handling connection","remote":"*********:34900","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7324307,"logger":"layer4","msg":"handling connection","remote":"*********:34912","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7327719,"logger":"layer4","msg":"handling connection","remote":"*********:34916","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7332618,"logger":"layer4","msg":"handling connection","remote":"*********:34942","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7333155,"logger":"layer4","msg":"handling connection","remote":"*********:34932","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7338002,"logger":"layer4","msg":"handling connection","remote":"*********:34954","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7349143,"logger":"layer4","msg":"handling connection","remote":"*********:34956","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7349713,"logger":"layer4","msg":"handling connection","remote":"*********:34960","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.743196,"logger":"layer4","msg":"handling connection","remote":"*********:34970","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7432528,"logger":"layer4","msg":"handling connection","remote":"*********:34966","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7454414,"logger":"layer4","msg":"handling connection","remote":"*********:34986","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224671.7929153,"logger":"layer4","msg":"handling connection","remote":"*********:35002","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224738.1267622,"logger":"layer4","msg":"handling connection","remote":"*********:58810","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224803.0111606,"logger":"layer4","msg":"handling connection","remote":"*********:43876","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224808.6349823,"logger":"layer4","msg":"handling connection","remote":"*********:38280","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224918.84351,"logger":"layer4","msg":"handling connection","remote":"*********:56748","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758224999.5686922,"logger":"layer4","msg":"handling connection","remote":"*********:35782","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225031.925958,"logger":"layer4","msg":"handling connection","remote":"*********:38574","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225099.5500963,"logger":"layer4","msg":"handling connection","remote":"*********:50422","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225134.0943568,"logger":"layer4","msg":"handling connection","remote":"*********:46166","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225172.5749269,"logger":"layer4","msg":"handling connection","remote":"*********:46390","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225174.7321417,"logger":"layer4","msg":"handling connection","remote":"*********:33850","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225210.7768478,"logger":"layer4","msg":"handling connection","remote":"*********:43214","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225280.231038,"logger":"layer4","msg":"handling connection","remote":"*********:44022","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758225425.3095748,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33756","remote_address":"***********:25566","error":"writeto tcp *********:33756->***********:25566: read tcp *********:33756->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225425.324631,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33762","remote_address":"***********:25566","error":"writeto tcp *********:33762->***********:25566: read tcp *********:33762->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225426.200451,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33828","remote_address":"***********:25566","error":"writeto tcp *********:33828->***********:25566: read tcp *********:33828->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225426.2008753,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33804","remote_address":"***********:25566","error":"writeto tcp *********:33804->***********:25566: read tcp *********:33804->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225426.2009718,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33818","remote_address":"***********:25566","error":"writeto tcp *********:33818->***********:25566: read tcp *********:33818->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225426.201009,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33826","remote_address":"***********:25566","error":"writeto tcp *********:33826->***********:25566: read tcp *********:33826->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225426.2006419,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33814","remote_address":"***********:25566","error":"writeto tcp *********:33814->***********:25566: read tcp *********:33814->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225426.456783,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33870","remote_address":"***********:25566","error":"writeto tcp *********:33870->***********:25566: read tcp *********:33870->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225436.3586526,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:38622","remote_address":"***********:25566","error":"writeto tcp *********:38622->***********:25566: read tcp *********:38622->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225461.100244,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:55030","remote_address":"***********:25566","error":"writeto tcp *********:55030->***********:25566: read tcp *********:55030->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225503.2142346,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56108","remote_address":"***********:25566","error":"writeto tcp *********:56108->***********:25566: read tcp *********:56108->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225503.2297497,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56112","remote_address":"***********:25566","error":"writeto tcp *********:56112->***********:25566: read tcp *********:56112->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225503.816714,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56148","remote_address":"***********:25566","error":"writeto tcp *********:56148->***********:25566: read tcp *********:56148->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225503.8271043,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56158","remote_address":"***********:25566","error":"writeto tcp *********:56158->***********:25566: read tcp *********:56158->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225503.97046,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56160","remote_address":"***********:25566","error":"writeto tcp *********:56160->***********:25566: read tcp *********:56160->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225503.982941,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56176","remote_address":"***********:25566","error":"writeto tcp *********:56176->***********:25566: read tcp *********:56176->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.4373193,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35482","remote_address":"***********:25566","error":"writeto tcp *********:35482->***********:25566: read tcp *********:35482->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.4379914,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35512","remote_address":"***********:25566","error":"writeto tcp *********:35512->***********:25566: read tcp *********:35512->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.4380255,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35480","remote_address":"***********:25566","error":"writeto tcp *********:35480->***********:25566: read tcp *********:35480->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.4379869,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35486","remote_address":"***********:25566","error":"writeto tcp *********:35486->***********:25566: read tcp *********:35486->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.4394023,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35500","remote_address":"***********:25566","error":"writeto tcp *********:35500->***********:25566: read tcp *********:35500->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.5465474,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35524","remote_address":"***********:25566","error":"writeto tcp *********:35524->***********:25566: read tcp *********:35524->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.546798,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35516","remote_address":"***********:25566","error":"writeto tcp *********:35516->***********:25566: read tcp *********:35516->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.5468588,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35534","remote_address":"***********:25566","error":"writeto tcp *********:35534->***********:25566: read tcp *********:35534->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225505.5468712,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35540","remote_address":"***********:25566","error":"writeto tcp *********:35540->***********:25566: read tcp *********:35540->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225506.2911553,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35550","remote_address":"***********:25566","error":"writeto tcp *********:35550->***********:25566: read tcp *********:35550->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225506.2917635,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35564","remote_address":"***********:25566","error":"writeto tcp *********:35564->***********:25566: read tcp *********:35564->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225506.3062053,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35572","remote_address":"***********:25566","error":"writeto tcp *********:35572->***********:25566: read tcp *********:35572->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225506.3069654,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35588","remote_address":"***********:25566","error":"writeto tcp *********:35588->***********:25566: read tcp *********:35588->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225506.3070421,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35594","remote_address":"***********:25566","error":"writeto tcp *********:35594->***********:25566: read tcp *********:35594->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225511.4668493,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35636","remote_address":"***********:25566","error":"writeto tcp *********:35636->***********:25566: read tcp *********:35636->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225511.4668493,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35634","remote_address":"***********:25566","error":"writeto tcp *********:35634->***********:25566: read tcp *********:35634->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225512.1375244,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35658","remote_address":"***********:25566","error":"writeto tcp *********:35658->***********:25566: read tcp *********:35658->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225512.1376066,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35668","remote_address":"***********:25566","error":"writeto tcp *********:35668->***********:25566: read tcp *********:35668->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225513.1339395,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35690","remote_address":"***********:25566","error":"writeto tcp *********:35690->***********:25566: read tcp *********:35690->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225513.1340075,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35692","remote_address":"***********:25566","error":"writeto tcp *********:35692->***********:25566: read tcp *********:35692->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225513.1464481,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35700","remote_address":"***********:25566","error":"writeto tcp *********:35700->***********:25566: read tcp *********:35700->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225513.1465285,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35702","remote_address":"***********:25566","error":"writeto tcp *********:35702->***********:25566: read tcp *********:35702->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225515.586196,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:37280","remote_address":"***********:25566","error":"writeto tcp *********:37280->***********:25566: read tcp *********:37280->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225515.586196,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:37258","remote_address":"***********:25566","error":"writeto tcp *********:37258->***********:25566: read tcp *********:37258->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225515.5862298,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:37272","remote_address":"***********:25566","error":"writeto tcp *********:37272->***********:25566: read tcp *********:37272->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225516.0212042,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:37284","remote_address":"***********:25566","error":"writeto tcp *********:37284->***********:25566: read tcp *********:37284->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225516.0367765,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:37308","remote_address":"***********:25566","error":"writeto tcp *********:37308->***********:25566: read tcp *********:37308->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225516.0369294,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:37322","remote_address":"***********:25566","error":"writeto tcp *********:37322->***********:25566: read tcp *********:37322->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225517.0333896,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:37346","remote_address":"***********:25566","error":"writeto tcp *********:37346->***********:25566: read tcp *********:37346->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225569.03829,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34338","remote_address":"***********:25566","error":"writeto tcp *********:34338->***********:25566: read tcp *********:34338->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225569.3585467,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34348","remote_address":"***********:25566","error":"writeto tcp *********:34348->***********:25566: read tcp *********:34348->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225569.7313204,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34356","remote_address":"***********:25566","error":"writeto tcp *********:34356->***********:25566: read tcp *********:34356->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225569.731452,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34358","remote_address":"***********:25566","error":"writeto tcp *********:34358->***********:25566: read tcp *********:34358->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225574.1432686,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34384","remote_address":"***********:25566","error":"writeto tcp *********:34384->***********:25566: read tcp *********:34384->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225574.1434882,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34386","remote_address":"***********:25566","error":"writeto tcp *********:34386->***********:25566: read tcp *********:34386->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225574.1457899,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34392","remote_address":"***********:25566","error":"writeto tcp *********:34392->***********:25566: read tcp *********:34392->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225575.0029552,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41334","remote_address":"***********:25566","error":"writeto tcp *********:41334->***********:25566: read tcp *********:41334->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225575.0064745,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41328","remote_address":"***********:25566","error":"writeto tcp *********:41328->***********:25566: read tcp *********:41328->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225575.0065267,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41330","remote_address":"***********:25566","error":"writeto tcp *********:41330->***********:25566: read tcp *********:41330->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225575.4487364,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41410","remote_address":"***********:25566","error":"writeto tcp *********:41410->***********:25566: read tcp *********:41410->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225575.4488382,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41392","remote_address":"***********:25566","error":"writeto tcp *********:41392->***********:25566: read tcp *********:41392->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758225575.4488375,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41408","remote_address":"***********:25566","error":"writeto tcp *********:41408->***********:25566: read tcp *********:41408->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226369.7166595,"logger":"layer4","msg":"handling connection","remote":"*********:51652","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226369.7903578,"logger":"layer4","msg":"handling connection","remote":"*********:51666","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226369.9052052,"logger":"layer4","msg":"handling connection","remote":"*********:51670","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226369.9052644,"logger":"layer4","msg":"handling connection","remote":"*********:51680","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226369.9406435,"logger":"layer4","msg":"handling connection","remote":"*********:51682","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.0147915,"logger":"layer4","msg":"handling connection","remote":"*********:51686","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.0149064,"logger":"layer4","msg":"handling connection","remote":"*********:51688","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.04124,"logger":"layer4","msg":"handling connection","remote":"*********:51702","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.1448903,"logger":"layer4","msg":"handling connection","remote":"*********:51718","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.1449428,"logger":"layer4","msg":"handling connection","remote":"*********:51724","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.1565194,"logger":"layer4","msg":"handling connection","remote":"*********:51738","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.1917405,"logger":"layer4","msg":"handling connection","remote":"*********:51750","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.1918242,"logger":"layer4","msg":"handling connection","remote":"*********:51752","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.4553552,"logger":"layer4","msg":"handling connection","remote":"*********:51778","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.4554195,"logger":"layer4","msg":"handling connection","remote":"*********:51776","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.4930356,"logger":"layer4","msg":"handling connection","remote":"*********:51796","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.4930925,"logger":"layer4","msg":"handling connection","remote":"*********:51810","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.5220802,"logger":"layer4","msg":"handling connection","remote":"*********:51822","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.5221329,"logger":"layer4","msg":"handling connection","remote":"*********:51832","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.5433066,"logger":"layer4","msg":"handling connection","remote":"*********:51860","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.5434155,"logger":"layer4","msg":"handling connection","remote":"*********:51848","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.8070364,"logger":"layer4","msg":"handling connection","remote":"*********:51868","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.8071048,"logger":"layer4","msg":"handling connection","remote":"*********:51878","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.8397522,"logger":"layer4","msg":"handling connection","remote":"*********:51884","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.8398032,"logger":"layer4","msg":"handling connection","remote":"*********:51888","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.8444028,"logger":"layer4","msg":"handling connection","remote":"*********:51896","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.8445115,"logger":"layer4","msg":"handling connection","remote":"*********:51902","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.9929595,"logger":"layer4","msg":"handling connection","remote":"*********:51906","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.993067,"logger":"layer4","msg":"handling connection","remote":"*********:51920","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.9946885,"logger":"layer4","msg":"handling connection","remote":"*********:51926","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226370.994883,"logger":"layer4","msg":"handling connection","remote":"*********:51936","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.2921402,"logger":"layer4","msg":"handling connection","remote":"*********:51964","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.2922072,"logger":"layer4","msg":"handling connection","remote":"*********:51952","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.2960315,"logger":"layer4","msg":"handling connection","remote":"*********:51966","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.296274,"logger":"layer4","msg":"handling connection","remote":"*********:51972","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.4619725,"logger":"layer4","msg":"handling connection","remote":"*********:51996","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.4620287,"logger":"layer4","msg":"handling connection","remote":"*********:51988","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.6434968,"logger":"layer4","msg":"handling connection","remote":"*********:52008","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.643555,"logger":"layer4","msg":"handling connection","remote":"*********:52000","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.665143,"logger":"layer4","msg":"handling connection","remote":"*********:52014","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.6652038,"logger":"layer4","msg":"handling connection","remote":"*********:52016","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.6986585,"logger":"layer4","msg":"handling connection","remote":"*********:52044","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.69873,"logger":"layer4","msg":"handling connection","remote":"*********:52032","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.827471,"logger":"layer4","msg":"handling connection","remote":"*********:52068","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.8275285,"logger":"layer4","msg":"handling connection","remote":"*********:52058","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.8493407,"logger":"layer4","msg":"handling connection","remote":"*********:52084","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.8494246,"logger":"layer4","msg":"handling connection","remote":"*********:52096","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.9962158,"logger":"layer4","msg":"handling connection","remote":"*********:52112","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.996284,"logger":"layer4","msg":"handling connection","remote":"*********:52118","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.9996126,"logger":"layer4","msg":"handling connection","remote":"*********:52124","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226371.999684,"logger":"layer4","msg":"handling connection","remote":"*********:52128","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226377.0365255,"logger":"layer4","msg":"handling connection","remote":"*********:46358","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226378.724805,"logger":"layer4","msg":"handling connection","remote":"*********:46374","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226378.7248569,"logger":"layer4","msg":"handling connection","remote":"*********:46388","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226378.746211,"logger":"layer4","msg":"handling connection","remote":"*********:46390","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226378.7462928,"logger":"layer4","msg":"handling connection","remote":"*********:46400","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226424.4408147,"logger":"layer4","msg":"handling connection","remote":"*********:42040","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226462.7122626,"logger":"layer4","msg":"handling connection","remote":"*********:58906","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226605.2977598,"logger":"layer4","msg":"handling connection","remote":"*********:43412","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226689.6628022,"logger":"layer4","msg":"handling connection","remote":"*********:43394","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226787.070802,"logger":"layer4","msg":"handling connection","remote":"*********:53864","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758226888.133177,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41032","remote_address":"***********:25566","error":"writeto tcp *********:41032->***********:25566: read tcp *********:41032->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226888.402601,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41042","remote_address":"***********:25566","error":"writeto tcp *********:41042->***********:25566: read tcp *********:41042->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226888.8436418,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41060","remote_address":"***********:25566","error":"writeto tcp *********:41060->***********:25566: read tcp *********:41060->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226888.8437746,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41062","remote_address":"***********:25566","error":"writeto tcp *********:41062->***********:25566: read tcp *********:41062->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226889.9495964,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41160","remote_address":"***********:25566","error":"writeto tcp *********:41160->***********:25566: read tcp *********:41160->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226889.9495964,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41156","remote_address":"***********:25566","error":"writeto tcp *********:41156->***********:25566: read tcp *********:41156->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226890.250655,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41190","remote_address":"***********:25566","error":"writeto tcp *********:41190->***********:25566: read tcp *********:41190->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226890.534633,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41224","remote_address":"***********:25566","error":"writeto tcp *********:41224->***********:25566: read tcp *********:41224->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758226890.534782,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41234","remote_address":"***********:25566","error":"writeto tcp *********:41234->***********:25566: read tcp *********:41234->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758227356.5529273,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758227356.554424,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758227371.0938444,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:49168","remote_address":"***********:25566","error":"writeto tcp *********:49168->***********:25566: read tcp *********:49168->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228051.282774,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35252","remote_address":"***********:25566","error":"writeto tcp *********:35252->***********:25566: read tcp *********:35252->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228090.603252,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:49484","remote_address":"***********:25566","error":"writeto tcp *********:49484->***********:25566: read tcp *********:49484->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228091.8772802,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46338","remote_address":"***********:25566","error":"writeto tcp *********:46338->***********:25566: read tcp *********:46338->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.0272875,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46346","remote_address":"***********:25566","error":"writeto tcp *********:46346->***********:25566: read tcp *********:46346->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.027402,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46372","remote_address":"***********:25566","error":"writeto tcp *********:46372->***********:25566: read tcp *********:46372->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.027402,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46362","remote_address":"***********:25566","error":"writeto tcp *********:46362->***********:25566: read tcp *********:46362->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.67093,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46382","remote_address":"***********:25566","error":"writeto tcp *********:46382->***********:25566: read tcp *********:46382->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.833125,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46402","remote_address":"***********:25566","error":"writeto tcp *********:46402->***********:25566: read tcp *********:46402->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.833124,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46390","remote_address":"***********:25566","error":"writeto tcp *********:46390->***********:25566: read tcp *********:46390->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.8522637,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46416","remote_address":"***********:25566","error":"writeto tcp *********:46416->***********:25566: read tcp *********:46416->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.8523202,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46404","remote_address":"***********:25566","error":"writeto tcp *********:46404->***********:25566: read tcp *********:46404->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.8523185,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46426","remote_address":"***********:25566","error":"writeto tcp *********:46426->***********:25566: read tcp *********:46426->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.8638446,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46434","remote_address":"***********:25566","error":"writeto tcp *********:46434->***********:25566: read tcp *********:46434->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.8798132,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46456","remote_address":"***********:25566","error":"writeto tcp *********:46456->***********:25566: read tcp *********:46456->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228092.9928114,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46462","remote_address":"***********:25566","error":"writeto tcp *********:46462->***********:25566: read tcp *********:46462->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228093.1392515,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46472","remote_address":"***********:25566","error":"writeto tcp *********:46472->***********:25566: read tcp *********:46472->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228093.1393273,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46488","remote_address":"***********:25566","error":"writeto tcp *********:46488->***********:25566: read tcp *********:46488->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228093.1527083,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46494","remote_address":"***********:25566","error":"writeto tcp *********:46494->***********:25566: read tcp *********:46494->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228100.1065092,"logger":"layer4","msg":"handling connection","remote":"*********:47312","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758228108.6565046,"logger":"layer4","msg":"handling connection","remote":"*********:34236","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758228121.2760565,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:58862","remote_address":"***********:25566","error":"writeto tcp *********:58862->***********:25566: read tcp *********:58862->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228121.551628,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:58882","remote_address":"***********:25566","error":"writeto tcp *********:58882->***********:25566: read tcp *********:58882->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228954.4497697,"logger":"layer4","msg":"handling connection","remote":"*********:54754","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758228970.7799377,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46826","remote_address":"***********:25566","error":"writeto tcp *********:46826->***********:25566: read tcp *********:46826->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228971.0566034,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:46850","remote_address":"***********:25566","error":"writeto tcp *********:46850->***********:25566: read tcp *********:46850->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228971.7357266,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:54828","remote_address":"***********:25566","error":"writeto tcp *********:54828->***********:25566: read tcp *********:54828->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758228971.7465746,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:54836","remote_address":"***********:25566","error":"writeto tcp *********:54836->***********:25566: read tcp *********:54836->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758229316.2059827,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:49706","remote_address":"***********:25566","error":"writeto tcp *********:49706->***********:25566: read tcp *********:49706->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758229394.777638,"logger":"layer4","msg":"handling connection","remote":"*********:50942","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758229465.003742,"logger":"layer4","msg":"handling connection","remote":"*********:54742","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758229469.6017365,"logger":"layer4","msg":"handling connection","remote":"*********:54748","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758229496.830475,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41554","remote_address":"***********:25566","error":"writeto tcp *********:41554->***********:25566: read tcp *********:41554->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230342.1150324,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:38444","remote_address":"***********:25566","error":"writeto tcp *********:38444->***********:25566: read tcp *********:38444->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230342.3697383,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:38470","remote_address":"***********:25566","error":"writeto tcp *********:38470->***********:25566: read tcp *********:38470->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230343.6996062,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:38592","remote_address":"***********:25566","error":"writeto tcp *********:38592->***********:25566: read tcp *********:38592->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230343.6996896,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:38606","remote_address":"***********:25566","error":"writeto tcp *********:38606->***********:25566: read tcp *********:38606->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230616.9921062,"logger":"layer4","msg":"handling connection","remote":"*********:35684","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.0406773,"logger":"layer4","msg":"handling connection","remote":"*********:35686","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.2639744,"logger":"layer4","msg":"handling connection","remote":"*********:35698","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.264038,"logger":"layer4","msg":"handling connection","remote":"*********:35714","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.2754672,"logger":"layer4","msg":"handling connection","remote":"*********:35718","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.2911036,"logger":"layer4","msg":"handling connection","remote":"*********:35724","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.2913203,"logger":"layer4","msg":"handling connection","remote":"*********:35734","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.4519334,"logger":"layer4","msg":"handling connection","remote":"*********:35754","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.4519918,"logger":"layer4","msg":"handling connection","remote":"*********:35744","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.4922886,"logger":"layer4","msg":"handling connection","remote":"*********:35758","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.492351,"logger":"layer4","msg":"handling connection","remote":"*********:35760","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.6366653,"logger":"layer4","msg":"handling connection","remote":"*********:35788","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.6367342,"logger":"layer4","msg":"handling connection","remote":"*********:35784","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.6424613,"logger":"layer4","msg":"handling connection","remote":"*********:35804","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.6425,"logger":"layer4","msg":"handling connection","remote":"*********:35806","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.8021116,"logger":"layer4","msg":"handling connection","remote":"*********:35820","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.802181,"logger":"layer4","msg":"handling connection","remote":"*********:35812","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.843884,"logger":"layer4","msg":"handling connection","remote":"*********:35834","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230617.8439605,"logger":"layer4","msg":"handling connection","remote":"*********:35846","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.312385,"logger":"layer4","msg":"handling connection","remote":"*********:48652","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.3123813,"logger":"layer4","msg":"handling connection","remote":"*********:48642","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.3337345,"logger":"layer4","msg":"handling connection","remote":"*********:48666","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.3337944,"logger":"layer4","msg":"handling connection","remote":"*********:48674","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.464646,"logger":"layer4","msg":"handling connection","remote":"*********:48688","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.4647014,"logger":"layer4","msg":"handling connection","remote":"*********:48704","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.4849389,"logger":"layer4","msg":"handling connection","remote":"*********:48726","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.4850013,"logger":"layer4","msg":"handling connection","remote":"*********:48714","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.613249,"logger":"layer4","msg":"handling connection","remote":"*********:48738","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.6133032,"logger":"layer4","msg":"handling connection","remote":"*********:48740","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.6356099,"logger":"layer4","msg":"handling connection","remote":"*********:48750","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230629.635684,"logger":"layer4","msg":"handling connection","remote":"*********:48752","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230730.628989,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:44480","remote_address":"***********:25566","error":"writeto tcp *********:44480->***********:25566: read tcp *********:44480->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230730.6291132,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:44478","remote_address":"***********:25566","error":"writeto tcp *********:44478->***********:25566: read tcp *********:44478->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230731.5318978,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:44484","remote_address":"***********:25566","error":"writeto tcp *********:44484->***********:25566: read tcp *********:44484->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230731.543366,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:44488","remote_address":"***********:25566","error":"writeto tcp *********:44488->***********:25566: read tcp *********:44488->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230732.421091,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:57390","remote_address":"***********:25566","error":"writeto tcp *********:57390->***********:25566: read tcp *********:57390->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230732.5653603,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:57402","remote_address":"***********:25566","error":"writeto tcp *********:57402->***********:25566: read tcp *********:57402->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230759.200834,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56506","remote_address":"***********:25566","error":"writeto tcp *********:56506->***********:25566: read tcp *********:56506->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230762.2569299,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:55064","remote_address":"***********:25566","error":"writeto tcp *********:55064->***********:25566: read tcp *********:55064->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758230777.0718932,"logger":"layer4","msg":"handling connection","remote":"*********:55528","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.0867555,"logger":"layer4","msg":"handling connection","remote":"*********:55536","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.2274761,"logger":"layer4","msg":"handling connection","remote":"*********:55546","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.2373483,"logger":"layer4","msg":"handling connection","remote":"*********:55552","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.635486,"logger":"layer4","msg":"handling connection","remote":"*********:55564","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.6355383,"logger":"layer4","msg":"handling connection","remote":"*********:55568","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.6392217,"logger":"layer4","msg":"handling connection","remote":"*********:55574","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.6393025,"logger":"layer4","msg":"handling connection","remote":"*********:55578","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.7521284,"logger":"layer4","msg":"handling connection","remote":"*********:55590","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.7522366,"logger":"layer4","msg":"handling connection","remote":"*********:55592","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.7903109,"logger":"layer4","msg":"handling connection","remote":"*********:55622","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230777.7903724,"logger":"layer4","msg":"handling connection","remote":"*********:55612","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.2045991,"logger":"layer4","msg":"handling connection","remote":"*********:50890","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.236765,"logger":"layer4","msg":"handling connection","remote":"*********:50906","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.496778,"logger":"layer4","msg":"handling connection","remote":"*********:49340","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.4968338,"logger":"layer4","msg":"handling connection","remote":"*********:49334","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.5384092,"logger":"layer4","msg":"handling connection","remote":"*********:49344","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.6233141,"logger":"layer4","msg":"handling connection","remote":"*********:49354","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.6233704,"logger":"layer4","msg":"handling connection","remote":"*********:49346","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.6398873,"logger":"layer4","msg":"handling connection","remote":"*********:49366","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.751763,"logger":"layer4","msg":"handling connection","remote":"*********:49372","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.7519567,"logger":"layer4","msg":"handling connection","remote":"*********:49388","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.7907078,"logger":"layer4","msg":"handling connection","remote":"*********:49400","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.8767257,"logger":"layer4","msg":"handling connection","remote":"*********:49406","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.9027798,"logger":"layer4","msg":"handling connection","remote":"*********:49412","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.9028432,"logger":"layer4","msg":"handling connection","remote":"*********:49418","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.941358,"logger":"layer4","msg":"handling connection","remote":"*********:49436","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230831.9414184,"logger":"layer4","msg":"handling connection","remote":"*********:49434","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.202729,"logger":"layer4","msg":"handling connection","remote":"*********:49450","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.2027981,"logger":"layer4","msg":"handling connection","remote":"*********:49464","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.2551029,"logger":"layer4","msg":"handling connection","remote":"*********:49468","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.2551546,"logger":"layer4","msg":"handling connection","remote":"*********:49478","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.293263,"logger":"layer4","msg":"handling connection","remote":"*********:49490","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.293365,"logger":"layer4","msg":"handling connection","remote":"*********:49496","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.673736,"logger":"layer4","msg":"handling connection","remote":"*********:49500","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.6738086,"logger":"layer4","msg":"handling connection","remote":"*********:49498","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.695373,"logger":"layer4","msg":"handling connection","remote":"*********:49522","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.6954446,"logger":"layer4","msg":"handling connection","remote":"*********:49532","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.9937818,"logger":"layer4","msg":"handling connection","remote":"*********:49540","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.9938428,"logger":"layer4","msg":"handling connection","remote":"*********:49556","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.9979038,"logger":"layer4","msg":"handling connection","remote":"*********:49574","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230832.9979794,"logger":"layer4","msg":"handling connection","remote":"*********:49568","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230833.1783915,"logger":"layer4","msg":"handling connection","remote":"*********:49592","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230833.178459,"logger":"layer4","msg":"handling connection","remote":"*********:49590","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230833.1992714,"logger":"layer4","msg":"handling connection","remote":"*********:49618","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230833.1993368,"logger":"layer4","msg":"handling connection","remote":"*********:49628","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758230942.9339788,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:56394","remote_address":"***********:25566","error":"writeto tcp *********:56394->***********:25566: read tcp *********:56394->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758231484.5524316,"logger":"layer4","msg":"handling connection","remote":"*********:37582","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758231665.2063272,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:52566","remote_address":"***********:25566","error":"writeto tcp *********:52566->***********:25566: read tcp *********:52566->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758231665.4631782,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:52582","remote_address":"***********:25566","error":"writeto tcp *********:52582->***********:25566: read tcp *********:52582->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758231665.5509794,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:52594","remote_address":"***********:25566","error":"writeto tcp *********:52594->***********:25566: read tcp *********:52594->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758231665.992849,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:52612","remote_address":"***********:25566","error":"writeto tcp *********:52612->***********:25566: read tcp *********:52612->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758231666.139244,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:52626","remote_address":"***********:25566","error":"writeto tcp *********:52626->***********:25566: read tcp *********:52626->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758232459.5861804,"logger":"layer4","msg":"handling connection","remote":"*********:53884","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758232558.9234312,"logger":"layer4","msg":"handling connection","remote":"*********:55494","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758254418.4411817,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34182","remote_address":"***********:25566","error":"writeto tcp *********:34182->***********:25566: read tcp *********:34182->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758288444.3165786,"logger":"admin.api","msg":"received request","method":"POST","host":"localhost:2019","uri":"/load","remote_ip":"127.0.0.1","remote_port":"37004","headers":{"Accept-Encoding":["gzip"],"Content-Length":["545"],"Content-Type":["application/json"],"Origin":["http://localhost:2019"],"User-Agent":["Go-http-client/1.1"]}}
{"level":"info","ts":1758288444.3179493,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//127.0.0.1:2019","//localhost:2019","//[::1]:2019"]}
{"level":"info","ts":1758288444.3183117,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1758288444.3183217,"logger":"admin.api","msg":"load complete"}
{"level":"info","ts":1758288444.3192234,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"error","ts":1758340823.037509,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:40580","remote_address":"***********:25566","error":"writeto tcp *********:40580->***********:25566: read tcp *********:40580->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758340839.4842803,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1758340839.4843757,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1758340839.505067,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1758340839.5051026,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1758340840.706051,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758340840.7068467,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758340904.9683676,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:38818","remote_address":"***********:25566","error":"writeto tcp **********:38818->***********:25566: read tcp **********:38818->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758355392.2498286,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758355392.2773438,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758355436.4915655,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:50736","remote_address":"***********:25566","error":"writeto tcp *********:50736->***********:25566: read tcp *********:50736->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758388823.593154,"logger":"layer4","msg":"handling connection","remote":"*********:37148","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758388974.3366475,"logger":"layer4","msg":"handling connection","remote":"*********:41088","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389119.5037663,"logger":"layer4","msg":"handling connection","remote":"*********:42636","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389155.1179197,"logger":"layer4","msg":"handling connection","remote":"*********:48400","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389305.836101,"logger":"layer4","msg":"handling connection","remote":"*********:46884","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389456.597118,"logger":"layer4","msg":"handling connection","remote":"*********:40218","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389519.2398949,"logger":"layer4","msg":"handling connection","remote":"*********:37278","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389607.3918636,"logger":"layer4","msg":"handling connection","remote":"*********:57580","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389788.1131284,"logger":"layer4","msg":"handling connection","remote":"*********:34902","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758389825.9767256,"logger":"layer4","msg":"handling connection","remote":"*********:54764","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758390028.8977256,"logger":"layer4","msg":"handling connection","remote":"*********:58048","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758390209.650144,"logger":"layer4","msg":"handling connection","remote":"*********:33704","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758390244.9248161,"logger":"layer4","msg":"handling connection","remote":"*********:40988","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1758390420.5861335,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758390420.5872386,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1758403835.7116325,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758403835.7123046,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758403950.3868291,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:53712","remote_address":"***********:25566","error":"writeto tcp *********:53712->***********:25566: read tcp *********:53712->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758513630.1775014,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:42836","remote_address":"***********:25566","error":"writeto tcp *********:42836->***********:25566: read tcp *********:42836->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758513630.53749,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:42844","remote_address":"***********:25566","error":"writeto tcp *********:42844->***********:25566: read tcp *********:42844->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758513637.356424,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:42856","remote_address":"***********:25566","error":"writeto tcp *********:42856->***********:25566: read tcp *********:42856->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758600040.2466552,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:35340","remote_address":"***********:25566","error":"writeto tcp *********:35340->***********:25566: read tcp *********:35340->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758600098.564808,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:34856","remote_address":"***********:25566","error":"writeto tcp *********:34856->***********:25566: read tcp *********:34856->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758664316.658813,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//[::1]:2019","//127.0.0.1:2019","//localhost:2019"]}
{"level":"info","ts":1758664316.6596892,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758664356.5963044,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:49982","remote_address":"***********:25566","error":"writeto tcp *********:49982->***********:25566: read tcp *********:49982->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758664363.036105,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:48436","remote_address":"***********:25566","error":"writeto tcp *********:48436->***********:25566: read tcp *********:48436->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758750569.902239,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1758750569.9032578,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1758750569.903535,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1758750569.903543,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1758756618.31869,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758756618.3199933,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758756687.6962147,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:55228","remote_address":"***********:25566","error":"writeto tcp *********:55228->***********:25566: read tcp *********:55228->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1758756736.9356947,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:55186","remote_address":"***********:25566","error":"writeto tcp *********:55186->***********:25566: read tcp *********:55186->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758902519.478432,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1758902519.4799254,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1758902519.480878,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1758902519.480949,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1758902613.4552016,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758902613.4560225,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758902643.4018078,"logger":"layer4","msg":"handling connection","remote":"*********:34416","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758902803.026797,"logger":"layer4","msg":"handling connection","remote":"*********:54346","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758902855.1058428,"logger":"layer4","msg":"handling connection","remote":"*********:49496","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758902954.7226028,"logger":"layer4","msg":"handling connection","remote":"*********:60338","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758902965.3558595,"logger":"layer4","msg":"handling connection","remote":"*********:47752","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903105.6839964,"logger":"layer4","msg":"handling connection","remote":"*********:41626","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903126.1463857,"logger":"layer4","msg":"handling connection","remote":"*********:54534","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903255.5514393,"logger":"layer4","msg":"handling connection","remote":"*********:60712","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903303.1278908,"logger":"layer4","msg":"handling connection","remote":"*********:53080","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903342.81639,"logger":"layer4","msg":"handling connection","remote":"*********:50804","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903371.4586549,"logger":"layer4","msg":"handling connection","remote":"*********:43070","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903406.5774379,"logger":"layer4","msg":"handling connection","remote":"*********:57386","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903557.5726292,"logger":"layer4","msg":"handling connection","remote":"*********:46254","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903619.6926122,"logger":"layer4","msg":"handling connection","remote":"*********:53858","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903703.457953,"logger":"layer4","msg":"handling connection","remote":"*********:50970","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903708.6278918,"logger":"layer4","msg":"handling connection","remote":"*********:51800","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903763.0460536,"logger":"layer4","msg":"handling connection","remote":"*********:52678","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903800.1734464,"logger":"layer4","msg":"handling connection","remote":"*********:59084","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903859.043266,"logger":"layer4","msg":"handling connection","remote":"*********:44476","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758903888.255046,"logger":"layer4","msg":"handling connection","remote":"*********:44688","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904009.111626,"logger":"layer4","msg":"handling connection","remote":"*********:55258","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904057.3165905,"logger":"layer4","msg":"handling connection","remote":"*********:38336","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904152.3789854,"logger":"layer4","msg":"handling connection","remote":"*********:51368","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904160.3623621,"logger":"layer4","msg":"handling connection","remote":"*********:38360","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904167.3183646,"logger":"layer4","msg":"handling connection","remote":"*********:38374","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904311.044972,"logger":"layer4","msg":"handling connection","remote":"*********:37782","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904362.5213022,"logger":"layer4","msg":"handling connection","remote":"*********:36654","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904461.9580073,"logger":"layer4","msg":"handling connection","remote":"*********:47896","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904533.8081584,"logger":"layer4","msg":"handling connection","remote":"*********:36256","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904557.5436168,"logger":"layer4","msg":"handling connection","remote":"*********:60318","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904557.6385994,"logger":"layer4","msg":"handling connection","remote":"*********:60332","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904565.035197,"logger":"layer4","msg":"handling connection","remote":"*********:40848","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904736.7974224,"logger":"layer4","msg":"handling connection","remote":"*********:51968","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904790.309719,"logger":"layer4","msg":"handling connection","remote":"*********:44480","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904887.4810185,"logger":"layer4","msg":"handling connection","remote":"*********:58608","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758904974.412792,"logger":"layer4","msg":"handling connection","remote":"*********:54114","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905069.5052588,"logger":"layer4","msg":"handling connection","remote":"*********:57070","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905206.2164454,"logger":"layer4","msg":"handling connection","remote":"*********:37998","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905211.5073633,"logger":"layer4","msg":"handling connection","remote":"*********:43642","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905277.5347915,"logger":"layer4","msg":"handling connection","remote":"*********:44932","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905293.1638956,"logger":"layer4","msg":"handling connection","remote":"*********:35878","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905322.5385976,"logger":"layer4","msg":"handling connection","remote":"*********:54760","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905405.6344965,"logger":"layer4","msg":"handling connection","remote":"*********:49662","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905446.2340271,"logger":"layer4","msg":"handling connection","remote":"*********:33056","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905456.8083315,"logger":"layer4","msg":"handling connection","remote":"*********:33654","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1758905643.337229,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1758905643.3411977,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1758905643.3416238,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1758905643.3417041,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1758905716.156504,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758905716.158001,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758905735.073907,"logger":"layer4","msg":"handling connection","remote":"*********:34160","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905740.6794844,"logger":"layer4","msg":"handling connection","remote":"*********:59014","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905752.0802324,"logger":"layer4","msg":"handling connection","remote":"*********:52620","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905885.6205487,"logger":"layer4","msg":"handling connection","remote":"*********:45938","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758905956.7139509,"logger":"layer4","msg":"handling connection","remote":"*********:37856","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758906003.7315328,"logger":"layer4","msg":"handling connection","remote":"*********:46986","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758906036.1381857,"logger":"layer4","msg":"handling connection","remote":"*********:33848","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758906115.3145916,"logger":"layer4","msg":"handling connection","remote":"*********:56234","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758906290.2452624,"logger":"layer4","msg":"handling connection","remote":"*********:57388","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758906331.1162372,"logger":"layer4","msg":"handling connection","remote":"*********:50662","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758906425.206799,"logger":"layer4","msg":"handling connection","remote":"*********:45182","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1758906541.0484378,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1758906541.048952,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1758906541.0491915,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1758906541.0492558,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1758906613.2964895,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758906613.2972093,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758906699.1208415,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:50630","remote_address":"***********:25566","error":"writeto tcp *********:50630->***********:25566: read tcp *********:50630->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1758989766.378539,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758989766.3793557,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1758994520.016881,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1758994520.0178,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1758994520.0180457,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1758994520.0182238,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1758994591.7019506,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//[::1]:2019","//127.0.0.1:2019","//localhost:2019"]}
{"level":"info","ts":1758994591.70258,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758994673.0190694,"logger":"layer4","msg":"handling connection","remote":"*********:54892","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758994792.5726058,"logger":"layer4","msg":"handling connection","remote":"*********:55076","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758994821.654731,"logger":"layer4","msg":"handling connection","remote":"*********:53258","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758994829.001528,"logger":"layer4","msg":"handling connection","remote":"*********:37868","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758994990.84346,"logger":"layer4","msg":"handling connection","remote":"*********:46652","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995142.1084027,"logger":"layer4","msg":"handling connection","remote":"*********:43464","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995225.7761416,"logger":"layer4","msg":"handling connection","remote":"*********:55072","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995228.1088228,"logger":"layer4","msg":"handling connection","remote":"*********:55076","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995322.7851095,"logger":"layer4","msg":"handling connection","remote":"*********:46954","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995473.302903,"logger":"layer4","msg":"handling connection","remote":"*********:46480","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995618.5753667,"logger":"layer4","msg":"handling connection","remote":"*********:34086","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995639.6943746,"logger":"layer4","msg":"handling connection","remote":"*********:34132","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995649.1034496,"logger":"layer4","msg":"handling connection","remote":"*********:51994","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995808.3742673,"logger":"layer4","msg":"handling connection","remote":"*********:54010","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758995907.2229135,"logger":"layer4","msg":"handling connection","remote":"*********:49240","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996117.127668,"logger":"layer4","msg":"handling connection","remote":"*********:34404","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996291.2663598,"logger":"layer4","msg":"handling connection","remote":"*********:46384","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996356.236943,"logger":"layer4","msg":"handling connection","remote":"*********:53360","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996411.709093,"logger":"layer4","msg":"handling connection","remote":"*********:54840","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996516.3051794,"logger":"layer4","msg":"handling connection","remote":"*********:35686","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996670.507875,"logger":"layer4","msg":"handling connection","remote":"*********:38584","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996694.9191236,"logger":"layer4","msg":"handling connection","remote":"*********:45278","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996755.7733276,"logger":"layer4","msg":"handling connection","remote":"*********:60176","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996787.7887938,"logger":"layer4","msg":"handling connection","remote":"*********:33326","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996926.403004,"logger":"layer4","msg":"handling connection","remote":"*********:45282","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758996957.3255794,"logger":"layer4","msg":"handling connection","remote":"*********:36830","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997042.59334,"logger":"layer4","msg":"handling connection","remote":"*********:41652","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997116.6182249,"logger":"layer4","msg":"handling connection","remote":"*********:47984","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997256.8183393,"logger":"layer4","msg":"handling connection","remote":"*********:50950","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997561.7270615,"logger":"layer4","msg":"handling connection","remote":"*********:43312","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997573.0204852,"logger":"layer4","msg":"handling connection","remote":"*********:60768","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997585.284544,"logger":"layer4","msg":"handling connection","remote":"*********:56380","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997822.2586577,"logger":"layer4","msg":"handling connection","remote":"*********:43512","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997839.1508443,"logger":"layer4","msg":"handling connection","remote":"*********:59516","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997961.7795339,"logger":"layer4","msg":"handling connection","remote":"*********:35230","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758997982.7860227,"logger":"layer4","msg":"handling connection","remote":"*********:40400","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998007.813032,"logger":"layer4","msg":"handling connection","remote":"*********:34946","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998071.8493874,"logger":"layer4","msg":"handling connection","remote":"*********:51760","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998186.7116835,"logger":"layer4","msg":"handling connection","remote":"*********:41866","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998186.9220498,"logger":"layer4","msg":"handling connection","remote":"*********:41876","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998204.2208116,"logger":"layer4","msg":"handling connection","remote":"*********:59998","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998222.4869924,"logger":"layer4","msg":"handling connection","remote":"*********:44650","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998243.534498,"logger":"layer4","msg":"handling connection","remote":"*********:46620","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1758998387.413708,"logger":"layer4","msg":"handling connection","remote":"*********:36960","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1758998403.34541,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1758998403.350007,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1758998403.3509393,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1758998403.3512928,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1758998520.3050745,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1758998520.3062701,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1758998538.3407645,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:41762","remote_address":"***********:25566","error":"writeto tcp *********:41762->***********:25566: read tcp *********:41762->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1759000609.2753692,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759000609.2758706,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759000609.276085,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759000609.276149,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759000622.4974577,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759000622.4982967,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1759001103.5217457,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759001103.5218973,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759001103.5221038,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759001103.52235,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759001177.1130867,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759001177.1140354,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1759001205.0180027,"logger":"layer4","msg":"handling connection","remote":"*********:38494","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001355.740692,"logger":"layer4","msg":"handling connection","remote":"*********:59002","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001516.8633325,"logger":"layer4","msg":"handling connection","remote":"*********:59426","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001536.7543461,"logger":"layer4","msg":"handling connection","remote":"*********:37322","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001672.1866412,"logger":"layer4","msg":"handling connection","remote":"*********:45098","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001687.3465772,"logger":"layer4","msg":"handling connection","remote":"*********:52040","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001745.3112962,"logger":"layer4","msg":"handling connection","remote":"*********:60196","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001787.1013274,"logger":"layer4","msg":"handling connection","remote":"*********:39294","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001787.1527357,"logger":"layer4","msg":"handling connection","remote":"*********:39300","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759001794.07712,"logger":"layer4","msg":"handling connection","remote":"*********:42370","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1759001798.6980062,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759001798.6981633,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759001798.7015102,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759001798.7017295,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759001908.2461948,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759001908.2469964,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1759001985.4466736,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759001985.4474921,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759001985.4478152,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759001985.448323,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759001998.8033714,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759001998.8050132,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1759002075.6569366,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:59446","remote_address":"***********:25566","error":"writeto tcp **********:59446->***********:25566: read tcp **********:59446->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759032058.7913113,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:44210","remote_address":"***********:25566","error":"writeto tcp **********:44210->***********:25566: read tcp **********:44210->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759032107.9262474,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:36286","remote_address":"***********:25566","error":"writeto tcp **********:36286->***********:25566: read tcp **********:36286->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759118491.8679686,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:46298","remote_address":"***********:25566","error":"writeto tcp **********:46298->***********:25566: read tcp **********:46298->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1759233233.0868297,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759233233.0873973,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759233233.0922928,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759233233.0923858,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759233403.6876101,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759233403.6889925,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1759233729.1682196,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759233729.1686778,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759233729.1703093,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759233729.170374,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759233749.181504,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759233749.1831834,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1759233783.0687325,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:54906","remote_address":"***********:25566","error":"writeto tcp **********:54906->***********:25566: read tcp **********:54906->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759233803.356445,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:50246","remote_address":"***********:25566","error":"writeto tcp **********:50246->***********:25566: read tcp **********:50246->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1759233814.762525,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759233814.762625,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759233814.7628202,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759233814.7628653,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759233836.1936169,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759233836.1944919,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1759233876.9527762,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759233876.952905,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759233876.9530723,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759233876.9531143,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759235839.308341,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759235839.3092082,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1759235877.08595,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:45528","remote_address":"***********:25566","error":"writeto tcp **********:45528->***********:25566: read tcp **********:45528->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759235882.2244115,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:45530","remote_address":"***********:25566","error":"writeto tcp **********:45530->***********:25566: read tcp **********:45530->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759235945.7940483,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:46386","remote_address":"***********:25566","error":"writeto tcp **********:46386->***********:25566: read tcp **********:46386->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1759238668.9179287,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759238668.9184046,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759238668.9227333,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759238668.9228165,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759238752.9837747,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//127.0.0.1:2019","//localhost:2019","//[::1]:2019"]}
{"level":"info","ts":1759238752.9845424,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1759256657.2602186,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759256657.2615783,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759256657.4125283,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759256657.4135604,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759256833.9398675,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759256833.941304,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1759256841.5368905,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759256841.5371118,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759256841.537975,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759256841.5380356,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759256888.1178312,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759256888.118517,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1759256923.7497776,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:43288","remote_address":"***********:25566","error":"writeto tcp *********:43288->***********:25566: read tcp *********:43288->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759256967.862414,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:43286","remote_address":"***********:25566","error":"writeto tcp *********:43286->***********:25566: read tcp *********:43286->***********:25566: read: connection reset by peer"}
{"level":"info","ts":1759291211.3235826,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759291211.3246603,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759291211.3250873,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759291211.3251407,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759401805.697386,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759401805.6977305,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1759401950.6329627,"logger":"layer4","msg":"handling connection","remote":"**********:43098","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402030.2725117,"logger":"layer4","msg":"handling connection","remote":"**********:53010","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402100.6096215,"logger":"layer4","msg":"handling connection","remote":"**********:44914","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402106.6260564,"logger":"layer4","msg":"handling connection","remote":"**********:39144","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402271.8699725,"logger":"layer4","msg":"handling connection","remote":"**********:59978","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402290.5575614,"logger":"layer4","msg":"handling connection","remote":"**********:40838","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402306.9517071,"logger":"layer4","msg":"handling connection","remote":"**********:50212","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402308.7876875,"logger":"layer4","msg":"handling connection","remote":"**********:50220","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402397.0433934,"logger":"layer4","msg":"handling connection","remote":"**********:33442","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402422.2844903,"logger":"layer4","msg":"handling connection","remote":"**********:52180","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402454.401481,"logger":"layer4","msg":"handling connection","remote":"**********:35566","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402499.9517028,"logger":"layer4","msg":"handling connection","remote":"**********:47664","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402622.555094,"logger":"layer4","msg":"handling connection","remote":"**********:37406","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402667.3037555,"logger":"layer4","msg":"handling connection","remote":"**********:59354","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402725.5131755,"logger":"layer4","msg":"handling connection","remote":"**********:45654","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402801.8075614,"logger":"layer4","msg":"handling connection","remote":"**********:51160","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402817.872973,"logger":"layer4","msg":"handling connection","remote":"**********:43300","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402916.6367667,"logger":"layer4","msg":"handling connection","remote":"**********:53560","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402916.7954779,"logger":"layer4","msg":"handling connection","remote":"**********:53574","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402917.7969818,"logger":"layer4","msg":"handling connection","remote":"**********:53580","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402917.8465962,"logger":"layer4","msg":"handling connection","remote":"**********:53586","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402917.9643795,"logger":"layer4","msg":"handling connection","remote":"**********:53594","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402917.9971511,"logger":"layer4","msg":"handling connection","remote":"**********:53606","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402918.1326792,"logger":"layer4","msg":"handling connection","remote":"**********:53618","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402918.182188,"logger":"layer4","msg":"handling connection","remote":"**********:53624","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.002855,"logger":"layer4","msg":"handling connection","remote":"**********:53626","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.035669,"logger":"layer4","msg":"handling connection","remote":"**********:53638","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.1702213,"logger":"layer4","msg":"handling connection","remote":"**********:53642","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.1863568,"logger":"layer4","msg":"handling connection","remote":"**********:53652","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.3400533,"logger":"layer4","msg":"handling connection","remote":"**********:53660","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.3880482,"logger":"layer4","msg":"handling connection","remote":"**********:53676","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.4889371,"logger":"layer4","msg":"handling connection","remote":"**********:53678","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.5392237,"logger":"layer4","msg":"handling connection","remote":"**********:53686","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.6398556,"logger":"layer4","msg":"handling connection","remote":"**********:53692","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402919.6893246,"logger":"layer4","msg":"handling connection","remote":"**********:53696","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402968.663908,"logger":"layer4","msg":"handling connection","remote":"**********:52310","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402974.3489149,"logger":"layer4","msg":"handling connection","remote":"**********:41658","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759402995.5830195,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:39552","remote_address":"***********:25566","error":"writeto tcp **********:39552->***********:25566: read tcp **********:39552->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759402996.3545983,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:39592","remote_address":"***********:25566","error":"writeto tcp **********:39592->***********:25566: read tcp **********:39592->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759404783.2759864,"logger":"layer4","msg":"handling connection","remote":"**********:60618","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759404813.1236193,"logger":"layer4","msg":"handling connection","remote":"**********:53716","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759404847.7254112,"logger":"layer4","msg":"handling connection","remote":"**********:46850","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"error","ts":1759405151.7238412,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:35074","remote_address":"***********:25566","error":"writeto tcp **********:35074->***********:25566: read tcp **********:35074->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759406381.3968291,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:47588","remote_address":"***********:25566","error":"writeto tcp **********:47588->***********:25566: read tcp **********:47588->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759435121.1803226,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"**********:57132","remote_address":"***********:25566","error":"writeto tcp **********:57132->***********:25566: read tcp **********:57132->***********:25566: read: connection reset by peer"}
{"level":"error","ts":1759438624.213057,"logger":"layer4","msg":"handling connection","remote":"**********:41588","error":"dial tcp ***********:25566: connect: connection refused"}
{"level":"info","ts":1759438658.949901,"msg":"shutting down apps, then terminating","signal":"SIGTERM"}
{"level":"warn","ts":1759438658.9499826,"msg":"exiting; byeee!! 👋","signal":"SIGTERM"}
{"level":"info","ts":1759438658.9502535,"logger":"admin","msg":"stopped previous server","address":"localhost:2019"}
{"level":"info","ts":1759438658.950267,"msg":"shutdown complete","signal":"SIGTERM","exit_code":0}
{"level":"info","ts":1759439362.2333262,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759439362.2337584,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"info","ts":1759511190.6169472,"logger":"admin","msg":"admin endpoint started","address":"localhost:2019","enforce_origin":false,"origins":["//localhost:2019","//[::1]:2019","//127.0.0.1:2019"]}
{"level":"info","ts":1759511190.6178422,"msg":"autosaved config (load with --resume flag)","file":"/config/caddy/autosave.json"}
{"level":"error","ts":1759511206.6937776,"logger":"layer4.handlers.proxy","msg":"upstream connection","local_address":"*********:33990","remote_address":"***********:25566","error":"writeto tcp *********:33990->***********:25566: read tcp *********:33990->***********:25566: read: connection reset by peer"}
