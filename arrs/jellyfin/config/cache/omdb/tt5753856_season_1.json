{"Title": "Dark", "seriesID": "tt5753856", "Season": 1, "totalSeasons": 3, "Episodes": [{"Title": "Geheimnisse", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "51 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>", "Actors": "<PERSON>, <PERSON><PERSON>, <PERSON>", "Plot": "The small German town of Winden is shaken by the disappearance of a teenage boy. While the townsfolk are occupied with secrets of their own, at nightfall a group of teenagers attempts to recover something the missing boy may have ...", "Language": "German, English", "Country": "United States, Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMjI3Mjk2MjUyOV5BMl5BanBnXkFtZTgwMTg1OTgyNDM@._V1_SX300.jpg", "imdbRating": "8.2", "imdbVotes": "17886", "imdbID": "tt6305578", "Episode": 1}, {"Title": "<PERSON><PERSON><PERSON>", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "44 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>", "Plot": "When a grim discovery leaves the police baffled, <PERSON> seeks a search warrant for the power plant. A mysterious stranger checks into the hotel.", "Language": "English, German", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMTg2ODU2NjA2MF5BMl5BanBnXkFtZTgwMTU1OTgyNDM@._V1_SX300.jpg", "imdbRating": "8.1", "imdbVotes": "15212", "imdbID": "tt7305776", "Episode": 2}, {"Title": "Gestern und heute", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "45 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON><PERSON>, <PERSON>", "Plot": "It's 1986, and <PERSON>'s brother, <PERSON><PERSON>, has been missing for a month. Confusion reigns as past and present intertwine.", "Language": "English, German", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMTEyODc1Njc3NjleQTJeQWpwZ15BbWU4MDc2NTk4MjQz._V1_SX300.jpg", "imdbRating": "8.5", "imdbVotes": "14237", "imdbID": "tt7305820", "Episode": 3}, {"Title": "Doppelleben", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "47 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "Bizarre occurrences give <PERSON> a sense of déjà vu, and she suspects <PERSON> is hiding something. <PERSON><PERSON><PERSON> snaps when <PERSON> confronts her.", "Language": "English, German, German Sign ", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMTg5MTM1MTg4MV5BMl5BanBnXkFtZTgwNjY1OTgyNDM@._V1_SX300.jpg", "imdbRating": "8.1", "imdbVotes": "13428", "imdbID": "tt7305818", "Episode": 4}, {"Title": "Wahrheiten", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "45 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>", "Actors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> takes her obsession with <PERSON> too far. The stranger asks <PERSON> to deliver an important package. <PERSON> is torn between <PERSON> and <PERSON><PERSON><PERSON>.", "Language": "English, German, German Sign ", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMTAyMTQyNjM3NzFeQTJeQWpwZ15BbWU4MDA4NTk4MjQz._V1_SX300.jpg", "imdbRating": "8.8", "imdbVotes": "14071", "imdbID": "tt7313308", "Episode": 5}, {"Title": "<PERSON><PERSON> Mundus <PERSON>", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "51 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> looks to the past for answers and dredges up disturbing family secrets. Armed with new tools, <PERSON> probes the cave's murky depths.", "Language": "English, German", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMTU3Mzc2OTczNl5BMl5BanBnXkFtZTgwNjc1OTgyNDM@._V1_SX300.jpg", "imdbRating": "9.0", "imdbVotes": "13752", "imdbID": "tt7313312", "Episode": 6}, {"Title": "Kreuzwege", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "52 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> questions a frail and frightened <PERSON><PERSON> in the nursing home. <PERSON> searches for <PERSON><PERSON><PERSON>, but the stranger warns him about meddling with the past.", "Language": "English, German", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BNmNhZDBjMDgtOWE3Yi00OGNmLWEyNjYtNDZkNjVlY2ZlOGE5XkEyXkFqcGdeQXVyODEyMzI2OTE@._V1_SX300.jpg", "imdbRating": "8.6", "imdbVotes": "12564", "imdbID": "tt7305824", "Episode": 7}, {"Title": "Was man sät, das wird man ernten", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "50 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "In 1953, the disfigured bodies of two boys are exhumed at a construction site, the future location of Winden's nuclear power plant.", "Language": "English, German", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMjM3OTIzMTc0NV5BMl5BanBnXkFtZTgwOTY1OTgyNDM@._V1_SX300.jpg", "imdbRating": "9.1", "imdbVotes": "14143", "imdbID": "tt7313316", "Episode": 8}, {"Title": "Alles ist jetzt", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "55 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> runs afoul of the law, <PERSON><PERSON> tries to dodge <PERSON><PERSON>, <PERSON> harnesses the cave's powers, and <PERSON><PERSON><PERSON> lashes out at <PERSON>.", "Language": "English, German", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMmEzOTk5Y2YtM2IzMS00MGQ5LThiNGQtNzE2ZjBlM2JmMGU0XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.6", "imdbVotes": "12495", "imdbID": "tt7313320", "Episode": 9}, {"Title": "Alpha und Omega", "Year": "2017", "Rated": "TV-MA", "Released": "01 Dec 2017", "Runtime": "57 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON><PERSON> bo <PERSON>", "Writer": "<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "Actors": "<PERSON>, <PERSON><PERSON>, <PERSON>", "Plot": "<PERSON> gets a shock. <PERSON> learns the truth about his family, but there are more surprises still to come. <PERSON><PERSON> makes a sacrifice.", "Language": "English, German", "Country": "Germany", "Poster": "https://m.media-amazon.com/images/M/MV5BMjM4MDc3MTMwMl5BMl5BanBnXkFtZTgwMzAzNTQxNDM@._V1_SX300.jpg", "imdbRating": "9.1", "imdbVotes": "14782", "imdbID": "tt7313322", "Episode": 10}], "Response": "True"}