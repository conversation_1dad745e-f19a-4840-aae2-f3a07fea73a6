{"Title": "Black Mirror", "seriesID": "tt2085059", "Season": 5, "totalSeasons": 7, "Episodes": [{"Title": "Striking Vipers", "Year": "2019", "Rated": "TV-MA", "Released": "05 Jun 2019", "Runtime": "61 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>", "Actors": "<PERSON>, <PERSON><PERSON>, <PERSON>", "Plot": "Two estranged college friends reunite in later life, triggering a series of events that could alter their lives forever.", "Language": "English", "Country": "United Kingdom", "Poster": "https://m.media-amazon.com/images/M/MV5BNWNkY2QwMmEtYTc3NS00MzdlLTgxMTItMWIzNWVlYTc3NGMzXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "6.8", "imdbVotes": "49531", "imdbID": "tt8503298", "Episode": 1}, {"Title": "Smithereens", "Year": "2019", "Rated": "TV-MA", "Released": "05 Jun 2019", "Runtime": "70 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>", "Actors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "Plot": "A cab driver with an agenda becomes the center of attention on a day that rapidly spirals out of control.", "Language": "English", "Country": "United Kingdom", "Awards": "1 nomination", "Poster": "https://m.media-amazon.com/images/M/MV5BNWM3YTEzZTMtOTRkMC00NzVkLTlhNzItNWFiOWYzMWU2NDI0XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.5", "imdbVotes": "42525", "imdbID": "tt8758202", "Episode": 2}, {"Title": "<PERSON>, <PERSON> and <PERSON>", "Year": "2019", "Rated": "TV-MA", "Released": "05 Jun 2019", "Runtime": "67 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>", "Actors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "Plot": "A lonely teenager yearns to connect with her favorite pop star - whose charmed existence isn't quite as rosy as it appears...", "Language": "English", "Country": "United Kingdom", "Poster": "https://m.media-amazon.com/images/M/MV5BMGViNDgzMGYtZDJiOC00ZjRkLWIwYTktNWM4ZDUyZDA1OGE2XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "6.1", "imdbVotes": "42784", "imdbID": "tt9053874", "Episode": 3}], "Response": "True"}