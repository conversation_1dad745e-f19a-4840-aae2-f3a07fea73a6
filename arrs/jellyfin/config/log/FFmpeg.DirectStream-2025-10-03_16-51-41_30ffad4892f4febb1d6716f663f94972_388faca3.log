{"Protocol":0,"Id":"30ffad4892f4febb1d6716f663f94972","Path":"/CONTENIDO/SERIES/<PERSON>/Season 8/Dexter - S08E05 - This Little Piggy WEBDL-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":2117060879,"Name":"Dexter - S08E05 - This Little Piggy WEBDL-1080p","IsRemote":false,"ETag":"36261d01dcbf063421209030673bef89","RunTimeTicks":28953920000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":5849462,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano - Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Ingl\u00E9s - English - Dolby Digital\u002B - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Completos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Completos] - Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s [Para sordos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Ingl\u00E9s [Para sordos] - English - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":6617462,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E05 - This Little Piggy WEBDL-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 libfdk_aac -ac 2 -ab 256000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "b873b36da466e75900e9161dd6d6c8dd-1.mp4" -start_number 0 -hls_segment_filename "/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd.m3u8"


ffmpeg version 7.1.2-Jellyfin Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 13 (Ubuntu 13.3.0-6ubuntu2~24.04)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, matroska,webm, from 'file:/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E05 - This Little Piggy WEBDL-1080p.mkv':
  Metadata:
    title           : Dexter S08E05 1080p NF WEB-DL DDP2.0 x264 - TSeD @danixd
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2018-08-18T08:44:53.000000Z
  Duration: 00:48:15.39, start: 0.000000, bitrate: 5849 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 5079588
        DURATION-eng    : 00:48:15.017000000
        NUMBER_OF_FRAMES-eng: 69411
        NUMBER_OF_BYTES-eng: 1838186711
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:44:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        title           : Castellano
        BPS-eng         : 128000
        DURATION-eng    : 00:48:15.136000000
        NUMBER_OF_FRAMES-eng: 90473
        NUMBER_OF_BYTES-eng: 46322176
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:44:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(eng): Audio: eac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        title           : Inglés
        BPS-eng         : 640000
        DURATION-eng    : 00:48:15.392000000
        NUMBER_OF_FRAMES-eng: 90481
        NUMBER_OF_BYTES-eng: 231631360
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:44:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(spa): Subtitle: subrip (srt)
      Metadata:
        title           : Castellano [Completos]
        BPS-eng         : 71
        DURATION-eng    : 00:46:36.794000000
        NUMBER_OF_FRAMES-eng: 736
        NUMBER_OF_BYTES-eng: 24886
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:44:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(eng): Subtitle: subrip (srt)
      Metadata:
        title           : Inglés [Para sordos]
        BPS-eng         : 75
        DURATION-eng    : 00:46:38.171000000
        NUMBER_OF_FRAMES-eng: 1457
        NUMBER_OF_BYTES-eng: 26386
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:44:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (eac3 (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd-1.mp4' for writing
Output #0, hls, to '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd.m3u8':
  Metadata:
    encoder         : Lavf61.7.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: aac, 48000 Hz, stereo, s16, 256 kb/s (default)
      Metadata:
        encoder         : Lavc61.19.101 libfdk_aac
      Side data:
        cpb: bitrate max/min/avg: 256000/256000/256000 buffer size: 0 vbv_delay: N/A
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd0.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd1.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd2.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd3.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd4.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd5.mp4' for writing
frame=  973 fps=0.0 q=-1.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd6.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd7.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd8.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd9.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd10.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd11.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd12.mp4' for writing
frame= 1944 fps=1944 q=-1.0 size=N/A time=00:00:40.59 bitrate=N/A speed=40.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd13.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd14.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd15.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd16.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd17.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd18.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd19.mp4' for writing
frame= 2897 fps=1931 q=-1.0 size=N/A time=00:01:20.46 bitrate=N/A speed=53.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd20.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd21.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd22.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd23.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd24.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd25.mp4' for writing
frame= 3824 fps=1912 q=-1.0 size=N/A time=00:01:58.97 bitrate=N/A speed=59.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd26.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd27.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd28.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd29.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd30.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd31.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd32.mp4' for writing
frame= 4788 fps=1915 q=-1.0 size=N/A time=00:02:39.16 bitrate=N/A speed=63.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd33.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd34.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd35.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd36.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd37.mp4' for writing
frame= 5580 fps=1860 q=-1.0 size=N/A time=00:03:12.21 bitrate=N/A speed=64.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd38.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd39.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd40.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd41.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd42.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd43.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd44.mp4' for writing
frame= 6564 fps=1875 q=-1.0 size=N/A time=00:03:53.32 bitrate=N/A speed=66.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd45.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd46.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd47.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd48.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd49.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd50.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd51.mp4' for writing
frame= 7523 fps=1880 q=-1.0 size=N/A time=00:04:33.36 bitrate=N/A speed=68.3x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd52.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd53.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd54.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd55.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd56.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd57.mp4' for writing
frame= 8452 fps=1878 q=-1.0 size=N/A time=00:05:12.28 bitrate=N/A speed=69.4x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd58.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd59.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd60.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd61.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd62.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd63.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd64.mp4' for writing
frame= 9380 fps=1876 q=-1.0 size=N/A time=00:05:50.99 bitrate=N/A speed=70.2x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd65.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd66.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd67.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd68.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd69.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd70.mp4' for writing
frame=10364 fps=1884 q=-1.0 size=N/A time=00:06:31.65 bitrate=N/A speed=71.2x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd71.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd72.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd73.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd74.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd75.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd76.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd77.mp4' for writing
frame=11306 fps=1884 q=-1.0 size=N/A time=00:07:10.95 bitrate=N/A speed=71.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd78.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd79.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd80.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd81.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd82.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd83.mp4' for writing
frame=12209 fps=1878 q=-1.0 size=N/A time=00:07:48.86 bitrate=N/A speed=72.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd84.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd85.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd86.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd87.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd88.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd89.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd90.mp4' for writing
frame=13171 fps=1881 q=-1.0 size=N/A time=00:08:28.90 bitrate=N/A speed=72.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd91.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd92.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd93.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd94.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd95.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd96.mp4' for writing
frame=14009 fps=1868 q=-1.0 size=N/A time=00:09:03.74 bitrate=N/A speed=72.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd97.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd98.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd99.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd100.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd101.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd102.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd103.mp4' for writing
frame=14983 fps=1873 q=-1.0 size=N/A time=00:09:44.53 bitrate=N/A speed=73.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd104.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd105.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd106.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd107.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd108.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd109.mp4' for writing
frame=15912 fps=1872 q=-1.0 size=N/A time=00:10:23.08 bitrate=N/A speed=73.3x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd110.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd111.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd112.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd113.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd114.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd115.mp4' for writing
frame=16831 fps=1870 q=-1.0 size=N/A time=00:11:01.58 bitrate=N/A speed=73.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd116.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd117.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd118.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd119.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd120.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd121.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd122.mp4' for writing
frame=17780 fps=1871 q=-1.0 size=N/A time=00:11:41.03 bitrate=N/A speed=73.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd123.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd124.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd125.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd126.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd127.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd128.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd129.mp4' for writing
frame=18751 fps=1875 q=-1.0 size=N/A time=00:12:21.83 bitrate=N/A speed=74.2x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd130.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd131.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd132.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd133.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd134.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd135.mp4' for writing
frame=19729 fps=1879 q=-1.0 size=N/A time=00:13:02.42 bitrate=N/A speed=74.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd136.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd137.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd138.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd139.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd140.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd141.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd142.mp4' for writing
frame=20684 fps=1880 q=-1.0 size=N/A time=00:13:42.16 bitrate=N/A speed=74.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd143.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd144.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd145.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd146.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd147.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd148.mp4' for writing
frame=21505 fps=1870 q=-1.0 size=N/A time=00:14:16.70 bitrate=N/A speed=74.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd149.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd150.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd151.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd152.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd153.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd154.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd155.mp4' for writing
frame=22536 fps=1878 q=-1.0 size=N/A time=00:14:59.45 bitrate=N/A speed=74.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd156.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd157.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd158.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd159.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd160.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd161.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd162.mp4' for writing
frame=23479 fps=1878 q=-1.0 size=N/A time=00:15:38.88 bitrate=N/A speed=75.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd163.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd164.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd165.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd166.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd167.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd168.mp4' for writing
frame=24385 fps=1875 q=-1.0 size=N/A time=00:16:16.59 bitrate=N/A speed=75.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd169.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd170.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd171.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd172.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd173.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd174.mp4' for writing
frame=25272 fps=1872 q=-1.0 size=N/A time=00:16:53.81 bitrate=N/A speed=75.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd175.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd176.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd177.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd178.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd179.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd180.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd181.mp4' for writing
frame=26234 fps=1874 q=-1.0 size=N/A time=00:17:33.63 bitrate=N/A speed=75.2x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd182.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd183.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd184.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd185.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd186.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd187.mp4' for writing
frame=27169 fps=1873 q=-1.0 size=N/A time=00:18:12.58 bitrate=N/A speed=75.3x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd188.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd189.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd190.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd191.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd192.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd193.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd194.mp4' for writing
frame=28147 fps=1876 q=-1.0 size=N/A time=00:18:53.48 bitrate=N/A speed=75.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd195.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd196.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd197.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd198.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd199.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd200.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd201.mp4' for writing
frame=29207 fps=1884 q=-1.0 size=N/A time=00:19:37.79 bitrate=N/A speed=  76x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd202.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd203.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd204.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd205.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd206.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd207.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd208.mp4' for writing
frame=30156 fps=1884 q=-1.0 size=N/A time=00:20:17.30 bitrate=N/A speed=76.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd209.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd210.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd211.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd212.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd213.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd214.mp4' for writing
frame=31098 fps=1884 q=-1.0 size=N/A time=00:20:56.53 bitrate=N/A speed=76.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd215.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd216.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd217.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd218.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd219.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd220.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd221.mp4' for writing
frame=32100 fps=1888 q=-1.0 size=N/A time=00:21:38.28 bitrate=N/A speed=76.4x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd222.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd223.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd224.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd225.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd226.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd227.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd228.mp4' for writing
frame=33117 fps=1892 q=-1.0 size=N/A time=00:22:20.71 bitrate=N/A speed=76.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd229.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd230.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd231.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd232.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd233.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd234.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd235.mp4' for writing
frame=34081 fps=1893 q=-1.0 size=N/A time=00:23:01.03 bitrate=N/A speed=76.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd236.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd237.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd238.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd239.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd240.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd241.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd242.mp4' for writing
frame=35088 fps=1896 q=-1.0 size=N/A time=00:23:42.84 bitrate=N/A speed=76.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd243.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd244.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd245.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd246.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd247.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd248.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd249.mp4' for writing
frame=36031 fps=1896 q=-1.0 size=N/A time=00:24:22.16 bitrate=N/A speed=76.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd250.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd251.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd252.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd253.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd254.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd255.mp4' for writing
frame=36991 fps=1897 q=-1.0 size=N/A time=00:25:02.22 bitrate=N/A speed=  77x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd256.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd257.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd258.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd259.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd260.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd261.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd262.mp4' for writing
frame=37946 fps=1897 q=-1.0 size=N/A time=00:25:42.16 bitrate=N/A speed=77.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd263.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd264.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd265.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd266.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd267.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd268.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd269.mp4' for writing
frame=38929 fps=1899 q=-1.0 size=N/A time=00:26:23.16 bitrate=N/A speed=77.2x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd270.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd271.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd272.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd273.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd274.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd275.mp4' for writing
frame=39889 fps=1899 q=-1.0 size=N/A time=00:27:03.23 bitrate=N/A speed=77.3x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd276.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd277.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd278.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd279.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd280.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd281.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd282.mp4' for writing
frame=40783 fps=1897 q=-1.0 size=N/A time=00:27:40.37 bitrate=N/A speed=77.2x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd283.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd284.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd285.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd286.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd287.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd288.mp4' for writing
frame=41719 fps=1896 q=-1.0 size=N/A time=00:28:19.56 bitrate=N/A speed=77.2x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd289.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd290.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd291.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd292.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd293.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd294.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd295.mp4' for writing
frame=42654 fps=1895 q=-1.0 size=N/A time=00:28:58.68 bitrate=N/A speed=77.3x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd296.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd297.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd298.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd299.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd300.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd301.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd302.mp4' for writing
frame=43651 fps=1898 q=-1.0 size=N/A time=00:29:40.05 bitrate=N/A speed=77.4x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd303.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd304.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd305.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd306.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd307.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd308.mp4' for writing
frame=44647 fps=1900 q=-1.0 size=N/A time=00:30:21.52 bitrate=N/A speed=77.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd309.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd310.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd311.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd312.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd313.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd314.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd315.mp4' for writing
frame=45606 fps=1900 q=-1.0 size=N/A time=00:31:01.58 bitrate=N/A speed=77.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd316.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd317.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd318.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd319.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd320.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd321.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd322.mp4' for writing
frame=46524 fps=1899 q=-1.0 size=N/A time=00:31:39.96 bitrate=N/A speed=77.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd323.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd324.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd325.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd326.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd327.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd328.mp4' for writing
frame=47466 fps=1898 q=-1.0 size=N/A time=00:32:19.22 bitrate=N/A speed=77.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd329.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd330.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd331.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd332.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd333.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd334.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd335.mp4' for writing
frame=48426 fps=1899 q=-1.0 size=N/A time=00:32:59.22 bitrate=N/A speed=77.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd336.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd337.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd338.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd339.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd340.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd341.mp4' for writing
frame=49303 fps=1896 q=-1.0 size=N/A time=00:33:35.74 bitrate=N/A speed=77.5x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd342.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd343.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd344.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd345.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd346.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd347.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd348.mp4' for writing
frame=50285 fps=1897 q=-1.0 size=N/A time=00:34:16.87 bitrate=N/A speed=77.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd349.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd350.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd351.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd352.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd353.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd354.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd355.mp4' for writing
frame=51229 fps=1897 q=-1.0 size=N/A time=00:34:56.25 bitrate=N/A speed=77.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd356.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd357.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd358.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd359.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd360.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd361.mp4' for writing
frame=52158 fps=1896 q=-1.0 size=N/A time=00:35:34.93 bitrate=N/A speed=77.6x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd362.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd363.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd364.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd365.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd366.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd367.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd368.mp4' for writing
frame=53125 fps=1897 q=-1.0 size=N/A time=00:36:15.18 bitrate=N/A speed=77.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd369.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd370.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd371.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd372.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd373.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd374.mp4' for writing
frame=54103 fps=1898 q=-1.0 size=N/A time=00:36:56.12 bitrate=N/A speed=77.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd375.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd376.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd377.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd378.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd379.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd380.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd381.mp4' for writing
frame=55063 fps=1898 q=-1.0 size=N/A time=00:37:36.06 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd382.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd383.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd384.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd385.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd386.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd387.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd388.mp4' for writing
frame=55997 fps=1898 q=-1.0 size=N/A time=00:38:15.06 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd389.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd390.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd391.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd392.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd393.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd394.mp4' for writing
frame=56837 fps=1894 q=-1.0 size=N/A time=00:38:50.34 bitrate=N/A speed=77.7x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd395.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd396.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd397.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd398.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd399.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd400.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd401.mp4' for writing
frame=57848 fps=1896 q=-1.0 size=N/A time=00:39:32.50 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd402.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd403.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd404.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd405.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd406.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd407.mp4' for writing
frame=58842 fps=1898 q=-1.0 size=N/A time=00:40:13.76 bitrate=N/A speed=77.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd408.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd409.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd410.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd411.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd412.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd413.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd414.mp4' for writing
frame=59771 fps=1897 q=-1.0 size=N/A time=00:40:52.56 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd415.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd416.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd417.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd418.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd419.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd420.mp4' for writing
frame=60721 fps=1897 q=-1.0 size=N/A time=00:41:32.11 bitrate=N/A speed=77.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd421.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd422.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd423.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd424.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd425.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd426.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd427.mp4' for writing
frame=61622 fps=1896 q=-1.0 size=N/A time=00:42:09.66 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd428.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd429.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd430.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd431.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd432.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd433.mp4' for writing
frame=62545 fps=1895 q=-1.0 size=N/A time=00:42:48.19 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd434.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd435.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd436.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd437.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd438.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd439.mp4' for writing
frame=63440 fps=1893 q=-1.0 size=N/A time=00:43:25.46 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd440.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd441.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd442.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd443.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd444.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd445.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd446.mp4' for writing
frame=64416 fps=1894 q=-1.0 size=N/A time=00:44:06.08 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd447.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd448.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd449.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd450.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd451.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd452.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd453.mp4' for writing
frame=65347 fps=1894 q=-1.0 size=N/A time=00:44:44.94 bitrate=N/A speed=77.8x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd454.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd455.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd456.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd457.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd458.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd459.mp4' for writing
frame=66324 fps=1895 q=-1.0 size=N/A time=00:45:25.82 bitrate=N/A speed=77.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd460.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd461.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd462.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd463.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd464.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd465.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd466.mp4' for writing
frame=67315 fps=1896 q=-1.0 size=N/A time=00:46:07.16 bitrate=N/A speed=77.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd467.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd468.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd469.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd470.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd471.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd472.mp4' for writing
frame=68185 fps=1894 q=-1.0 size=N/A time=00:46:43.43 bitrate=N/A speed=77.9x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd473.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd474.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd475.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd476.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd477.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd478.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd479.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd480.mp4' for writing
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd481.mp4' for writing
frame=69373 fps=1900 q=-1.0 size=N/A time=00:47:32.82 bitrate=N/A speed=78.1x    
[hls @ 0x5555dc19bd40] Opening '/config/cache/transcodes/b873b36da466e75900e9161dd6d6c8dd482.mp4' for writing
[out#0/hls @ 0x5555dc19f200] video:1795103KiB audio:90475KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=69411 fps=1901 q=-1.0 Lsize=N/A time=00:47:34.78 bitrate=N/A speed=78.2x    