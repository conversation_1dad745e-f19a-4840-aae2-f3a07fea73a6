{"Protocol":0,"Id":"d7635ac0d950ce6d205db435e65964d2","Path":"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E02 - Every Silver Lining WEBDL-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":2518808351,"Name":"Dexter - S08E02 - Every Silver Lining WEBDL-1080p","IsRemote":false,"ETag":"95ba0ac026e21339eeeea705802e3da2","RunTimeTicks":34437440000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":5851325,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano - Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Ingl\u00E9s - English - Dolby Digital\u002B - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Completos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Completos] - Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s [Para sordos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Ingl\u00E9s [Para sordos] - English - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":6619325,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E02 - Every Silver Lining WEBDL-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "edc89a620fe86cf0df83cd6276314da5-1.mp4" -start_number 0 -hls_segment_filename "/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5.m3u8"


ffmpeg version 7.1.2-Jellyfin Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 13 (Ubuntu 13.3.0-6ubuntu2~24.04)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, matroska,webm, from 'file:/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E02 - Every Silver Lining WEBDL-1080p.mkv':
  Metadata:
    title           : Dexter S08E02 1080p NF WEB-DL DDP2.0 x264 - TSeD @danixd
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2018-08-18T08:22:19.000000Z
  Duration: 00:57:23.74, start: 0.000000, bitrate: 5851 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 5081105
        DURATION-eng    : 00:57:23.565000000
        NUMBER_OF_FRAMES-eng: 82563
        NUMBER_OF_BYTES-eng: 2187139774
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:22:19
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        title           : Castellano
        BPS-eng         : 128000
        DURATION-eng    : 00:57:23.616000000
        NUMBER_OF_FRAMES-eng: 107613
        NUMBER_OF_BYTES-eng: 55097856
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:22:19
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(eng): Audio: eac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        title           : Inglés
        BPS-eng         : 640000
        DURATION-eng    : 00:57:23.744000000
        NUMBER_OF_FRAMES-eng: 107617
        NUMBER_OF_BYTES-eng: 275499520
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:22:19
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(spa): Subtitle: subrip (srt)
      Metadata:
        title           : Castellano [Completos]
        BPS-eng         : 64
        DURATION-eng    : 00:55:57.688000000
        NUMBER_OF_FRAMES-eng: 732
        NUMBER_OF_BYTES-eng: 27179
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:22:19
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(eng): Subtitle: subrip (srt)
      Metadata:
        title           : Inglés [Para sordos]
        BPS-eng         : 67
        DURATION-eng    : 00:56:07.197000000
        NUMBER_OF_FRAMES-eng: 1489
        NUMBER_OF_BYTES-eng: 28301
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:22:19
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5-1.mp4' for writing
[mp4 @ 0x562cc80d2ec0] track 1: codec frame size is not set
Output #0, hls, to '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5.m3u8':
  Metadata:
    encoder         : Lavf61.7.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
Press [q] to stop, [?] for help
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da50.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da51.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da52.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da53.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da54.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da55.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da56.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da57.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da58.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da59.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da510.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da511.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da512.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da513.mp4' for writing
frame= 2143 fps=0.0 q=-1.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da514.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da515.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da516.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da517.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da518.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da519.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da520.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da521.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da522.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da523.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da524.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da525.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da526.mp4' for writing
frame= 3960 fps=3959 q=-1.0 size=N/A time=00:01:15.78 bitrate=N/A speed=75.8x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da527.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da528.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da529.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da530.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da531.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da532.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da533.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da534.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da535.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da536.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da537.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da538.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da539.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da540.mp4' for writing
frame= 5989 fps=3992 q=-1.0 size=N/A time=00:02:40.41 bitrate=N/A speed= 107x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da541.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da542.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da543.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da544.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da545.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da546.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da547.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da548.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da549.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da550.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da551.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da552.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da553.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da554.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da555.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da556.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da557.mp4' for writing
frame= 8448 fps=4223 q=-1.0 size=N/A time=00:04:22.97 bitrate=N/A speed= 131x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da558.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da559.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da560.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da561.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da562.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da563.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da564.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da565.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da566.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da567.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da568.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da569.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da570.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da571.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da572.mp4' for writing
frame=10605 fps=4241 q=-1.0 size=N/A time=00:05:52.93 bitrate=N/A speed= 141x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da573.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da574.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da575.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da576.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da577.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da578.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da579.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da580.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da581.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da582.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da583.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da584.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da585.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da586.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da587.mp4' for writing
frame=12801 fps=4266 q=-1.0 size=N/A time=00:07:24.52 bitrate=N/A speed= 148x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da588.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da589.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da590.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da591.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da592.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da593.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da594.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da595.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da596.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da597.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da598.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da599.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5100.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5101.mp4' for writing
frame=14772 fps=4220 q=-1.0 size=N/A time=00:08:46.81 bitrate=N/A speed= 150x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5102.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5103.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5104.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5105.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5106.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5107.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5108.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5109.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5110.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5111.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5112.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5113.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5114.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5115.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5116.mp4' for writing
frame=16972 fps=4242 q=-1.0 size=N/A time=00:10:18.49 bitrate=N/A speed= 155x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5117.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5118.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5119.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5120.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5121.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5122.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5123.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5124.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5125.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5126.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5127.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5128.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5129.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5130.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5131.mp4' for writing
frame=19031 fps=4228 q=-1.0 size=N/A time=00:11:44.41 bitrate=N/A speed= 157x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5132.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5133.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5134.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5135.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5136.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5137.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5138.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5139.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5140.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5141.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5142.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5143.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5144.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5145.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5146.mp4' for writing
frame=21264 fps=4252 q=-1.0 size=N/A time=00:13:17.50 bitrate=N/A speed= 159x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5147.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5148.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5149.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5150.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5151.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5152.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5153.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5154.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5155.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5156.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5157.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5158.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5159.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5160.mp4' for writing
frame=23185 fps=4215 q=-1.0 size=N/A time=00:14:37.62 bitrate=N/A speed= 160x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5161.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5162.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5163.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5164.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5165.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5166.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5167.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5168.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5169.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5170.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5171.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5172.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5173.mp4' for writing
frame=25201 fps=4200 q=-1.0 size=N/A time=00:16:01.71 bitrate=N/A speed= 160x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5174.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5175.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5176.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5177.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5178.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5179.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5180.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5181.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5182.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5183.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5184.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5185.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5186.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5187.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5188.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5189.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5190.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5191.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5192.mp4' for writing
frame=27867 fps=4287 q=-1.0 size=N/A time=00:17:52.90 bitrate=N/A speed= 165x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5193.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5194.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5195.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5196.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5197.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5198.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5199.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5200.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5201.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5202.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5203.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5204.mp4' for writing
frame=29612 fps=4230 q=-1.0 size=N/A time=00:19:05.68 bitrate=N/A speed= 164x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5205.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5206.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5207.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5208.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5209.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5210.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5211.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5212.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5213.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5214.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5215.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5216.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5217.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5218.mp4' for writing
frame=31655 fps=4220 q=-1.0 size=N/A time=00:20:30.89 bitrate=N/A speed= 164x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5219.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5220.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5221.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5222.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5223.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5224.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5225.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5226.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5227.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5228.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5229.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5230.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5231.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5232.mp4' for writing
frame=33637 fps=4204 q=-1.0 size=N/A time=00:21:53.56 bitrate=N/A speed= 164x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5233.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5234.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5235.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5236.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5237.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5238.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5239.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5240.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5241.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5242.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5243.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5244.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5245.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5246.mp4' for writing
frame=35697 fps=4199 q=-1.0 size=N/A time=00:23:19.48 bitrate=N/A speed= 165x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5247.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5248.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5249.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5250.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5251.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5252.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5253.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5254.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5255.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5256.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5257.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5258.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5259.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5260.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5261.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5262.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5263.mp4' for writing
frame=38120 fps=4235 q=-1.0 size=N/A time=00:25:00.54 bitrate=N/A speed= 167x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5264.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5265.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5266.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5267.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5268.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5269.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5270.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5271.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5272.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5273.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5274.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5275.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5276.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5277.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5278.mp4' for writing
frame=40320 fps=4244 q=-1.0 size=N/A time=00:26:32.29 bitrate=N/A speed= 168x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5279.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5280.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5281.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5282.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5283.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5284.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5285.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5286.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5287.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5288.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5289.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5290.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5291.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5292.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5293.mp4' for writing
frame=42466 fps=4246 q=-1.0 size=N/A time=00:28:01.80 bitrate=N/A speed= 168x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5294.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5295.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5296.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5297.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5298.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5299.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5300.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5301.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5302.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5303.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5304.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5305.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5306.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5307.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5308.mp4' for writing
frame=44552 fps=4242 q=-1.0 size=N/A time=00:29:28.80 bitrate=N/A speed= 168x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5309.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5310.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5311.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5312.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5313.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5314.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5315.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5316.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5317.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5318.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5319.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5320.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5321.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5322.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5323.mp4' for writing
frame=46657 fps=4241 q=-1.0 size=N/A time=00:30:56.60 bitrate=N/A speed= 169x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5324.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5325.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5326.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5327.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5328.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5329.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5330.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5331.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5332.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5333.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5334.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5335.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5336.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5337.mp4' for writing
frame=48732 fps=4237 q=-1.0 size=N/A time=00:32:23.19 bitrate=N/A speed= 169x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5338.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5339.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5340.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5341.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5342.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5343.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5344.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5345.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5346.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5347.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5348.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5349.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5350.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5351.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5352.mp4' for writing
frame=50928 fps=4243 q=-1.0 size=N/A time=00:33:54.74 bitrate=N/A speed= 170x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5353.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5354.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5355.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5356.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5357.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5358.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5359.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5360.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5361.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5362.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5363.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5364.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5365.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5366.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5367.mp4' for writing
frame=52980 fps=4238 q=-1.0 size=N/A time=00:35:20.32 bitrate=N/A speed= 170x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5368.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5369.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5370.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5371.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5372.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5373.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5374.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5375.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5376.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5377.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5378.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5379.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5380.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5381.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5382.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5383.mp4' for writing
frame=55303 fps=4253 q=-1.0 size=N/A time=00:36:57.21 bitrate=N/A speed= 171x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5384.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5385.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5386.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5387.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5388.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5389.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5390.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5391.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5392.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5393.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5394.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5395.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5396.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5397.mp4' for writing
frame=57319 fps=4245 q=-1.0 size=N/A time=00:38:21.29 bitrate=N/A speed= 170x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5398.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5399.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5400.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5401.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5402.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5403.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5404.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5405.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5406.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5407.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5408.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5409.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5410.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5411.mp4' for writing
frame=59328 fps=4237 q=-1.0 size=N/A time=00:39:45.09 bitrate=N/A speed= 170x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5412.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5413.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5414.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5415.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5416.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5417.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5418.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5419.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5420.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5421.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5422.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5423.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5424.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5425.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5426.mp4' for writing
frame=61558 fps=4245 q=-1.0 size=N/A time=00:41:18.10 bitrate=N/A speed= 171x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5427.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5428.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5429.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5430.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5431.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5432.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5433.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5434.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5435.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5436.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5437.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5438.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5439.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5440.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5441.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5442.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5443.mp4' for writing
frame=64025 fps=4268 q=-1.0 size=N/A time=00:43:00.99 bitrate=N/A speed= 172x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5444.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5445.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5446.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5447.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5448.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5449.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5450.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5451.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5452.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5453.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5454.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5455.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5456.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5457.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5458.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5459.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5460.mp4' for writing
frame=66337 fps=4279 q=-1.0 size=N/A time=00:44:37.42 bitrate=N/A speed= 173x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5461.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5462.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5463.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5464.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5465.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5466.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5467.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5468.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5469.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5470.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5471.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5472.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5473.mp4' for writing
frame=68346 fps=4271 q=-1.0 size=N/A time=00:46:01.21 bitrate=N/A speed= 173x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5474.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5475.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5476.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5477.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5478.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5479.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5480.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5481.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5482.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5483.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5484.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5485.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5486.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5487.mp4' for writing
frame=70369 fps=4264 q=-1.0 size=N/A time=00:47:25.59 bitrate=N/A speed= 172x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5488.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5489.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5490.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5491.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5492.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5493.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5494.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5495.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5496.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5497.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5498.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5499.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5500.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5501.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5502.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5503.mp4' for writing
frame=72651 fps=4273 q=-1.0 size=N/A time=00:49:00.85 bitrate=N/A speed= 173x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5504.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5505.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5506.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5507.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5508.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5509.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5510.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5511.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5512.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5513.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5514.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5515.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5516.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5517.mp4' for writing
frame=74592 fps=4262 q=-1.0 size=N/A time=00:50:21.76 bitrate=N/A speed= 173x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5518.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5519.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5520.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5521.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5522.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5523.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5524.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5525.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5526.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5527.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5528.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5529.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5530.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5531.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5532.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5533.mp4' for writing
frame=76849 fps=4269 q=-1.0 size=N/A time=00:51:55.86 bitrate=N/A speed= 173x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5534.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5535.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5536.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5537.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5538.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5539.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5540.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5541.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5542.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5543.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5544.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5545.mp4' for writing
frame=78685 fps=4253 q=-1.0 size=N/A time=00:53:12.43 bitrate=N/A speed= 173x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5546.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5547.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5548.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5549.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5550.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5551.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5552.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5553.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5554.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5555.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5556.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5557.mp4' for writing
frame=80352 fps=4228 q=-1.0 size=N/A time=00:54:21.96 bitrate=N/A speed= 172x    
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5558.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5559.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5560.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5561.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5562.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5563.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5564.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5565.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5566.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5567.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5568.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5569.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5570.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5571.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5572.mp4' for writing
[hls @ 0x562cc80d31c0] Opening '/config/cache/transcodes/edc89a620fe86cf0df83cd6276314da5573.mp4' for writing
[out#0/hls @ 0x562cc8123800] video:2135877KiB audio:53806KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=82563 fps=4248 q=-1.0 Lsize=N/A time=00:55:54.18 bitrate=N/A speed= 173x    