{"Protocol":0,"Id":"ca5ebcf281bc46f8eff8eb1de4ad2886","Path":"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E01 - A Beautiful Day WEBDL-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":2599540840,"Name":"Dexter - S08E01 - A Beautiful Day WEBDL-1080p","IsRemote":false,"ETag":"dad83b22ac6eba5ea408c519e51c4eac","RunTimeTicks":31876160000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":6524100,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano - Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Ingl\u00E9s - English - Dolby Digital\u002B - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Forzados]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Forzados] - Spanish - Predeterminado - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Completos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Completos] - Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s [Para sordos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Ingl\u00E9s [Para sordos] - English - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":7292100,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:21:13.772 -noaccurate_seek -fflags +genpts  -i file:"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E01 - A Beautiful Day WEBDL-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "3f67245c2c83e0b6b5b28f48d6c5c12f-1.mp4" -start_number 212 -hls_segment_filename "/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f.m3u8"


ffmpeg version 7.1.2-Jellyfin Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 13 (Ubuntu 13.3.0-6ubuntu2~24.04)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, matroska,webm, from 'file:/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E01 - A Beautiful Day WEBDL-1080p.mkv':
  Metadata:
    title           : Dexter S08E01 1080p NF WEB-DL DDP2.0 x264 - TSeD @danixd
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2018-08-18T08:13:53.000000Z
  Duration: 00:53:07.62, start: 0.000000, bitrate: 6524 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 5754092
        DURATION-eng    : 00:53:07.309000000
        NUMBER_OF_FRAMES-eng: 76419
        NUMBER_OF_BYTES-eng: 2292508903
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        title           : Castellano
        BPS-eng         : 128000
        DURATION-eng    : 00:53:07.360000000
        NUMBER_OF_FRAMES-eng: 99605
        NUMBER_OF_BYTES-eng: 50997760
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(eng): Audio: eac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        title           : Inglés
        BPS-eng         : 640000
        DURATION-eng    : 00:53:07.616000000
        NUMBER_OF_FRAMES-eng: 99613
        NUMBER_OF_BYTES-eng: 255009280
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(spa): Subtitle: subrip (srt) (default) (forced)
      Metadata:
        title           : Castellano [Forzados]
        BPS-eng         : 321
        DURATION-eng    : 00:00:01.793000000
        NUMBER_OF_FRAMES-eng: 1
        NUMBER_OF_BYTES-eng: 72
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(spa): Subtitle: subrip (srt)
      Metadata:
        title           : Castellano [Completos]
        BPS-eng         : 70
        DURATION-eng    : 00:51:42.434000000
        NUMBER_OF_FRAMES-eng: 858
        NUMBER_OF_BYTES-eng: 27455
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:5(eng): Subtitle: subrip (srt)
      Metadata:
        title           : Inglés [Para sordos]
        BPS-eng         : 76
        DURATION-eng    : 00:51:43.393000000
        NUMBER_OF_FRAMES-eng: 1642
        NUMBER_OF_BYTES-eng: 29526
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f-1.mp4' for writing
[mp4 @ 0x562e8e3b4a40] track 1: codec frame size is not set
Output #0, hls, to '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f.m3u8':
  Metadata:
    encoder         : Lavf61.7.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
Press [q] to stop, [?] for help
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f212.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f213.mp4' for writing
frame=  390 fps=0.0 q=-1.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f214.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f215.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f216.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f217.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f218.mp4' for writing
frame= 1087 fps=1087 q=-1.0 size=N/A time=00:00:29.07 bitrate=N/A speed=29.1x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f219.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f220.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f221.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f222.mp4' for writing
frame= 1648 fps=1099 q=-1.0 size=N/A time=00:00:52.46 bitrate=N/A speed=  35x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f223.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f224.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f225.mp4' for writing
frame= 2119 fps=1059 q=-1.0 size=N/A time=00:01:12.11 bitrate=N/A speed=36.1x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f226.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f227.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f228.mp4' for writing
frame= 2564 fps=1025 q=-1.0 size=N/A time=00:01:30.67 bitrate=N/A speed=36.3x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f229.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f230.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f231.mp4' for writing
frame= 2951 fps=984 q=-1.0 size=N/A time=00:01:46.81 bitrate=N/A speed=35.6x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f232.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f233.mp4' for writing
frame= 3198 fps=914 q=-1.0 size=N/A time=00:01:57.11 bitrate=N/A speed=33.5x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f234.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f235.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f236.mp4' for writing
frame= 3635 fps=909 q=-1.0 size=N/A time=00:02:15.34 bitrate=N/A speed=33.8x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f237.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f238.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f239.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f240.mp4' for writing
frame= 4202 fps=934 q=-1.0 size=N/A time=00:02:38.99 bitrate=N/A speed=35.3x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f241.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f242.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f243.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f244.mp4' for writing
frame= 4760 fps=952 q=-1.0 size=N/A time=00:03:02.26 bitrate=N/A speed=36.4x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f245.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f246.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f247.mp4' for writing
frame= 5269 fps=958 q=-1.0 size=N/A time=00:03:23.49 bitrate=N/A speed=  37x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f248.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f249.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f250.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f251.mp4' for writing
frame= 5790 fps=965 q=-1.0 size=N/A time=00:03:45.22 bitrate=N/A speed=37.5x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f252.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f253.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f254.mp4' for writing
frame= 6225 fps=958 q=-1.0 size=N/A time=00:04:03.36 bitrate=N/A speed=37.4x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f255.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f256.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f257.mp4' for writing
frame= 6642 fps=949 q=-1.0 size=N/A time=00:04:20.76 bitrate=N/A speed=37.2x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f258.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f259.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f260.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f261.mp4' for writing
frame= 7226 fps=963 q=-1.0 size=N/A time=00:04:45.11 bitrate=N/A speed=  38x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f262.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f263.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f264.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f265.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f266.mp4' for writing
frame= 8010 fps=1001 q=-1.0 size=N/A time=00:05:17.81 bitrate=N/A speed=39.7x    
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f267.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f268.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f269.mp4' for writing
[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f270.mp4' for writing


[q] command received. Exiting.

[hls @ 0x562e8e3ad500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f271.mp4' for writing
[out#0/hls @ 0x562e8e3e3dc0] video:251637KiB audio:5588KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame= 8572 fps=1006 q=-1.0 Lsize=N/A time=00:05:41.25 bitrate=N/A speed=40.1x    