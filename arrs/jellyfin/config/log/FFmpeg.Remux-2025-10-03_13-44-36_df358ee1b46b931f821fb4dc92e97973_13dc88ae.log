{"Protocol":0,"Id":"df358ee1b46b931f821fb4dc92e97973","Path":"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":2607651779,"Name":"Dexter - S08E04 - Scar Tissue WEBDL-1080p","IsRemote":false,"ETag":"4d1f702cbe558ddef5cc04839d582871","RunTimeTicks":30152000000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":6918683,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano - Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Ingl\u00E9s - English - Dolby Digital\u002B - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Completos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Completos] - Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s [Para sordos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Ingl\u00E9s [Para sordos] - English - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":7686683,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "3f95bf04e2e9d8219cf0d18eedbc0edd-1.mp4" -start_number 0 -hls_segment_filename "/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd.m3u8"


ffmpeg version 7.1.2-Jellyfin Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 13 (Ubuntu 13.3.0-6ubuntu2~24.04)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, matroska,webm, from 'file:/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv':
  Metadata:
    title           : Dexter S08E04 1080p NF WEB-DL DDP2.0 x264 - TSeD @danixd
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2018-08-18T08:37:21.000000Z
  Duration: 00:50:15.20, start: 0.000000, bitrate: 6918 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 6148245
        DURATION-eng    : 00:50:15.137000000
        NUMBER_OF_FRAMES-eng: 72291
        NUMBER_OF_BYTES-eng: 2317225192
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        title           : Castellano
        BPS-eng         : 128000
        DURATION-eng    : 00:50:15.200000000
        NUMBER_OF_FRAMES-eng: 94225
        NUMBER_OF_BYTES-eng: 48243200
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(eng): Audio: eac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        title           : Inglés
        BPS-eng         : 640000
        DURATION-eng    : 00:50:15.200000000
        NUMBER_OF_FRAMES-eng: 94225
        NUMBER_OF_BYTES-eng: 241216000
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(spa): Subtitle: subrip (srt)
      Metadata:
        title           : Castellano [Completos]
        BPS-eng         : 70
        DURATION-eng    : 00:47:24.050000000
        NUMBER_OF_FRAMES-eng: 755
        NUMBER_OF_BYTES-eng: 25189
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(eng): Subtitle: subrip (srt)
      Metadata:
        title           : Inglés [Para sordos]
        BPS-eng         : 72
        DURATION-eng    : 00:48:51.720000000
        NUMBER_OF_FRAMES-eng: 1425
        NUMBER_OF_BYTES-eng: 26446
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd-1.mp4' for writing
[mp4 @ 0x5588759b4440] track 1: codec frame size is not set
Output #0, hls, to '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd.m3u8':
  Metadata:
    encoder         : Lavf61.7.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
Press [q] to stop, [?] for help
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd0.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd1.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd2.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd3.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd4.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd5.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd6.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd7.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd8.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd9.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd10.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd11.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd12.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd13.mp4' for writing
frame= 2061 fps=0.0 q=-1.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd14.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd15.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd16.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd17.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd18.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd19.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd20.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd21.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd22.mp4' for writing
frame= 3417 fps=3417 q=-1.0 size=N/A time=00:00:56.55 bitrate=N/A speed=56.5x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd23.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd24.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd25.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd26.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd27.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd28.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd29.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd30.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd31.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd32.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd33.mp4' for writing
frame= 4913 fps=3275 q=-1.0 size=N/A time=00:01:58.95 bitrate=N/A speed=79.3x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd34.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd35.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd36.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd37.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd38.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd39.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd40.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd41.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd42.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd43.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd44.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd45.mp4' for writing
frame= 6650 fps=3324 q=-1.0 size=N/A time=00:03:11.52 bitrate=N/A speed=95.7x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd46.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd47.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd48.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd49.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd50.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd51.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd52.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd53.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd54.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd55.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd56.mp4' for writing
frame= 8209 fps=3283 q=-1.0 size=N/A time=00:04:16.42 bitrate=N/A speed= 103x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd57.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd58.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd59.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd60.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd61.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd62.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd63.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd64.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd65.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd66.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd67.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd68.mp4' for writing
frame=10000 fps=3333 q=-1.0 size=N/A time=00:05:31.12 bitrate=N/A speed= 110x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd69.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd70.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd71.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd72.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd73.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd74.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd75.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd76.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd77.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd78.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd79.mp4' for writing
frame=11616 fps=3318 q=-1.0 size=N/A time=00:06:38.52 bitrate=N/A speed= 114x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd80.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd81.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd82.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd83.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd84.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd85.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd86.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd87.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd88.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd89.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd90.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd91.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd92.mp4' for writing
frame=13522 fps=3380 q=-1.0 size=N/A time=00:07:58.14 bitrate=N/A speed= 120x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd93.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd94.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd95.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd96.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd97.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd98.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd99.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd100.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd101.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd102.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd103.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd104.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd105.mp4' for writing
frame=15408 fps=3423 q=-1.0 size=N/A time=00:09:16.68 bitrate=N/A speed= 124x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd106.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd107.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd108.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd109.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd110.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd111.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd112.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd113.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd114.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd115.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd116.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd117.mp4' for writing
frame=17050 fps=3409 q=-1.0 size=N/A time=00:10:25.16 bitrate=N/A speed= 125x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd118.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd119.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd120.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd121.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd122.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd123.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd124.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd125.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd126.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd127.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd128.mp4' for writing
frame=18585 fps=3379 q=-1.0 size=N/A time=00:11:29.18 bitrate=N/A speed= 125x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd129.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd130.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd131.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd132.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd133.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd134.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd135.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd136.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd137.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd138.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd139.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd140.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd141.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd142.mp4' for writing
frame=20682 fps=3446 q=-1.0 size=N/A time=00:12:56.65 bitrate=N/A speed= 129x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd143.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd144.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd145.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd146.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd147.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd148.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd149.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd150.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd151.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd152.mp4' for writing
frame=22093 fps=3398 q=-1.0 size=N/A time=00:13:55.50 bitrate=N/A speed= 129x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd153.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd154.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd155.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd156.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd157.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd158.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd159.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd160.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd161.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd162.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd163.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd164.mp4' for writing
frame=23849 fps=3406 q=-1.0 size=N/A time=00:15:08.74 bitrate=N/A speed= 130x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd165.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd166.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd167.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd168.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd169.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd170.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd171.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd172.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd173.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd174.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd175.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd176.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd177.mp4' for writing
frame=25744 fps=3432 q=-1.0 size=N/A time=00:16:27.77 bitrate=N/A speed= 132x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd178.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd179.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd180.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd181.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd182.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd183.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd184.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd185.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd186.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd187.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd188.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd189.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd190.mp4' for writing
frame=27541 fps=3442 q=-1.0 size=N/A time=00:17:42.72 bitrate=N/A speed= 133x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd191.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd192.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd193.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd194.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd195.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd196.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd197.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd198.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd199.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd200.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd201.mp4' for writing
frame=29223 fps=3437 q=-1.0 size=N/A time=00:18:52.88 bitrate=N/A speed= 133x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd202.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd203.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd204.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd205.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd206.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd207.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd208.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd209.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd210.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd211.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd212.mp4' for writing
frame=30793 fps=3421 q=-1.0 size=N/A time=00:19:58.36 bitrate=N/A speed= 133x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd213.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd214.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd215.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd216.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd217.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd218.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd219.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd220.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd221.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd222.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd223.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd224.mp4' for writing
frame=32501 fps=3421 q=-1.0 size=N/A time=00:21:09.68 bitrate=N/A speed= 134x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd225.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd226.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd227.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd228.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd229.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd230.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd231.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd232.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd233.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd234.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd235.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd236.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd237.mp4' for writing
frame=34375 fps=3437 q=-1.0 size=N/A time=00:22:27.76 bitrate=N/A speed= 135x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd238.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd239.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd240.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd241.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd242.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd243.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd244.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd245.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd246.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd247.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd248.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd249.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd250.mp4' for writing
frame=36224 fps=3449 q=-1.0 size=N/A time=00:23:44.88 bitrate=N/A speed= 136x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd251.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd252.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd253.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd254.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd255.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd256.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd257.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd258.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd259.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd260.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd261.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd262.mp4' for writing
frame=37941 fps=3449 q=-1.0 size=N/A time=00:24:56.49 bitrate=N/A speed= 136x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd263.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd264.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd265.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd266.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd267.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd268.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd269.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd270.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd271.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd272.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd273.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd274.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd275.mp4' for writing
frame=39853 fps=3465 q=-1.0 size=N/A time=00:26:16.24 bitrate=N/A speed= 137x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd276.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd277.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd278.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd279.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd280.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd281.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd282.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd283.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd284.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd285.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd286.mp4' for writing
frame=41472 fps=3455 q=-1.0 size=N/A time=00:27:23.76 bitrate=N/A speed= 137x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd287.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd288.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd289.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd290.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd291.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd292.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd293.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd294.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd295.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd296.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd297.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd298.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd299.mp4' for writing
frame=43299 fps=3463 q=-1.0 size=N/A time=00:28:39.96 bitrate=N/A speed= 138x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd300.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd301.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd302.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd303.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd304.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd305.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd306.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd307.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd308.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd309.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd310.mp4' for writing
frame=44889 fps=3452 q=-1.0 size=N/A time=00:29:46.28 bitrate=N/A speed= 137x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd311.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd312.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd313.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd314.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd315.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd316.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd317.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd318.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd319.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd320.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd321.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd322.mp4' for writing
frame=46591 fps=3451 q=-1.0 size=N/A time=00:30:57.27 bitrate=N/A speed= 138x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd323.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd324.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd325.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd326.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd327.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd328.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd329.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd330.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd331.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd332.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd333.mp4' for writing
frame=48097 fps=3435 q=-1.0 size=N/A time=00:32:00.08 bitrate=N/A speed= 137x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd334.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd335.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd336.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd337.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd338.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd339.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd340.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd341.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd342.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd343.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd344.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd345.mp4' for writing
frame=49804 fps=3434 q=-1.0 size=N/A time=00:33:11.28 bitrate=N/A speed= 137x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd346.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd347.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd348.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd349.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd350.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd351.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd352.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd353.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd354.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd355.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd356.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd357.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd358.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd359.mp4' for writing
frame=51842 fps=3456 q=-1.0 size=N/A time=00:34:36.28 bitrate=N/A speed= 138x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd360.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd361.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd362.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd363.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd364.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd365.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd366.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd367.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd368.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd369.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd370.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd371.mp4' for writing
frame=53622 fps=3459 q=-1.0 size=N/A time=00:35:50.52 bitrate=N/A speed= 139x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd372.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd373.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd374.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd375.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd376.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd377.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd378.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd379.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd380.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd381.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd382.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd383.mp4' for writing
frame=55296 fps=3455 q=-1.0 size=N/A time=00:37:00.34 bitrate=N/A speed= 139x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd384.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd385.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd386.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd387.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd388.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd389.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd390.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd391.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd392.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd393.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd394.mp4' for writing
frame=56862 fps=3446 q=-1.0 size=N/A time=00:38:05.74 bitrate=N/A speed= 139x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd395.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd396.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd397.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd398.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd399.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd400.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd401.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd402.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd403.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd404.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd405.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd406.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd407.mp4' for writing
frame=58825 fps=3460 q=-1.0 size=N/A time=00:39:27.53 bitrate=N/A speed= 139x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd408.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd409.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd410.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd411.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd412.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd413.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd414.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd415.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd416.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd417.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd418.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd419.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd420.mp4' for writing
frame=60577 fps=3461 q=-1.0 size=N/A time=00:40:40.60 bitrate=N/A speed= 139x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd421.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd422.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd423.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd424.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd425.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd426.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd427.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd428.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd429.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd430.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd431.mp4' for writing
frame=62253 fps=3458 q=-1.0 size=N/A time=00:41:50.50 bitrate=N/A speed= 139x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd432.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd433.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd434.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd435.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd436.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd437.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd438.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd439.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd440.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd441.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd442.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd443.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd444.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd445.mp4' for writing
frame=64224 fps=3471 q=-1.0 size=N/A time=00:43:12.75 bitrate=N/A speed= 140x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd446.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd447.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd448.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd449.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd450.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd451.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd452.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd453.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd454.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd455.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd456.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd457.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd458.mp4' for writing
frame=66150 fps=3481 q=-1.0 size=N/A time=00:44:33.04 bitrate=N/A speed= 141x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd459.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd460.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd461.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd462.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd463.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd464.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd465.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd466.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd467.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd468.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd469.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd470.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd471.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd472.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd473.mp4' for writing
frame=68265 fps=3500 q=-1.0 size=N/A time=00:46:01.25 bitrate=N/A speed= 142x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd474.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd475.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd476.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd477.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd478.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd479.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd480.mp4' for writing
frame=69361 fps=3468 q=-1.0 size=N/A time=00:46:46.97 bitrate=N/A speed= 140x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd481.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd482.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd483.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd484.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd485.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd486.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd487.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd488.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd489.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd490.mp4' for writing
frame=70801 fps=3453 q=-1.0 size=N/A time=00:47:47.03 bitrate=N/A speed= 140x    
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd491.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd492.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd493.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd494.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd495.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd496.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd497.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd498.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd499.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd500.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd501.mp4' for writing
[hls @ 0x5588759b3b80] Opening '/config/cache/transcodes/3f95bf04e2e9d8219cf0d18eedbc0edd502.mp4' for writing
[out#0/hls @ 0x558875a02700] video:2262914KiB audio:47112KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=72291 fps=3489 q=-1.0 Lsize=N/A time=00:48:49.17 bitrate=N/A speed= 141x    