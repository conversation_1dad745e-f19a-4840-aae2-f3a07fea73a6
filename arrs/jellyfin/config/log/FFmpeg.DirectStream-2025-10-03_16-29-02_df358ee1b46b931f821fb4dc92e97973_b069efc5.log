{"Protocol":0,"Id":"df358ee1b46b931f821fb4dc92e97973","Path":"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":2607651779,"Name":"Dexter - S08E04 - Scar Tissue WEBDL-1080p","IsRemote":false,"ETag":"4d1f702cbe558ddef5cc04839d582871","RunTimeTicks":30152000000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":6918683,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano - Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Ingl\u00E9s - English - Dolby Digital\u002B - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Completos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Completos] - Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s [Para sordos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Ingl\u00E9s [Para sordos] - English - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":7686683,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:26:50.108 -noaccurate_seek -fflags +genpts  -i file:"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 libfdk_aac -ac 2 -ab 256000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "7a066d43e13586abed81d48b714e4055-1.mp4" -start_number 268 -hls_segment_filename "/config/cache/transcodes/7a066d43e13586abed81d48b714e4055%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/config/cache/transcodes/7a066d43e13586abed81d48b714e4055.m3u8"


ffmpeg version 7.1.2-Jellyfin Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 13 (Ubuntu 13.3.0-6ubuntu2~24.04)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, matroska,webm, from 'file:/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv':
  Metadata:
    title           : Dexter S08E04 1080p NF WEB-DL DDP2.0 x264 - TSeD @danixd
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2018-08-18T08:37:21.000000Z
  Duration: 00:50:15.20, start: 0.000000, bitrate: 6918 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 6148245
        DURATION-eng    : 00:50:15.137000000
        NUMBER_OF_FRAMES-eng: 72291
        NUMBER_OF_BYTES-eng: 2317225192
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        title           : Castellano
        BPS-eng         : 128000
        DURATION-eng    : 00:50:15.200000000
        NUMBER_OF_FRAMES-eng: 94225
        NUMBER_OF_BYTES-eng: 48243200
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(eng): Audio: eac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        title           : Inglés
        BPS-eng         : 640000
        DURATION-eng    : 00:50:15.200000000
        NUMBER_OF_FRAMES-eng: 94225
        NUMBER_OF_BYTES-eng: 241216000
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(spa): Subtitle: subrip (srt)
      Metadata:
        title           : Castellano [Completos]
        BPS-eng         : 70
        DURATION-eng    : 00:47:24.050000000
        NUMBER_OF_FRAMES-eng: 755
        NUMBER_OF_BYTES-eng: 25189
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(eng): Subtitle: subrip (srt)
      Metadata:
        title           : Inglés [Para sordos]
        BPS-eng         : 72
        DURATION-eng    : 00:48:51.720000000
        NUMBER_OF_FRAMES-eng: 1425
        NUMBER_OF_BYTES-eng: 26446
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (eac3 (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055-1.mp4' for writing
Output #0, hls, to '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055.m3u8':
  Metadata:
    encoder         : Lavf61.7.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: aac, 48000 Hz, stereo, s16, 256 kb/s (default)
      Metadata:
        encoder         : Lavc61.19.101 libfdk_aac
      Side data:
        cpb: bitrate max/min/avg: 256000/256000/256000 buffer size: 0 vbv_delay: N/A
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055268.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055269.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055270.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055271.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055272.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055273.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055274.mp4' for writing
frame= 1054 fps=0.0 q=-1.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055275.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055276.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055277.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055278.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055279.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055280.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055281.mp4' for writing
[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055282.mp4' for writing


[q] command received. Exiting.

[hls @ 0x562d70c37f80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055283.mp4' for writing
[out#0/hls @ 0x562d70c26bc0] video:66660KiB audio:2833KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame= 2171 fps=2154 q=-1.0 Lsize=N/A time=00:00:46.91 bitrate=N/A speed=46.6x    