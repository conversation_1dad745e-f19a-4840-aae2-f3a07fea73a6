[2025-10-02 23:25:37.014 +02:00] [INF] [6] Main: Jellyfin version: "10.10.7"
[2025-10-02 23:25:37.035 +02:00] [INF] [6] Main: Environment Variables: ["[JELLYFIN_CACHE_DIR, /config/cache]", "[<PERSON><PERSON><PERSON><PERSON>FI<PERSON>_WEB_DIR, /usr/share/jellyfin/web]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CONFIG_DIR, /config]", "[JELLYFIN_DATA_DIR, /config/data]"]
[2025-10-02 23:25:37.037 +02:00] [INF] [6] Main: Arguments: ["/usr/lib/jellyfin/bin/jellyfin.dll", "--ffmpeg=/usr/lib/jellyfin-ffmpeg/ffmpeg"]
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: Operating system: "Ubuntu 24.04.3 LTS"
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: Architecture: X64
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: 64-Bit Process: True
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: User Interactive: True
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: Processor count: 12
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: Program data path: "/config/data"
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: Log directory path: "/config/log"
[2025-10-02 23:25:37.319 +02:00] [INF] [6] Main: Config directory path: "/config"
[2025-10-02 23:25:37.320 +02:00] [INF] [6] Main: Cache path: "/config/cache"
[2025-10-02 23:25:37.320 +02:00] [INF] [6] Main: Temp directory path: "/tmp/jellyfin"
[2025-10-02 23:25:37.320 +02:00] [INF] [6] Main: Web resources path: "/usr/share/jellyfin/web"
[2025-10-02 23:25:37.320 +02:00] [INF] [6] Main: Application directory: "/usr/lib/jellyfin/bin/"
[2025-10-02 23:25:37.332 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Marking following migrations as applied because this is a fresh install: ["CreateNetworkConfiguration", "MigrateMusicBrainzTimeout", "MigrateNetworkConfiguration", "MigrateEncodingOptions"]
[2025-10-02 23:25:37.384 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:25:37.408 +02:00] [INF] [6] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-10-02 23:25:37.455 +02:00] [INF] [6] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-10-02 23:25:37.455 +02:00] [INF] [6] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-10-02 23:25:37.455 +02:00] [INF] [6] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-10-02 23:25:37.456 +02:00] [INF] [6] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "***********"]
[2025-10-02 23:25:37.456 +02:00] [INF] [6] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-10-02 23:25:37.457 +02:00] [INF] [6] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-10-02 23:25:37.457 +02:00] [INF] [6] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-10-02 23:25:38.139 +02:00] [INF] [6] Emby.Server.Implementations.ApplicationHost: There are pending EFCore migrations in the database. Applying... (This may take a while, do not stop Jellyfin)
[2025-10-02 23:25:38.381 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"ImageInfos"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.381 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"ImageInfos"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.382 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"Permissions"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.382 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"Permissions"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.383 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"Preferences"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.383 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"Preferences"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.383 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"Users"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.383 +02:00] [WRN] [6] Microsoft.EntityFrameworkCore.Migrations: An operation of type '"SqlOperation"' will be attempted while a rebuild of table '"Users"' is pending. The database may not be in an expected state. Review the SQL generated by this migration to help diagnose any failures. Consider moving these operations to a subsequent migration.
[2025-10-02 23:25:38.522 +02:00] [INF] [6] Emby.Server.Implementations.ApplicationHost: EFCore migrations applied successfully
[2025-10-02 23:25:38.873 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:25:38.873 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:25:38.876 +02:00] [INF] [6] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-10-02 23:25:38.877 +02:00] [INF] [6] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-10-02 23:25:38.877 +02:00] [INF] [6] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-10-02 23:25:38.886 +02:00] [INF] [6] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-10-02 23:25:38.886 +02:00] [INF] [6] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-10-02 23:25:38.968 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Marking following migrations as applied because this is a fresh install: ["DisableTranscodingThrottling", "CreateLoggingConfigHeirarchy", "MigrateActivityLogDatabase", "RemoveDuplicateExtras", "MigrateUserDatabase", "MigrateDisplayPreferencesDatabase", "RemoveDownloadImagesInAdvance", "MigrateAuthenticationDatabase", "FixPlaylistOwner", "MigrateRatingLevels", "FixAudioData", "RemoveDuplicatePlaylistChildren"]
[2025-10-02 23:25:38.970 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Applying migration '"AddDefaultPluginRepository"'
[2025-10-02 23:25:38.970 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:25:38.971 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:25:38.971 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Migration '"AddDefaultPluginRepository"' applied successfully
[2025-10-02 23:25:38.971 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Applying migration '"ReaddDefaultPluginRepository"'
[2025-10-02 23:25:38.971 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Migration '"ReaddDefaultPluginRepository"' applied successfully
[2025-10-02 23:25:38.971 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Applying migration '"AddDefaultCastReceivers"'
[2025-10-02 23:25:38.971 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:25:38.972 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:25:38.972 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Migration '"AddDefaultCastReceivers"' applied successfully
[2025-10-02 23:25:38.972 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Applying migration '"UpdateDefaultPluginRepository10.9"'
[2025-10-02 23:25:38.972 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:25:38.972 +02:00] [INF] [6] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:25:38.972 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Migration '"UpdateDefaultPluginRepository10.9"' applied successfully
[2025-10-02 23:25:38.973 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Applying migration '"MoveTrickplayFiles"'
[2025-10-02 23:25:39.035 +02:00] [INF] [6] Jellyfin.Server.Migrations.Routines.MoveTrickplayFiles: Moved 0 items in 00:00:00.0614329
[2025-10-02 23:25:39.036 +02:00] [INF] [6] Jellyfin.Server.Migrations.MigrationRunner: Migration '"MoveTrickplayFiles"' applied successfully
[2025-10-02 23:25:39.060 +02:00] [INF] [6] Main: Kestrel is listening on "0.0.0.0"
[2025-10-02 23:25:39.073 +02:00] [WRN] [6] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: No XML encryptor configured. Key {fa73f424-a3f4-4942-af53-8de8917ef9b4} may be persisted to storage in unencrypted form.
[2025-10-02 23:25:39.428 +02:00] [WRN] [6] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/run/s6-rc:s6-rc-init:mJJADC/servicedirs/svc-jellyfin/wwwroot". Static files may be unavailable.
[2025-10-02 23:25:39.473 +02:00] [INF] [6] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-10-02 23:25:39.491 +02:00] [INF] [6] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generate Trickplay Images" set to fire at 2025-10-03 03:00:00.000 +02:00, which is 03:34:20.5088694 from now.
[2025-10-02 23:25:39.493 +02:00] [INF] [6] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extract Chapter Images" set to fire at 2025-10-03 02:00:00.000 +02:00, which is 02:34:20.5068308 from now.
[2025-10-02 23:25:39.835 +02:00] [INF] [6] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.1.2"
[2025-10-02 23:25:39.880 +02:00] [INF] [6] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-10-02 23:25:39.896 +02:00] [INF] [6] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-10-02 23:25:39.912 +02:00] [INF] [6] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-10-02 23:25:40.028 +02:00] [INF] [6] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-10-02 23:25:42.500 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Clean Transcode Directory" Completed after 0 minute(s) and 0 seconds
[2025-10-02 23:25:42.502 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Clean up collections and playlists" Completed after 0 minute(s) and 0 seconds
[2025-10-02 23:25:43.247 +02:00] [INF] [6] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-10-02 23:25:43.248 +02:00] [INF] [6] Emby.Server.Implementations.ApplicationHost: ServerId: "bb54c7f5cdc84394bff8ee74ed400408"
[2025-10-02 23:25:43.248 +02:00] [INF] [6] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-10-02 23:25:43.248 +02:00] [INF] [6] Main: Startup complete 0:00:06.3815447
[2025-10-02 23:25:43.406 +02:00] [INF] [6] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Update Plugins" Completed after 0 minute(s) and 0 seconds
[2025-10-02 23:27:17.189 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:27:17.194 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:27:17.198 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:27:17.225 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:27:17.226 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:27:17.226 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:27:35.101 +02:00] [INF] [23] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has been denied (IP: "***********").
[2025-10-02 23:27:35.103 +02:00] [ERR] [23] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "Invalid username or password entered". URL "POST" "/Users/<USER>".
[2025-10-02 23:28:08.110 +02:00] [INF] [28] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:28:08.110 +02:00] [INF] [28] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:28:08.111 +02:00] [INF] [29] Jellyfin.Networking.PortForwardingHost: Stopping NAT discovery
[2025-10-02 23:28:08.144 +02:00] [WRN] [31] Jellyfin.Server.Implementations.Users.UserManager: No users, creating one with username "abc"
[2025-10-02 23:29:13.136 +02:00] [INF] [26] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:29:13.136 +02:00] [INF] [26] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:29:15.661 +02:00] [INF] [27] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-10-02 23:29:15.661 +02:00] [INF] [27] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-10-02 23:29:15.661 +02:00] [INF] [27] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-10-02 23:29:15.661 +02:00] [INF] [27] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "***********"]
[2025-10-02 23:29:15.661 +02:00] [INF] [27] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-10-02 23:29:15.661 +02:00] [INF] [27] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-10-02 23:29:15.661 +02:00] [INF] [27] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-10-02 23:29:16.520 +02:00] [INF] [27] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:29:16.521 +02:00] [INF] [27] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:29:16.522 +02:00] [INF] [31] Jellyfin.Networking.PortForwardingHost: Stopping NAT discovery
[2025-10-02 23:29:16.522 +02:00] [INF] [31] Jellyfin.Networking.PortForwardingHost: Starting NAT discovery
[2025-10-02 23:29:16.523 +02:00] [INF] [23] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-10-02 23:29:16.682 +02:00] [WRN] [23] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/data/playlists" is inaccessible or empty, skipping
[2025-10-02 23:29:16.761 +02:00] [WRN] [26] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/data/playlists" is inaccessible or empty, skipping
[2025-10-02 23:29:17.617 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Ocean's Eight (2018)/Ocean's Eight (2018) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:17.628 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/A Quiet Place - Day One (2024)/UnLgarTr4nqulo. Di4.1.2160.HR+DV.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:17.632 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Alien Resurrection (1997)/Alien Resurrection (1997) Remux-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:17.633 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Captain America - Brave New World (2025)/Captain America - Brave New World (2025) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:17.634 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Blade Runner 2049 (2017)/Blade Runner 2049 (2017) Remux-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:17.660 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Final Destination Bloodlines (2025)/Final Destination Bloodlines (2025) Remux-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:17.837 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Fury (2014)/Fury (2014) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:17.853 +02:00] [INF] [23] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Inglourious Basterds (2009)/Malditos Bastardos 4K  X265 60 FPS.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:18.027 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Jurassic Park III (2001)/Jurassic Park III (2001) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:18.027 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Jurassic World (2015)/Jurassic World (2015) Bluray-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:18.042 +02:00] [ERR] [40] MediaBrowser.Providers.Movies.MovieMetadataService: Error in "Probe Provider"
MediaBrowser.Common.FfmpegException: ffprobe failed - streams and format are both null.
   at MediaBrowser.MediaEncoding.Encoder.MediaEncoder.GetMediaInfoInternal(String inputPath, String primaryPath, MediaProtocol protocol, Boolean extractChapters, String probeSizeArgument, Boolean isAudio, Nullable`1 videoType, CancellationToken cancellationToken)
   at MediaBrowser.MediaEncoding.Encoder.MediaEncoder.GetMediaInfoInternal(String inputPath, String primaryPath, MediaProtocol protocol, Boolean extractChapters, String probeSizeArgument, Boolean isAudio, Nullable`1 videoType, CancellationToken cancellationToken)
   at MediaBrowser.Providers.MediaInfo.FFProbeVideoInfo.ProbeVideo[T](T item, MetadataRefreshOptions options, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.MetadataService`2.RunCustomProvider(ICustomMetadataProvider`1 provider, TItemType item, String logName, MetadataRefreshOptions options, RefreshResult refreshResult, CancellationToken cancellationToken)
[2025-10-02 23:29:18.081 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Jurassic World - Fallen Kingdom (2018)/Jurassic World - Fallen Kingdom (2018) Bluray-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:18.083 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Jurassic World Dominion (2022)/Jurassic World Dominion (2022) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:18.136 +02:00] [INF] [3] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Captain America - Brave New World (2025)" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:18.416 +02:00] [ERR] [23] MediaBrowser.Providers.Manager.ProviderManager: IOException saving to "/config/data/metadata/People/M/Michael C. Hall/folder.jpg". Will retry saving to "/config/data/metadata/library/08/0867cc13b4ed67441c0a7be42473fc75/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/M/Michael C. Hall/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileStreamOptions options)
   at MediaBrowser.Providers.Manager.ImageSaver.SaveImageToLocation(Stream source, String path, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ImageSaver.SaveImageToLocation(Stream source, String path, String retryPath, CancellationToken cancellationToken)
[2025-10-02 23:29:18.536 +02:00] [ERR] [45] MediaBrowser.Providers.Manager.ProviderManager: IOException saving to "/config/data/metadata/People/J/Jack Alcott/folder.jpg". Will retry saving to "/config/data/metadata/library/ef/efce2a7675f7a36b205b1d2921afe09e/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/J/Jack Alcott/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileStreamOptions options)
   at MediaBrowser.Providers.Manager.ImageSaver.SaveImageToLocation(Stream source, String path, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ImageSaver.SaveImageToLocation(Stream source, String path, String retryPath, CancellationToken cancellationToken)
[2025-10-02 23:29:18.641 +02:00] [ERR] [3] Emby.Server.Implementations.Library.LibraryManager: Cannot compute blurhash for "/config/data/metadata/People/D/David Zayas/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/D/David Zayas/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Jellyfin.Drawing.Skia.SkiaEncoder.GetImageBlurHash(Int32 xComp, Int32 yComp, String path)
   at Jellyfin.Drawing.ImageProcessor.GetImageBlurHash(String path, ImageDimensions imageDimensions)
   at Emby.Server.Implementations.Library.LibraryManager.UpdateImagesAsync(BaseItem item, Boolean forceUpdate)
[2025-10-02 23:29:18.661 +02:00] [INF] [33] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Black Mirror"
[2025-10-02 23:29:19.037 +02:00] [INF] [47] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Dark"
[2025-10-02 23:29:19.142 +02:00] [INF] [40] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Dexter"
[2025-10-02 23:29:19.646 +02:00] [INF] [40] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Dragon Ball Z"
[2025-10-02 23:29:20.737 +02:00] [INF] [46] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda (2008)/Kung Fu Panda (2008) Bluray-720p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:20.983 +02:00] [INF] [29] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Dexter: Pecado Original"
[2025-10-02 23:29:21.068 +02:00] [ERR] [39] Emby.Server.Implementations.Library.LibraryManager: Cannot compute blurhash for "/config/data/metadata/People/S/Steven Spielberg/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/S/Steven Spielberg/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Jellyfin.Drawing.Skia.SkiaEncoder.GetImageBlurHash(Int32 xComp, Int32 yComp, String path)
   at Jellyfin.Drawing.ImageProcessor.GetImageBlurHash(String path, ImageDimensions imageDimensions)
   at Emby.Server.Implementations.Library.LibraryManager.UpdateImagesAsync(BaseItem item, Boolean forceUpdate)
[2025-10-02 23:29:21.069 +02:00] [ERR] [39] Emby.Server.Implementations.Library.LibraryManager: Cannot compute blurhash for "/config/data/metadata/People/S/Steven Spielberg/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/S/Steven Spielberg/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Jellyfin.Drawing.Skia.SkiaEncoder.GetImageBlurHash(Int32 xComp, Int32 yComp, String path)
   at Jellyfin.Drawing.ImageProcessor.GetImageBlurHash(String path, ImageDimensions imageDimensions)
   at Emby.Server.Implementations.Library.LibraryManager.UpdateImagesAsync(BaseItem item, Boolean forceUpdate)
[2025-10-02 23:29:21.166 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 2 (2011)/Kung Fu Panda 2 (2011) Bluray-720p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:21.260 +02:00] [INF] [41] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 3 (2016)/Kung Fu Panda 3 (2016) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:21.313 +02:00] [INF] [41] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 4 (2024)/Kung Fu Panda 4 (2024) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:21.317 +02:00] [INF] [46] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Ocean's Eleven (2001)/Ocean's Eleven (2001) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:21.401 +02:00] [INF] [46] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Ocean's Thirteen (2007)/Ocean's Thirteen (2007) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:21.660 +02:00] [INF] [31] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E01 - Secrets WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:21.670 +02:00] [INF] [33] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Dexter: New Blood"
[2025-10-02 23:29:21.802 +02:00] [INF] [34] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Dexter: Resurrección"
[2025-10-02 23:29:21.830 +02:00] [INF] [27] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Alien: Planeta Tierra"
[2025-10-02 23:29:21.927 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E05 - F is for Fuck-Up WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:22.358 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E05 - F is for Fuck-Up WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:22.581 +02:00] [INF] [31] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E05 - Runaway WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:22.656 +02:00] [INF] [23] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Ocean's Twelve (2004)/Ocean's Twelve (2004) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:22.661 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E01 - A Beating Heart WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:22.674 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 1/Black Mirror - S01E01 - The National Anthem HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:22.797 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E01 - Neverland WEBRip-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:23.058 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E02 - Lies WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:23.277 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E01 - A Beating Heart WEBDL-2160p.en.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:23.307 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 1/Black Mirror - S01E01 - The National Anthem HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:23.379 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E01 - And in the beginning WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:23.388 +02:00] [ERR] [41] Emby.Server.Implementations.Library.LibraryManager: Cannot compute blurhash for "/config/data/metadata/People/J/Jerry Weintraub/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/J/Jerry Weintraub/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Jellyfin.Drawing.Skia.SkiaEncoder.GetImageBlurHash(Int32 xComp, Int32 yComp, String path)
   at Jellyfin.Drawing.ImageProcessor.GetImageBlurHash(String path, ImageDimensions imageDimensions)
   at Emby.Server.Implementations.Library.LibraryManager.UpdateImagesAsync(BaseItem item, Boolean forceUpdate)
[2025-10-02 23:29:23.389 +02:00] [ERR] [41] Emby.Server.Implementations.Library.LibraryManager: Cannot compute blurhash for "/config/data/metadata/People/J/Jerry Weintraub/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/J/Jerry Weintraub/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   at Jellyfin.Drawing.Skia.SkiaEncoder.GetImageBlurHash(Int32 xComp, Int32 yComp, String path)
   at Jellyfin.Drawing.ImageProcessor.GetImageBlurHash(String path, ImageDimensions imageDimensions)
   at Emby.Server.Implementations.Library.LibraryManager.UpdateImagesAsync(BaseItem item, Boolean forceUpdate)
[2025-10-02 23:29:23.530 +02:00] [ERR] [48] MediaBrowser.Providers.Manager.ProviderManager: IOException saving to "/config/data/metadata/People/A/Alessandro Carloni/folder.jpg". Will retry saving to "/config/data/metadata/library/9a/9afc8ad9e5b6ca7ef808dc5bc90b89ce/folder.jpg"
System.IO.IOException: The process cannot access the file '/config/data/metadata/People/A/Alessandro Carloni/folder.jpg' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Init(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Int64& fileLength, UnixFileMode& filePermissions)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, UnixFileMode openPermissions, Int64& fileLength, UnixFileMode& filePermissions, Boolean failForSymlink, Boolean& wasSymlink, Func`4 createOpenException)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileStreamOptions options)
   at MediaBrowser.Providers.Manager.ImageSaver.SaveImageToLocation(Stream source, String path, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ImageSaver.SaveImageToLocation(Stream source, String path, String retryPath, CancellationToken cancellationToken)
[2025-10-02 23:29:23.560 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Oppenheimer (2023)/Oppenheimer (2023) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:23.564 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E01 - A Beating Heart WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:23.747 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E01 - And in the beginning WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:23.795 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Prometheus (2012)/Prometheus (2012) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:23.905 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Shutter Island (2010)/Shutter Island PRUEBA.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:23.931 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/The Chronicles of Narnia - Prince Caspian (2008)/The Chronicles of Narnia - Prince Caspian (2008) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:23.932 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/The Chronicles of Narnia - The Lion, the Witch and the Wardrobe (2005)/The Chronicles of Narnia - The Lion, the Witch and the Wardrobe (2005) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.008 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/The Chronicles of Narnia - The Voyage of the Dawn Treader (2010)/The Chronicles of Narnia - The Voyage of the Dawn Treader (2010) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.055 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E03 - Past and Present WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.057 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E01 - Cold Snap WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.140 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E02 - Mr. October WEBRip-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.171 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 1/Black Mirror - S01E02 - Fifteen Million Merits HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.191 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E01 - Dexter WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.222 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/The Hateful Eight (2015)/The Hateful Eight (2015) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.304 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E02 - Camera Shy WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.401 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E02 - Kid in a Candy Store WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.510 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/The Lord of the Rings - The Fellowship of the Ring (2001)/The Lord of the Rings - The Fellowship of the Ring (2001) Bluray-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:24.728 +02:00] [INF] [31] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 1/Black Mirror - S01E02 - Fifteen Million Merits HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:24.837 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E02 - Camera Shy WEBDL-2160p.en.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:24.978 +02:00] [INF] [31] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E02 - Kid in a Candy Store WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:25.035 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E05 - Yamcha the Desert Bandit Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.124 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E03 - Metamorphosis WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.211 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E02 - Camera Shy WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:25.448 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E02 - Crocodile WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.529 +02:00] [INF] [46] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 1/Black Mirror - S01E03 - The Entire History of You HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.601 +02:00] [INF] [16] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Dexter - Resurrection - S01E02 - Camera Shy WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:25.603 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E04 - Double Lives WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.604 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E02 - Storm of Fuck WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.722 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E03 - Miami Vice WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.861 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E03 - Backseat Driver WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.930 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E04 - Observation WEBRip-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:25.939 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 1/Black Mirror - S01E03 - The Entire History of You HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:26.169 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E03 - Miami Vice WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:26.328 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E03 - Popping Cherry WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:26.357 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E03 - Backseat Driver WEBDL-2160p.en.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:26.453 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/The Lost World - Jurassic Park (1997)/The Lost World - Jurassic Park (1997) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:26.676 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 2/Black Mirror - S02E01 - Be Right Back HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:26.752 +02:00] [INF] [46] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E03 - Backseat Driver WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:26.944 +02:00] [INF] [46] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E05 - In Space, No One WEBRip-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.035 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E05 - Truths WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.062 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E04 - Fender Bender WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.320 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E04 - Let's Give the Boy a Hand WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.329 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 2/Black Mirror - S02E01 - Be Right Back HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:27.363 +02:00] [INF] [31] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E03 - Smoke Signals WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.404 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Weapons (2025)/Weapons (2025) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.490 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E04 - Fender Bender WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:27.544 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E04 - Call Me Red WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.724 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Wolfs (2024)/Wolfs (2024) WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:27.804 +02:00] [INF] [36] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Rick y Morty"
[2025-10-02 23:29:27.834 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E06 - The Fly WEBRip-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:28.013 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E04 - Call Me Red WEBDL-2160p.en.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:28.089 +02:00] [INF] [33] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "A dos metros bajo tierra"
[2025-10-02 23:29:28.140 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E05 - Love American Style WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:28.189 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E06 - The Joy of Killing WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:28.341 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 2/Black Mirror - S02E02 - White Bear HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:28.429 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E04 - Call Me Red WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:28.550 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E06 - Sic Mundus Creatus Est WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:28.611 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E06 - The Joy of Killing WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:28.677 +02:00] [INF] [46] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E07 - Emergence HDTV-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:28.808 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E04 - H is for Hero WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:28.839 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 2/Black Mirror - S02E02 - White Bear HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:28.954 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E06 - Return to Sender WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:29.165 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E06 - Cats & Mouse WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:29.412 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E07 - The Big Bad Body Problem WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:29.544 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Alien - Earth/Season 1/Alien - Earth - S01E08 - The Real Monsters WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:29.615 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 2/Black Mirror - S02E03 - The Waldo Moment HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:29.637 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E06 - Cats & Mouse WEBDL-2160p.en.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:29.771 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E07 - The Big Bad Body Problem WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:29.812 +02:00] [INF] [31] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E07 - Circle of Friends WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:29.919 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E07 - Crossroads WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:29.923 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E06 - Cats & Mouse WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:30.118 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 2/Black Mirror - S02E03 - The Waldo Moment HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:30.259 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E06 - Too Many Tuna Sandwiches WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:30.441 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E08 - Business and Pleasure WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:30.673 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E08 - Shrink Wrap WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:30.784 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E07 - Course Correction WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:30.897 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E01 - Nosedive HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:30.941 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E08 - Business and Pleasure WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:31.097 +02:00] [INF] [47] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Alien: Planeta Tierra"
[2025-10-02 23:29:31.098 +02:00] [INF] [47] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: dd965816-822b-a5a5-2cac-592b3ac71e90
[2025-10-02 23:29:31.180 +02:00] [INF] [42] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-10-02 23:29:31.181 +02:00] [INF] [42] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 0/0
[2025-10-02 23:29:31.182 +02:00] [INF] [42] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 24f5447d-4585-49f7-b7b4-460038974b9d
[2025-10-02 23:29:31.245 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E07 - Course Correction WEBDL-2160p.en.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:31.251 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E08 - As You Sow, so You Shall Reap WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:31.378 +02:00] [INF] [47] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:29:31.402 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E01 - Nosedive HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:31.500 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E09 - Father Knows Best WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:31.546 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E09 - Blood Drive WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:31.603 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E07 - Course Correction WEBDL-2160p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:31.612 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E07 - Skin of Her Teeth WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:31.646 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E05 - An Open Book WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:31.960 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:31.962 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:31.963 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:29:32.017 +02:00] [INF] [34] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:32.017 +02:00] [INF] [34] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:32.017 +02:00] [INF] [34] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:29:32.042 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E09 - Blood Drive WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:32.217 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E05 - An Open Book WEBDL-1080p Proper.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:32.348 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E08 - The Kill Room Where It Happens WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:32.391 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E02 - Playtest HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:32.489 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E10 - Seeing Red WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:32.784 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E09 - Everything Is Now WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:32.793 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E10 - Code Blues WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:32.819 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E08 - The Kill Room Where It Happens WEBDL-2160p.en.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:32.879 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E02 - Playtest HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:33.150 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E08 - The Kill Room Where It Happens WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:33.150 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E08 - Unfair Game WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:33.164 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Original Sin/Season 1/Dexter - Original Sin - S01E10 - Code Blues WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:33.281 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E11 - Truth Be Told WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:33.372 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E01 - Pilot WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:33.453 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E03 - Shut Up and Dance WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:33.805 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E01 - Pilot WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:33.813 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E03 - Shut Up and Dance WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:33.861 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E01 - Pilot HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:33.956 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dark/Season 1/Dark - S01E10 - Alpha and Omega WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:34.003 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E09 - Touched By An Ángel WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:34.018 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 1/Dexter - S01E12 - Born Free WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:34.237 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E09 - The Family Business WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:34.284 +02:00] [INF] [36] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Dexter: Pecado Original"
[2025-10-02 23:29:34.284 +02:00] [INF] [36] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: b15d8eb2-8e0e-6fe1-3781-d4d9e9a94bc8
[2025-10-02 23:29:34.416 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E09 - Touched By An Ángel WEBDL-2160p.en.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:34.549 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E04 - San Junipero HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:34.571 +02:00] [INF] [33] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E09 - Touched By An Ángel WEBDL-2160p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:34.624 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E01 - It's Alive! WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:34.640 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E02 - The Will WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:34.870 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E04 - San Junipero HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:34.947 +02:00] [INF] [3] Emby.Server.Implementations.MediaEncoder.EncodingManager: Stopping chapter extraction for "Dexter - Resurrection - S01E09 - Touched By An Ángel WEBDL" because a chapter was found with a position greater than the runtime.
[2025-10-02 23:29:34.984 +02:00] [INF] [29] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Dark"
[2025-10-02 23:29:34.984 +02:00] [INF] [29] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: a31b5060-5ef4-4fbd-ccfd-c277ee90cc25
[2025-10-02 23:29:35.028 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E02 - The Will WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:35.031 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - New Blood/Season 1/Dexter - New Blood - S01E10 - Sins of the Father WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:35.314 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E10 - And Justice For All… WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:35.453 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E05 - Men Against Fire HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:35.456 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E03 - The Foot WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:35.468 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E02 - Lawnmower Dog HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:35.530 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E02 - Waiting to Exhale WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:35.673 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E10 - And Justice For All… WEBDL-2160p.en.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:35.831 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E03 - The Foot WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:35.887 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E05 - Men Against Fire HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:35.926 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E10 - And Justice For All… WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:36.150 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E03 - An Inconvenient Lie WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:36.203 +02:00] [INF] [48] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Dexter: New Blood"
[2025-10-02 23:29:36.203 +02:00] [INF] [48] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 889d9f56-9fc8-15d8-9962-70d640557be6
[2025-10-02 23:29:36.406 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E04 - Familia WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:36.453 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E06 - Hated in the Nation HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:36.631 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E04 - Familia WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:36.632 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E05 - Murder Horny WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:36.636 +02:00] [INF] [31] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E03 - Anatomy Park HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:36.706 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 3/Black Mirror - S03E06 - Hated in the Nation HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:36.776 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E04 - See-Through WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:36.979 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E05 - Murder Horny WEBDL-2160p.en.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:37.002 +02:00] [INF] [47] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:37.002 +02:00] [INF] [47] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:37.002 +02:00] [INF] [47] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:29:37.193 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter - Resurrection/Season 1/Dexter - Resurrection - S01E05 - Murder Horny WEBDL-2160p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:37.401 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E06 - The Room WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:37.403 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E05 - The Dark Defender WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:37.412 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E01 - USS Callister HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:37.764 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E06 - The Room WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:37.809 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E01 - USS Callister HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:37.884 +02:00] [INF] [48] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Miércoles"
[2025-10-02 23:29:37.893 +02:00] [INF] [34] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Dexter: Resurrección"
[2025-10-02 23:29:37.893 +02:00] [INF] [34] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: f3cb3e06-e76e-dcf6-6d6c-785afc11dcb3
[2025-10-02 23:29:37.912 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E04 - M. Night Shaym-Aliens! HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:37.940 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E06 - Dex, Lies, and Videotape WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:38.176 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E07 - Brotherhood WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:38.222 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E02 - Arkangel HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:38.362 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E01 - Secret of the Dragon Balls Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:38.366 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E07 - Brotherhood WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:38.368 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E02 - Arkangel HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:38.419 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E07 - That Night, A Forest Grew WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:38.761 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E05 - Meeseeks and Destroy HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:38.778 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E01 - Wednesday's Child is Full of Woe WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.065 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E08 - Morning Comes WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.067 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E08 - Crossroads WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.137 +02:00] [INF] [47] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Wednesday - S01E01 - Wednesday's Child is Full of Woe WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:39.139 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E03 - Crocodile HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.380 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E08 - Crossroads WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:39.464 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E03 - Crocodile HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:39.583 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E09 - Resistance Is Futile WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.634 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E06 - Rick Potion #9 HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.668 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E02 - Woe is the Loneliest Number WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.910 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E09 - Life's Too Short WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:39.988 +02:00] [INF] [44] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Wednesday - S01E02 - Woe is the Loneliest Number WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:40.095 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E04 - Hang the DJ HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.131 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E10 - There's Something About Harry WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.255 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E09 - Life's Too Short WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:40.357 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E04 - Hang the DJ HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:40.361 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E03 - Friend or Woe WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.511 +02:00] [INF] [24] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Wednesday - S01E03 - Friend or Woe WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:40.605 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E04 - Woe What a Night WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.607 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E11 - Left Turn Ahead WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.627 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E07 - Raising Gazorpazorp HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.766 +02:00] [INF] [44] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Wednesday - S01E04 - Woe What a Night WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:40.795 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E05 - Metalhead HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.821 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E10 - The New Person WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:40.914 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E05 - Metalhead HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:40.991 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E10 - The New Person WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:41.038 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 2/Dexter - S02E12 - The British Invasion WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.048 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E05 - You Reap What You Woe WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.159 +02:00] [INF] [16] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Wednesday - S01E05 - You Reap What You Woe WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:41.248 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E06 - Quid Pro Woe WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.256 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E06 - Black Museum HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.305 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E01 - Our Father WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.350 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E11 - The Trip WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.371 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E08 - Rixty Minutes HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.386 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 4/Black Mirror - S04E06 - Black Museum HDTV-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:41.427 +02:00] [INF] [47] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Wednesday - S01E06 - Quid Pro Woe WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:41.490 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E11 - The Trip WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:41.492 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E07 - If You Don't Woe Me By Now WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.595 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E02 - The Emperor's Quest Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.608 +02:00] [INF] [44] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Wednesday - S01E07 - If You Don't Woe Me By Now WEBDL" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-02 23:29:41.639 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 1/Wednesday - S01E08 - A Murder of Woes WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.775 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 5/Black Mirror - S05E01 - Striking Vipers WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.839 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E02 - Finding Freebo WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.968 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E09 - Something Ricked This Way Comes HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:41.976 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 5/Black Mirror - S05E01 - Striking Vipers WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:41.983 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E01 - Here We Woe Again HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.015 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:42.015 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:42.015 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:29:42.063 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E12 - A Private Life WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.155 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E03 - The Lion Sleeps Tonight WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.324 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E12 - A Private Life WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:42.468 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 5/Black Mirror - S05E02 - Smithereens WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.564 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E04 - All in the Family WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.583 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 5/Black Mirror - S05E02 - Smithereens WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:42.677 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E02 - The Devil You Woe HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.720 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E13 - Knock, Knock WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.737 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E10 - Close Rick-Counters of the Rick Kind HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:42.920 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 1/Six Feet Under - S01E13 - Knock, Knock WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:42.997 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E05 - Turning Biminese WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.135 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 5/Black Mirror - S05E03 - Rachel, Jack and Ashley Too WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.137 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E03 - Call of the Woe HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.255 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E05 - The Invisible Woman WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.269 +02:00] [INF] [29] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 5/Black Mirror - S05E03 - Rachel, Jack and Ashley Too WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:43.353 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E06 - Sí Se Puede WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.459 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E11 - Ricksy Business HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.483 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:43.484 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:29:43.484 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:29:43.615 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E05 - The Invisible Woman WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:43.655 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E04 - If These Woes Could Talk HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.824 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E01 - Joan Is Awful WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:43.827 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E07 - Easy as Pie WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.000 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E05 - Hyde and Woe Seek WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.018 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E01 - Joan Is Awful WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:44.094 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E01 - In the Game WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.130 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E08 - The Damage a Man Can Do WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.237 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E01 - In the Game WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:44.331 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E01 - A Rickle in Time HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.344 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E06 - Woe Thyself WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.412 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E02 - Out, Out Brief Candle WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.458 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E09 - About Last Night WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.469 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E03 - The Nimbus Cloud of Roshi Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.673 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E02 - Out, Out Brief Candle WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:44.712 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E02 - Loch Henry WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.765 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E07 - Woe Me the Money WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.850 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E02 - Loch Henry WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:44.917 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E10 - Go Your Own Way WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:44.984 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Wednesday/Season 2/Wednesday - S02E08 - This Means Woe WEBDL-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.117 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E03 - Beyond the Sea WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.178 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E03 - The Plan WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.332 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E03 - Beyond the Sea WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:45.341 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E11 - I Had a Dream WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.348 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E03 - The Plan WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:45.506 +02:00] [INF] [16] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Miércoles"
[2025-10-02 23:29:45.506 +02:00] [INF] [16] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 9e2f22dc-22ed-9bed-41e6-11647cd1d51a
[2025-10-02 23:29:45.580 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E02 - Mortynight Run HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.603 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E04 - Mazey Day WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.656 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 3/Dexter - S03E12 - Do You Take Dexter Morgan WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.745 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E04 - Driving Mr. Mossback WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:45.820 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E04 - Mazey Day WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:45.948 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E04 - Driving Mr. Mossback WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:46.122 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E01 - Living the Dream WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:46.126 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E05 - Demon 79 WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:46.240 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E06 - In Place of Anger WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:46.394 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 6/Black Mirror - S06E05 - Demon 79 WEBDL-1080p.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:46.397 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E06 - In Place of Anger WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:46.507 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E03 - Auto Erotic Assimilation HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:46.695 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E02 - Remains to Be Seen WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:46.764 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E01 - Common People HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:46.893 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E07 - Back to the Garden WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:46.920 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E01 - Common People HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:47.050 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E03 - Blinded by the Light WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.183 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E07 - Back to the Garden WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:47.365 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E04 - Dex Takes a Holiday WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.409 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E04 - Oolong the Terrible Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.428 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E04 - Total Rickall HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.433 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E08 - It's the Most Wonderful Time of the Year WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.538 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E02 - Bête Noire HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.615 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E08 - It's the Most Wonderful Time of the Year WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:47.675 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E02 - Bête Noire HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:47.769 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E05 - Dirty Harry WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.874 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E03 - Hotel Reverie HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:47.963 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E03 - Hotel Reverie HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:47.993 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E06 - If I Had a Hammer WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.026 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E12 - I'll Take You WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.039 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E05 - Get Schwifty HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.295 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E12 - I'll Take You WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:48.381 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E04 - Plaything HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.404 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E07 - Slack Tide WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.498 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E04 - Plaything HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:48.606 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E13 - The Last Time WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.712 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E06 - The Ricks Must Be Crazy HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.714 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E05 - Eulogy HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.734 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E08 - Road Kill WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:48.828 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 2/Six Feet Under - S02E13 - The Last Time WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:48.912 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E05 - Eulogy HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:49.151 +02:00] [INF] [36] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E06 - USS Callister - Into Infinity HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:49.171 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 3/Six Feet Under - S03E02 - You Never Know WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:49.190 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E09 - Hungry Man WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:49.332 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Black Mirror/Season 7/Black Mirror - S07E06 - USS Callister - Into Infinity HDTV-1080p.es.hi.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:49.386 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 3/Six Feet Under - S03E02 - You Never Know WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:49.425 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E07 - Big Trouble in Little Sanchez HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:49.590 +02:00] [INF] [24] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Black Mirror"
[2025-10-02 23:29:49.590 +02:00] [INF] [24] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 3ab5e62c-23e3-e086-e3d9-df89a57f7d0e
[2025-10-02 23:29:49.795 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 3/Six Feet Under - S03E03 - The Eye Inside WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:49.845 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 3/Six Feet Under - S03E03 - The Eye Inside WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:49.854 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E10 - Lost Boys WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:49.905 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E08 - Interdimensional Cable 2 - Tempting Fate HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:49.964 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E06 - Keep an Eye on the Dragon Balls Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:50.153 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 3/Six Feet Under - S03E04 - Nobody Sleeps WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:50.236 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E11 - Hello, Dexter Morgan WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:50.335 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 3/Six Feet Under - S03E04 - Nobody Sleeps WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:50.523 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E12 - The Getaway WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:50.540 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 4/Six Feet Under - S04E02 - In Case of Rapture WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:50.616 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E09 - Look Who's Purging Now HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:50.795 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 4/Six Feet Under - S04E02 - In Case of Rapture WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:50.899 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E01 - My Bad WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.105 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E10 - The Wedding Squanchers HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.186 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 4/Six Feet Under - S04E04 - Can I Come Up Now WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.319 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E07 - The Ox-King on Fire Mountain Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.320 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 4/Six Feet Under - S04E04 - Can I Come Up Now WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:51.416 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E02 - Hello, Bandit WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.551 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 4/Six Feet Under - S04E11 - Bomb Shelter WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.635 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 4/Six Feet Under - S04E11 - Bomb Shelter WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:51.698 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E03 - Practically Perfect WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.701 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E01 - The Rickshank Rickdemption HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:51.940 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E01 - A Coat of White Primer WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:52.024 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E04 - Beauty and the Beast WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:52.160 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E01 - A Coat of White Primer WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:52.446 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E05 - First Blood WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:52.553 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E02 - Dancing for Me WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:52.584 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E02 - Rickmancing the Stone HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:52.731 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E02 - Dancing for Me WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:52.853 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E06 - Everything Is Illumenated WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:53.041 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E05 - Eat a Peach WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:53.243 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E05 - Eat a Peach WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:53.247 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E07 - Circle Us WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:53.542 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E03 - Pickle Rick HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:53.563 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E08 - Take It! WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:53.733 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E08 - The Kamehameha Wave Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:53.816 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E06 - The Rainbow of Her Reasons WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:53.934 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E06 - The Rainbow of Her Reasons WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:53.986 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E09 - Teenage Wasteland WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:54.073 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E04 - Vindicators 3 - The Return of Worldender HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:54.325 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E08 - Singing for Our Lives WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:54.348 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E10 - In the Beginning WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:54.536 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E08 - Singing for Our Lives WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:54.728 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E05 - The Whirly Dirly Conspiracy HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:54.753 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E11 - Hop a Freighter WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:54.926 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E12 - Everyone's Waiting WEBDL-1080p Proper.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:55.063 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Six Feet Under/Season 5/Six Feet Under - S05E12 - Everyone's Waiting WEBDL-1080p Proper.es.srt\" -threads 0 -v warning -print_format json -show_streams -show_format"
[2025-10-02 23:29:55.125 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E12 - The Big One WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:55.416 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E01 - Those Kinds of Things WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:55.441 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E06 - Rest and Ricklaxation HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:55.537 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E09 - Boss Rabbit's Magic Touch Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:55.608 +02:00] [INF] [42] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "A dos metros bajo tierra"
[2025-10-02 23:29:55.608 +02:00] [INF] [42] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 15927319-27a9-af57-cfae-66104b608684
[2025-10-02 23:29:55.803 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E07 - The Ricklantis Mixup HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:55.818 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E02 - Once Upon a Time WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:56.268 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E03 - Smokey and the Bandit WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:56.395 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E08 - Morty's Mind Blowers HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:56.478 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E04 - A Horse of a Different Color WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:56.881 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E05 - The Angel of Death WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:57.002 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E09 - The ABC's of Beth HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:57.149 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E06 - Just Let Go WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:57.484 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E10 - The Dragon Balls are Stolen! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:57.580 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E10 - The Rickchurian Mortydate HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:57.646 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E07 - Nebraska WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:58.107 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E08 - Sin of Omission WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:58.190 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E01 - Edge of Tomorty - Rick Die Rickpeat HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:58.600 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E09 - Get Gellar WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:58.847 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E10 - Ricochet Rabbit WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:58.884 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E02 - The Old Man and the Seat HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:59.150 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E11 - Talk to the Hand WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:59.285 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E11 - The Penalty is Pinball Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:59.376 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 6/Dexter - S06E12 - This is the Way the World Ends WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:59.627 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E03 - One Crew Over the Crewcoo's Morty HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:29:59.690 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E01 - Are You WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:00.203 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E02 - Sunshine and Frosty Swirl WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:00.379 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E04 - Claw and Hoarder - Special Ricktim's Morty HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:00.459 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E03 - Buck the System WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:00.469 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E12 - A Wish to the Eternal Dragon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:00.732 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E04 - Run WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:00.905 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E05 - Rattlestar Ricklactica HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:01.027 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E05 - Swim Deep WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:01.368 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E06 - Do the Wrong Thing WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:01.544 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E06 - Never Ricking Morty HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:01.693 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E07 - Chemistry WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:02.031 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E08 - Argentina WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:02.215 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E07 - Promortyus HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:02.315 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 1/Dragon Ball - S01E13 - The Legend of Goku Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:02.355 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E09 - Helter Skelter WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:02.582 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E10 - The Dark. Whatever WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:02.618 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E08 - The Vat of Acid Episode HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:02.990 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E11 - Do You See What I See WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:03.253 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 7/Dexter - S07E12 - Surprise, Motherfucker! WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:03.337 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E09 - Childrick of Mort HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:03.394 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E06 - The Tournament Begins Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:03.550 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E01 - A Beautiful Day WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:03.896 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E10 - Star Mort - Rickturn of the Jerri HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:04.061 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E02 - Every Silver Lining WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:04.304 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E03 - What's Eating Dexter Morgan WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:04.354 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E01 - Mort Dinner Rick Andre HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:04.539 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:04.613 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E01 - Goku's Rival Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:04.797 +02:00] [INF] [24] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E05 - This Little Piggy WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:04.921 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E02 - Mortyplicity HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:05.142 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E06 - A Little Reflection WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:05.346 +02:00] [WRN] [24] Microsoft.EntityFrameworkCore.Query: Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
[2025-10-02 23:30:05.443 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E07 - Dress Code WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:05.737 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E03 - A Rickconvenient Mort HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:05.958 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E08 - Are We There Yet WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:06.076 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E02 - Look Out for Launch! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:06.153 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E09 - Make Your Own Kind of Music WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:06.346 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E04 - Rickdependence Spray HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:06.597 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E10 - Goodbye Miami WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:06.885 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E11 - Monkey in a Box WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:07.037 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E05 - Amortycan Grickfitti HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:07.165 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E12 - Remember the Monsters WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:07.608 +02:00] [INF] [48] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Dexter"
[2025-10-02 23:30:07.608 +02:00] [INF] [48] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: bab6c838-f2b4-a7ba-db20-ca2684b1e29d
[2025-10-02 23:30:07.709 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E03 - Find That Stone Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:07.732 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E06 - Rick & Morty's Thanksploitation Spectacular HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:08.407 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E07 - Gotron Jerrysis Rickvangelion HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:09.013 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E08 - Rickternal Friendshine of the Spotless Mort HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:09.021 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E04 - Milk Delivery Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:09.571 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E09 - Forgetting Sarick Mortshall HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:10.061 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 5/Rick and Morty - S05E10 - Rickmurai Jack HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:10.171 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E05 - The Turtle Hermit Way Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:10.620 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E01 - Solaricks HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:11.174 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E07 - Elimination Round Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:11.326 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E02 - Rick - A Mort Well Lived HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:11.735 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E03 - Bethic Twinstinct HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:12.033 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E08 - Smells Like Trouble Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:12.117 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E04 - Night Family HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:12.759 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E05 - Final DeSmithation HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:13.058 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E09 - Quarter Finals Begin Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:13.196 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E06 - JuRicksic Mort HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:13.706 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E07 - Full Meta Jackrick HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:14.101 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E08 - Analyze Piss HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:14.287 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E10 - Monster Beast Giran Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:14.614 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E09 - A Rick in King Mortur's Mort HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:15.050 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 6/Rick and Morty - S06E10 - Ricktional Mortpoon's Rickmas Mortcation HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:15.595 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E11 - Krillin's Frantic Attack! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:15.760 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E01 - How Poopy Got His Poop Back HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:16.454 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E02 - The Jerrick Trap HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:16.736 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E12 - Danger From Above Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:16.997 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E03 - Air Force Wong HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:17.404 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E04 - That's Amorte HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:17.437 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E13 - The Grand Finals Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:17.754 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E05 - Unmortricken HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:18.360 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E06 - Rickfending Your Mort HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:18.771 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E07 - Wet Kuat Amortican Summer HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:18.868 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E14 - Number One Under The Moon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:19.117 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E08 - Rise of the Numbericons - The Movie HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:19.651 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E09 - Mort - Ragnarick HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:20.051 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 2/Dragon Ball - S02E15 - The Final Blow Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:20.144 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 7/Rick and Morty - S07E10 - Fear No Mort HDTV-720p.mp4\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:20.695 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E01 - Summer of All Fears HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:21.104 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E02 - Valkyrick HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:21.236 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E01 - The Roaming Lake Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:21.267 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E03 - The Rick, The Mort & The Ugly HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:21.789 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E04 - The Last Temptation of Jerry HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:22.251 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E05 - Cryo Mort a Rickver HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:22.750 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E06 - The CuRicksous Case of Bethjamin Button HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:22.820 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E02 - Pilaf and The Mystery Force Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:23.141 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E07 - Ricker Than Fiction HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:23.727 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E08 - Nomortland HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:23.919 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E03 - Wedding Plans Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:24.050 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E09 - Morty Daddy HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:24.485 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E10 - Hot Rick HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:24.954 +02:00] [INF] [40] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Rick y Morty"
[2025-10-02 23:30:24.954 +02:00] [INF] [40] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 6ed225c1-b48f-10be-fa7e-3694be710444
[2025-10-02 23:30:25.493 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E04 - The Flying Fortress – Vanished! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:26.554 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E05 - The Legend of a Dragon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:27.616 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E06 - Cruel General Red Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:28.606 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E07 - Cold Reception Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:29.457 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E08 - Major Metallitron Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:30.679 +02:00] [INF] [40] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E09 - Ninja Murasaki is Coming! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:31.723 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E11 - Mysterious Android No. 8 Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:32.726 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E12 - Horrifying Buyon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:33.928 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E13 - The Fall of Muscle Tower Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:35.213 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E14 - The Secret of Dr. Flappe Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:35.970 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E15 - A Trip to the City Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:36.906 +02:00] [INF] [49] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E16 - Master Thief, Haski Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:37.899 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E17 - Danger in the Air Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:39.068 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E18 - Bulma's Bad Day Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:40.247 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E19 - Kame House - Found! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:41.355 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E21 - Roshi Surprise Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:42.422 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E22 - The Trap is Sprung Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:43.461 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E23 - Beware of Robot Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:44.597 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E24 - The Pirate Treasure Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:45.690 +02:00] [INF] [25] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:30:45.690 +02:00] [INF] [25] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:30:45.855 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E32 - Tao Attacks! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:46.933 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E33 - Korin Tower Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:47.580 +02:00] [INF] [16] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:30:47.959 +02:00] [INF] [49] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E34 - Sacred Water Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:49.087 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E35 - The Return of Goku Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:50.202 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E36 - The Last of Mercenary Tao Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:50.626 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:30:50.626 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:30:50.626 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:30:50.678 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:30:50.678 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:30:50.678 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:30:51.271 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E37 - Confront the Red Ribbon Army Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:52.420 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E38 - A Real Bind Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:53.674 +02:00] [INF] [49] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E39 - The End of Commander Red Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:54.833 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E40 - The Last Dragon Ball Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:55.730 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E10 - Five Murasakis Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:56.905 +02:00] [INF] [16] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E20 - Deep Blue Sea Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:57.537 +02:00] [INF] [47] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-10-02 23:30:57.537 +02:00] [INF] [47] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 0/0
[2025-10-02 23:30:57.537 +02:00] [INF] [47] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 24f5447d-4585-49f7-b7b4-460038974b9d
[2025-10-02 23:30:57.682 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E25 - Blue, Black and Blue Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:58.820 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E31 - The Notorious Mercenary Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:59.692 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E26 - Escape From Pirate Cave Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:30:59.836 +02:00] [INF] [47] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:31:00.854 +02:00] [INF] [38] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E27 - Penguin Village Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:02.060 +02:00] [INF] [38] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E28 - Strange Visitor Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:03.057 +02:00] [INF] [38] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E29 - Arale vs. Blue Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:03.870 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 3/Dragon Ball - S03E30 - The Land of Korin Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:04.691 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E01 - Who is Fortuneteller Baba Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:05.852 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E02 - We Are The Five Warriors Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:06.796 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E03 - Deadly Battle Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:07.692 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E04 - Goku's Turn Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:08.926 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E05 - The Devilmite Beam Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:10.016 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E06 - The Mysterious Fifth Man Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:10.994 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E07 - The Strong Ones Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:12.192 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E08 - The True Colors of the Masked Man Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:13.238 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E09 - Pilaf's Tactics Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:14.266 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E11 - Terror and Plague Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:15.178 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E12 - Goku vs. Sky Dragon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:16.234 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E13 - Goku Goes to Demon Land Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:17.205 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E14 - The Rampage Of InoShikaCho Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:18.117 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E15 - Which Way to Papaya Island Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:19.373 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E16 - Rivals and Arrivals Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:20.487 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E17 - Preliminary Peril Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:21.391 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E18 - Then There Were Eight Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:22.429 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E19 - Yamcha vs. Tien Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:23.360 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E21 - Full-Moon Vengeance Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:24.398 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E22 - The Dodon Wave! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:25.365 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E23 - Counting Controversy! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:26.441 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E24 - Goku Enters The Ring Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:27.410 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E26 - Stepping Down Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:28.313 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E27 - Goku vs. Krillin Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:29.407 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E28 - Tail's Tale Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:30.282 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E29 - Final Match - Goku vs. Tien Shinhan Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:31.382 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E30 - Victory's Edge Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:32.381 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E31 - Tien's Insurrection Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:33.413 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E32 - The Spirit Cannon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:34.633 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E33 - The Fallen Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:35.542 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E10 - The Eternal Dragon Rises Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:36.471 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E20 - Yamcha's Big Break Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:37.718 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 4/Dragon Ball - S04E25 - Tien Shinhan vs. Jackie Chun Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:38.687 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E01 - Enter King Piccolo Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:39.933 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E02 - Tambourine Attacks! Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:40.730 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E03 - Mark of the Demon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:41.776 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E04 - Here Comes Yajirobe Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:42.870 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E12 - Siege on Chow Castle Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:43.865 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E13 - Conquest and Power Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:44.818 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E14 - Awaken Darkness Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:45.968 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E15 - A Taste of Destiny Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:46.302 +02:00] [INF] [47] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:46.302 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:46.302 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:46.302 +02:00] [INF] [47] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:46.302 +02:00] [INF] [47] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:31:46.303 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:31:46.841 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E06 - Tien's Atonement Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:47.890 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E07 - Goku's Revenge Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:49.034 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E08 - Goku vs. King Piccolo Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:50.134 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E09 - Piccolo Closes In Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:51.261 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E10 - Roshi's Gambit Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:51.281 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:51.281 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:51.282 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:31:52.286 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E23 - Temple Above the Clouds Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:52.965 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:52.965 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:31:52.965 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:31:53.352 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E24 - Earth's Guardian Emerges Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:54.364 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E25 - Eternal Dragon Resurrected Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:55.394 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E26 - Quicker Than Lightning Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:56.398 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E27 - Secret of the Woods Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:57.352 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E28 - The Time Room Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:58.204 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E29 - Goku's Doll Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:31:59.204 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E30 - Walking Their Own Ways Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:00.178 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E31 - Hotter Than Lava Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:01.275 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E05 - Terrible Tambourine Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:02.425 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E11 - King Piccolo's Wish Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:03.711 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E16 - The Ultimate Sacrifice Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:04.748 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E22 - Lost and Found Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:05.794 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E17 - Prelude to Vengeance Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:06.967 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E18 - Battle Cry Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:07.986 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E19 - Goku Strikes Back Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:09.015 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E20 - The Biggest Crisis Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:10.062 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 5/Dragon Ball - S05E21 - Final Showdown Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:11.327 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E01 - Changes Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:12.315 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E02 - Preliminary Peril Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:13.428 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E03 - Battle of Eight Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:14.401 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E04 - Tien Shinhan vs Mercenary Tao Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:15.541 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E12 - Super Kamehameha Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:16.093 +02:00] [INF] [37] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Chapter Segments Provider" "*******"
[2025-10-02 23:32:16.098 +02:00] [INF] [37] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:32:16.730 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E13 - Junior no More Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:17.960 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E14 - Goku's Trap Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:19.210 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E15 - Goku Hangs On Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:19.223 +02:00] [INF] [47] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Kodi Sync Queue" "1*******"
[2025-10-02 23:32:19.226 +02:00] [INF] [47] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:32:20.497 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E16 - The Victor Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:21.540 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E06 - The Mysterious Hero Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:22.722 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E07 - Rematch Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:24.069 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E08 - Goku Gains Speed Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:25.344 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E09 - The Four Faces of Tien Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:26.017 +02:00] [INF] [45] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Playback Reporting" "********"
[2025-10-02 23:32:26.020 +02:00] [INF] [45] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:32:26.463 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E10 - Kami vs Piccolo Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:27.726 +02:00] [INF] [45] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E17 - Dress in Flames Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:28.935 +02:00] [INF] [47] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E18 - The Fire-Eater Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:30.033 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E19 - Outrageous Octagon Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:30.346 +02:00] [INF] [28] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Trakt" "********"
[2025-10-02 23:32:30.350 +02:00] [INF] [30] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:32:31.184 +02:00] [INF] [20] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E20 - Mystery of the Dark World Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:32.278 +02:00] [INF] [19] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E21 - The End, The Beginning Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:33.521 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E05 - Anonymous Proposal Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:34.763 +02:00] [INF] [19] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Dragon Ball/Season 6/Dragon Ball - S06E11 - Battle for the Future Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-10-02 23:32:36.156 +02:00] [INF] [30] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Dragon Ball Z"
[2025-10-02 23:32:36.156 +02:00] [INF] [30] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 50694a55-8cd2-ce31-7cc4-283d0a6d8fe5
[2025-10-02 23:32:36.511 +02:00] [INF] [47] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Subtitle Extract" "*******"
[2025-10-02 23:32:36.514 +02:00] [INF] [47] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:32:44.162 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/3faabb48310c41ada18d4b951a6fe9e5.png"
[2025-10-02 23:32:44.595 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/3faabb48310c41ada18d4b951a6fe9e5.png"
[2025-10-02 23:32:44.613 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/33a9b94b13be4ce4b828188ff0a1f7f2.png"
[2025-10-02 23:32:44.937 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/33a9b94b13be4ce4b828188ff0a1f7f2.png"
[2025-10-02 23:32:44.972 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/cc4369556cbc4b7da80e941e9e887c58.png"
[2025-10-02 23:32:45.482 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/cc4369556cbc4b7da80e941e9e887c58.png"
[2025-10-02 23:32:45.504 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/25c42b009fcb485b9f08f1c874967d40.png"
[2025-10-02 23:32:45.886 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/25c42b009fcb485b9f08f1c874967d40.png"
[2025-10-02 23:32:45.904 +02:00] [INF] [37] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/6e60b2cf7a8049c6899d2981c219b9be.png"
[2025-10-02 23:32:46.215 +02:00] [INF] [37] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/6e60b2cf7a8049c6899d2981c219b9be.png"
[2025-10-02 23:32:46.228 +02:00] [INF] [18] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/6c7d99239448409b85aca4ddba334c54.png"
[2025-10-02 23:32:46.629 +02:00] [INF] [18] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/6c7d99239448409b85aca4ddba334c54.png"
[2025-10-02 23:32:46.645 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/5ae41ec6b60f48b49e86334f42f441b7.png"
[2025-10-02 23:32:46.956 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/5ae41ec6b60f48b49e86334f42f441b7.png"
[2025-10-02 23:32:46.968 +02:00] [INF] [17] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/874465d4e3cd40eaafefaaa1b550e33c.png"
[2025-10-02 23:32:47.255 +02:00] [INF] [30] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "TheTVDB" "********"
[2025-10-02 23:32:47.257 +02:00] [INF] [30] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:32:47.333 +02:00] [INF] [17] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/874465d4e3cd40eaafefaaa1b550e33c.png"
[2025-10-02 23:32:47.351 +02:00] [INF] [17] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/1b07087aa910441aa477c2ce67e24914.png"
[2025-10-02 23:32:47.730 +02:00] [INF] [17] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/1b07087aa910441aa477c2ce67e24914.png"
[2025-10-02 23:32:47.748 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/80e98e896cbd4d0fae8ed15338ffa828.png"
[2025-10-02 23:32:48.329 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/80e98e896cbd4d0fae8ed15338ffa828.png"
[2025-10-02 23:32:48.347 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/ba7d6e5824354b9e920b49677d0c5f01.png"
[2025-10-02 23:32:48.813 +02:00] [INF] [30] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/ba7d6e5824354b9e920b49677d0c5f01.png"
[2025-10-02 23:32:48.833 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/298f932edb1f4096b09c83e2d692df51.png"
[2025-10-02 23:32:49.188 +02:00] [INF] [17] Emby.Server.Implementations.Updates.InstallationManager: Plugin "updated": "TheTVDB" "********"
[2025-10-02 23:32:49.191 +02:00] [INF] [17] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:32:49.387 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/298f932edb1f4096b09c83e2d692df51.png"
[2025-10-02 23:32:49.399 +02:00] [INF] [17] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/3205dc66540444649abf28653b3043b7.png"
[2025-10-02 23:32:49.840 +02:00] [INF] [17] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/3205dc66540444649abf28653b3043b7.png"
[2025-10-02 23:32:49.857 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/d96708b56beb466089decfce2b71f7af.png"
[2025-10-02 23:32:50.168 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/d96708b56beb466089decfce2b71f7af.png"
[2025-10-02 23:32:50.181 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/161e1aa485fb430986ca55510f5dc6a7.png"
[2025-10-02 23:32:50.532 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/161e1aa485fb430986ca55510f5dc6a7.png"
[2025-10-02 23:32:50.549 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/6233a9401acf4418b869acd3d0d809b1.png"
[2025-10-02 23:32:51.135 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/6233a9401acf4418b869acd3d0d809b1.png"
[2025-10-02 23:32:51.166 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/8472f42b41344ccba7a412ae9becf96d.png"
[2025-10-02 23:32:51.651 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/8472f42b41344ccba7a412ae9becf96d.png"
[2025-10-02 23:32:51.667 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/ef545df5dc9c4ef699ca8385cc7f80e1.png"
[2025-10-02 23:32:52.122 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/ef545df5dc9c4ef699ca8385cc7f80e1.png"
[2025-10-02 23:32:52.132 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/be8648e7e7bb4292843fc4fbbfa0b0fd.png"
[2025-10-02 23:32:52.650 +02:00] [INF] [20] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/be8648e7e7bb4292843fc4fbbfa0b0fd.png"
[2025-10-02 23:32:57.554 +02:00] [INF] [19] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:32:57.554 +02:00] [INF] [19] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:32:59.391 +02:00] [INF] [30] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:32:59.843 +02:00] [INF] [17] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:33:01.022 +02:00] [INF] [30] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 3 minute(s) and 44 seconds
[2025-10-02 23:33:01.085 +02:00] [INF] [19] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-10-02 23:33:01.097 +02:00] [INF] [17] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-10-02 23:33:15.207 +02:00] [INF] [17] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:33:15.207 +02:00] [INF] [17] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:33:16.009 +02:00] [INF] [19] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:33:16.232 +02:00] [INF] [19] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:33:47.179 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:33:47.179 +02:00] [INF] [45] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:33:47.180 +02:00] [INF] [45] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:33:47.180 +02:00] [INF] [45] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:33:47.180 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-02 23:33:47.180 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-02 23:34:31.876 +02:00] [INF] [25] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:34:31.876 +02:00] [INF] [25] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:34:33.517 +02:00] [INF] [48] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:34:33.956 +02:00] [INF] [30] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:35:00.870 +02:00] [INF] [48] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Saving system configuration
[2025-10-02 23:35:00.870 +02:00] [INF] [48] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:35:02.865 +02:00] [INF] [48] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:35:03.100 +02:00] [INF] [30] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:35:27.867 +02:00] [INF] [48] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Webhook" "********"
[2025-10-02 23:35:27.869 +02:00] [INF] [48] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:35:31.550 +02:00] [INF] [48] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:35:36.796 +02:00] [INF] [18] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Open Subtitles" "20.0.0.0"
[2025-10-02 23:35:36.799 +02:00] [INF] [18] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:35:39.693 +02:00] [INF] [45] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:35:46.057 +02:00] [INF] [45] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Reports" "********"
[2025-10-02 23:35:46.060 +02:00] [INF] [45] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:35:47.072 +02:00] [INF] [25] Emby.Server.Implementations.Updates.InstallationManager: Plugin "updated": "Reports" "********"
[2025-10-02 23:35:47.074 +02:00] [INF] [25] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:35:48.599 +02:00] [INF] [25] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:35:49.797 +02:00] [INF] [18] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:35:59.801 +02:00] [INF] [30] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:36:06.456 +02:00] [INF] [18] Emby.Server.Implementations.Updates.InstallationManager: Plugin "installed": "Local Intros" "*******"
[2025-10-02 23:36:06.458 +02:00] [INF] [18] Emby.Server.Implementations.ApplicationHost: App needs to be restarted.
[2025-10-02 23:36:07.370 +02:00] [INF] [25] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:36:10.525 +02:00] [INF] [30] Emby.Server.Implementations.Session.SessionManager: Sending shutdown notifications
[2025-10-02 23:36:10.528 +02:00] [INF] [44] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:36:10.534 +02:00] [INF] [30] Jellyfin.Networking.PortForwardingHost: Stopping NAT discovery
[2025-10-02 23:36:10.538 +02:00] [INF] [43] Main: Running query planner optimizations in the database... This might take a while
[2025-10-02 23:36:10.558 +02:00] [INF] [44] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-10-02 23:36:10.559 +02:00] [INF] [44] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-10-02 23:36:10.565 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: Disposing "CoreAppHost"
[2025-10-02 23:36:10.565 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: Disposing "MusicBrainzArtistProvider"
[2025-10-02 23:36:10.566 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: Disposing "MusicBrainzAlbumProvider"
[2025-10-02 23:36:10.566 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: Disposing "PluginManager"
[2025-10-02 23:36:10.588 +02:00] [INF] [43] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/config/cache"
[2025-10-02 23:36:10.613 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-10-02 23:36:10.614 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.ChapterSegments, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Chapter Segments Provider_*******/Jellyfin.Plugin.ChapterSegments.dll"
[2025-10-02 23:36:10.617 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.KodiSyncQueue, Version=1*******, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Kodi Sync Queue_1*******/Jellyfin.Plugin.KodiSyncQueue.dll"
[2025-10-02 23:36:10.622 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "LiteDB, Version=********, Culture=neutral, PublicKeyToken=4ee40123013c9f27" from "/config/data/plugins/Kodi Sync Queue_1*******/LiteDB.dll"
[2025-10-02 23:36:10.622 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.LocalIntros, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Local Intros_*******/Jellyfin.Plugin.LocalIntros.dll"
[2025-10-02 23:36:10.624 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.OpenSubtitles, Version=20.0.0.0, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Open Subtitles_20.0.0.0/Jellyfin.Plugin.OpenSubtitles.dll"
[2025-10-02 23:36:10.626 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "SQLitePCL.pretty, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Playback Reporting_********/SQLitePCL.pretty.dll"
[2025-10-02 23:36:10.626 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.PlaybackReporting, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Playback Reporting_********/Jellyfin.Plugin.PlaybackReporting.dll"
[2025-10-02 23:36:10.647 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "ClosedXML, Version=0.97.0.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b" from "/config/data/plugins/Reports_********/ClosedXML.dll"
[2025-10-02 23:36:10.734 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17" from "/config/data/plugins/Reports_********/DocumentFormat.OpenXml.dll"
[2025-10-02 23:36:10.735 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "ExcelNumberFormat, Version=1.1.0.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca" from "/config/data/plugins/Reports_********/ExcelNumberFormat.dll"
[2025-10-02 23:36:10.736 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "System.IO.Packaging, Version=4.0.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" from "/config/data/plugins/Reports_********/System.IO.Packaging.dll"
[2025-10-02 23:36:10.741 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "SixLabors.Fonts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13" from "/config/data/plugins/Reports_********/SixLabors.Fonts.dll"
[2025-10-02 23:36:10.742 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Reports, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Reports_********/Jellyfin.Plugin.Reports.dll"
[2025-10-02 23:36:10.742 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.SubtitleExtract, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Subtitle Extract_*******/Jellyfin.Plugin.SubtitleExtract.dll"
[2025-10-02 23:36:10.746 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-10-02 23:36:10.752 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=4.7.10.0, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-10-02 23:36:10.756 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Trakt_********/Trakt.dll"
[2025-10-02 23:36:10.783 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938" from "/config/data/plugins/Webhook_********/BouncyCastle.Cryptography.dll"
[2025-10-02 23:36:10.785 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Webhook, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/data/plugins/Webhook_********/Jellyfin.Plugin.Webhook.dll"
[2025-10-02 23:36:10.797 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MailKit, Version=4.8.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b" from "/config/data/plugins/Webhook_********/MailKit.dll"
[2025-10-02 23:36:10.806 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MimeKit, Version=4.8.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814" from "/config/data/plugins/Webhook_********/MimeKit.dll"
[2025-10-02 23:36:10.810 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MQTTnet, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63" from "/config/data/plugins/Webhook_********/MQTTnet.dll"
[2025-10-02 23:36:10.816 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Handlebars, Version=2.1.6.0, Culture=neutral, PublicKeyToken=22225d0bf33cd661" from "/config/data/plugins/Webhook_********/Handlebars.dll"
[2025-10-02 23:36:10.817 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MQTTnet.Extensions.ManagedClient, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63" from "/config/data/plugins/Webhook_********/MQTTnet.Extensions.ManagedClient.dll"
[2025-10-02 23:36:10.830 +02:00] [INF] [43] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-10-02 23:36:10.830 +02:00] [INF] [43] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-10-02 23:36:10.830 +02:00] [INF] [43] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-10-02 23:36:10.830 +02:00] [INF] [43] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "***********"]
[2025-10-02 23:36:10.830 +02:00] [INF] [43] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-10-02 23:36:10.830 +02:00] [INF] [43] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-10-02 23:36:10.830 +02:00] [INF] [43] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-10-02 23:36:11.195 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Chapter Segments Provider" "*******"
[2025-10-02 23:36:11.196 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.KodiSyncQueuePlugin: KodiSyncQueue is starting...
[2025-10-02 23:36:11.196 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.Data.DbRepo: Creating DB Repository...
[2025-10-02 23:36:11.269 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.Data.DbRepo: Upgraded DB to v1
[2025-10-02 23:36:11.270 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Kodi Sync Queue" "1*******"
[2025-10-02 23:36:11.270 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Local Intros" "*******"
[2025-10-02 23:36:11.278 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Open Subtitles" "20.0.0.0"
[2025-10-02 23:36:11.278 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Playback Reporting" "********"
[2025-10-02 23:36:11.279 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Reports" "********"
[2025-10-02 23:36:11.280 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Subtitle Extract" "*******"
[2025-10-02 23:36:11.280 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-10-02 23:36:11.281 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-10-02 23:36:11.282 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Webhook" "********"
[2025-10-02 23:36:11.282 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-10-02 23:36:11.282 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-10-02 23:36:11.282 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-10-02 23:36:11.284 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-10-02 23:36:11.284 +02:00] [INF] [43] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-10-02 23:36:11.332 +02:00] [INF] [43] Main: Kestrel is listening on "0.0.0.0"
[2025-10-02 23:36:11.335 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: EventMonitorEntryPoint Running
[2025-10-02 23:36:11.345 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Sqlite version: 3.41.2
[2025-10-02 23:36:11.346 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Sqlite compiler options: ATOMIC_INTRINSICS=1,COMPILER=gcc-9.4.0,DEFAULT_AUTOVACUUM,DEFAULT_CACHE_SIZE=-2000,DEFAULT_FILE_FORMAT=4,DEFAULT_FOREIGN_KEYS,DEFAULT_JOURNAL_SIZE_LIMIT=-1,DEFAULT_MMAP_SIZE=0,DEFAULT_PAGE_SIZE=4096,DEFAULT_PCACHE_INITSZ=20,DEFAULT_RECURSIVE_TRIGGERS,DEFAULT_SECTOR_SIZE=4096,DEFAULT_SYNCHRONOUS=2,DEFAULT_WAL_AUTOCHECKPOINT=1000,DEFAULT_WAL_SYNCHRONOUS=2,DEFAULT_WORKER_THREADS=0,ENABLE_COLUMN_METADATA,ENABLE_FTS3,ENABLE_FTS3_PARENTHESIS,ENABLE_FTS4,ENABLE_FTS5,ENABLE_MATH_FUNCTIONS,ENABLE_RTREE,ENABLE_SNAPSHOT,MALLOC_SOFT_LIMIT=1024,MAX_ATTACHED=10,MAX_COLUMN=2000,MAX_COMPOUND_SELECT=500,MAX_DEFAULT_PAGE_SIZE=8192,MAX_EXPR_DEPTH=1000,MAX_FUNCTION_ARG=127,MAX_LENGTH=1000000000,MAX_LIKE_PATTERN_LENGTH=50000,MAX_MMAP_SIZE=0x7fff0000,MAX_PAGE_COUNT=1073741823,MAX_PAGE_SIZE=65536,MAX_SQL_LENGTH=1000000000,MAX_TRIGGER_DEPTH=1000,MAX_VARIABLE_NUMBER=32766,MAX_VDBE_OP=250000000,MAX_WORKER_THREADS=8,MUTEX_PTHREADS,SYSTEM_MALLOC,TEMP_STORE=1,THREADSAFE=1
[2025-10-02 23:36:11.354 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Default journal_mode for "/config/data/data/playback_reporting.db" is "delete"
[2025-10-02 23:36:11.355 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Initialize PlaybackActivity Repository
[2025-10-02 23:36:11.355 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: PlaybackActivity table schema miss match!
[2025-10-02 23:36:11.355 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Expected : "datecreated:datetime|userid:text|itemid:text|itemtype:text|itemname:text|playbackmethod:text|clientname:text|devicename:text|playduration:int"
[2025-10-02 23:36:11.355 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Received : ""
[2025-10-02 23:36:11.355 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Dropping and recreating PlaybackActivity table
[2025-10-02 23:36:11.393 +02:00] [INF] [43] Jellyfin.Networking.PortForwardingHost: Starting NAT discovery
[2025-10-02 23:36:11.564 +02:00] [WRN] [43] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/run/s6-rc:s6-rc-init:mJJADC/servicedirs/svc-jellyfin/wwwroot". Static files may be unavailable.
[2025-10-02 23:36:11.571 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-10-02 23:36:11.571 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.ScheduledTasks.RetentionTask: Retention task scheduled
[2025-10-02 23:36:11.573 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.TaskCleanDb: TaskCleanDb Loaded
[2025-10-02 23:36:11.573 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.TaskRunBackup: TaskRunBackup Loaded
[2025-10-02 23:36:11.580 +02:00] [INF] [43] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Remove Old Sync Data" set to fire at 2025-10-03 00:01:00.000 +02:00, which is 00:24:48.4199629 from now.
[2025-10-02 23:36:11.580 +02:00] [INF] [43] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Playback Reporting Trim Db" set to fire at 2025-10-03 00:00:00.000 +02:00, which is 00:23:48.4196485 from now.
[2025-10-02 23:36:11.582 +02:00] [INF] [43] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-10-03 03:00:00.000 +02:00, which is 03:23:48.4179735 from now.
[2025-10-02 23:36:11.582 +02:00] [INF] [43] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-10-03 02:00:00.000 +02:00, which is 02:23:48.4178281 from now.
[2025-10-02 23:36:11.587 +02:00] [INF] [45] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-10-02 23:36:11.600 +02:00] [INF] [44] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-10-02 23:36:11.610 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.1.2"
[2025-10-02 23:36:11.632 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-10-02 23:36:11.647 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-10-02 23:36:11.662 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-10-02 23:36:11.763 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-10-02 23:36:14.589 +02:00] [INF] [44] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-10-02 23:36:14.639 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-10-02 23:36:15.081 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-10-02 23:36:15.082 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: ServerId: "bb54c7f5cdc84394bff8ee74ed400408"
[2025-10-02 23:36:15.082 +02:00] [INF] [43] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-10-02 23:36:15.082 +02:00] [INF] [43] Main: Startup complete 0:00:04.5159794
[2025-10-02 23:36:17.632 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 2 seconds
[2025-10-02 23:36:30.250 +02:00] [INF] [44] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:36:30.348 +02:00] [INF] [18] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:36:30.657 +02:00] [INF] [25] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:36:37.013 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Api.PlaybackReportingActivityController: PlaybackReportingActivityController Loaded
[2025-10-02 23:36:37.018 +02:00] [INF] [43] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Default journal_mode for "/config/data/data/playback_reporting.db" is "delete"
[2025-10-02 23:36:43.865 +02:00] [INF] [46] Trakt.Api.TraktController: TraktDeviceAuthorization request received
[2025-10-02 23:36:43.865 +02:00] [WRN] [46] Trakt.Api.TraktController: No associated trakt.tv user found - creating one.
[2025-10-02 23:36:44.633 +02:00] [INF] [18] Trakt.Api.TraktController: TraktDeviceAuthorization request received
[2025-10-02 23:36:46.651 +02:00] [INF] [17] Trakt.Api.TraktController: TraktDeviceAuthorization request received
[2025-10-02 23:36:46.809 +02:00] [INF] [18] Trakt.Api.TraktController: TraktDeviceAuthorization request received
[2025-10-02 23:36:47.622 +02:00] [INF] [17] Trakt.Api.TraktApi: Polling for access token every 5s. Expires at 10/02/2025 21:46:47 UTC.
[2025-10-02 23:36:47.626 +02:00] [INF] [18] Trakt.Api.TraktController: TraktPollAuthorizationStatus request received
[2025-10-02 23:36:50.650 +02:00] [INF] [17] Trakt.Api.TraktApi: Polling for access token every 5s. Expires at 10/02/2025 21:46:50 UTC.
[2025-10-02 23:36:53.024 +02:00] [INF] [21] Trakt.Api.TraktApi: Polling for access token every 5s. Expires at 10/02/2025 21:46:53 UTC.
[2025-10-02 23:36:54.503 +02:00] [INF] [32] Trakt.Api.TraktApi: Polling for access token every 5s. Expires at 10/02/2025 21:46:54 UTC.
[2025-10-02 23:36:57.729 +02:00] [INF] [46] Trakt.Api.TraktApi: Device successfully authorized
[2025-10-02 23:37:09.897 +02:00] [INF] [44] Trakt.Api.TraktController: TraktPollAuthorizationStatus request received
[2025-10-02 23:37:09.901 +02:00] [INF] [46] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:37:10.284 +02:00] [INF] [19] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:37:11.702 +02:00] [INF] [19] Trakt.Api.TraktApi: Device successfully authorized
[2025-10-02 23:37:15.547 +02:00] [INF] [46] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:38:04.687 +02:00] [INF] [46] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:38:05.148 +02:00] [INF] [17] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:38:09.720 +02:00] [INF] [32] Trakt.ScheduledTasks.SyncFromTraktTask: Trakt.tv watched movies for user "Tankeeee2_GAMES": 52
[2025-10-02 23:38:09.720 +02:00] [INF] [32] Trakt.ScheduledTasks.SyncFromTraktTask: Trakt.tv watched movies history for user "Tankeeee2_GAMES": 75
[2025-10-02 23:38:09.720 +02:00] [INF] [32] Trakt.ScheduledTasks.SyncFromTraktTask: Trakt.tv paused movies for user "Tankeeee2_GAMES": 24
[2025-10-02 23:38:09.720 +02:00] [INF] [32] Trakt.ScheduledTasks.SyncFromTraktTask: Trakt.tv watched shows for user "Tankeeee2_GAMES": 21
[2025-10-02 23:38:09.720 +02:00] [INF] [32] Trakt.ScheduledTasks.SyncFromTraktTask: Trakt.tv watched episodes history for user "Tankeeee2_GAMES": 1781
[2025-10-02 23:38:09.720 +02:00] [INF] [32] Trakt.ScheduledTasks.SyncFromTraktTask: Trakt.tv paused episodes for user "Tankeeee2_GAMES": 128
[2025-10-02 23:38:10.366 +02:00] [INF] [32] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Import watched states and playback progress from trakt.tv" Completed after 0 minute(s) and 42 seconds
[2025-10-02 23:38:10.531 +02:00] [INF] [19] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:38:10.856 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:11.020 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 273 updates
[2025-10-02 23:38:11.020 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.1642713"
[2025-10-02 23:38:11.047 +02:00] [INF] [21] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:38:11.896 +02:00] [INF] [42] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:38:12.135 +02:00] [INF] [46] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:38:13.750 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:13.794 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:13.794 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0440045"
[2025-10-02 23:38:14.386 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:14.393 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:14.393 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0070523"
[2025-10-02 23:38:15.399 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:15.407 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:15.407 +02:00] [INF] [19] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0077442"
[2025-10-02 23:38:16.185 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:16.191 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:16.192 +02:00] [INF] [43] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0065540"
[2025-10-02 23:38:17.823 +02:00] [INF] [42] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:17.832 +02:00] [INF] [42] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:17.832 +02:00] [INF] [42] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0086918"
[2025-10-02 23:38:19.109 +02:00] [INF] [42] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:19.114 +02:00] [INF] [42] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:19.114 +02:00] [INF] [42] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0051277"
[2025-10-02 23:38:19.916 +02:00] [INF] [43] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:38:20.169 +02:00] [INF] [46] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:38:22.468 +02:00] [INF] [25] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:22.471 +02:00] [INF] [25] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:22.471 +02:00] [INF] [25] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0037741"
[2025-10-02 23:38:23.023 +02:00] [INF] [25] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:23.029 +02:00] [INF] [25] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:23.029 +02:00] [INF] [25] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0059488"
[2025-10-02 23:38:24.856 +02:00] [INF] [46] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Started user data sync
[2025-10-02 23:38:24.860 +02:00] [INF] [46] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: User Data Sync: User "Tankeeee2_GAMES" ("24f5447d458549f7b7b4460038974b9d") posted 2 updates
[2025-10-02 23:38:24.860 +02:00] [INF] [46] Jellyfin.Plugin.KodiSyncQueue.EntryPoints.UserSyncNotification: Finished user data sync, taking "00:00:00.0037693"
[2025-10-02 23:38:26.435 +02:00] [INF] [43] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:38:26.667 +02:00] [INF] [46] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:38:29.399 +02:00] [INF] [19] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:38:32.010 +02:00] [INF] [25] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" closed
[2025-10-02 23:38:36.909 +02:00] [INF] [41] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "***********" request
[2025-10-02 23:46:47.687 +02:00] [ERR] [31] Trakt.Api.TraktApi: Expired - the tokens have expired, restart the process
