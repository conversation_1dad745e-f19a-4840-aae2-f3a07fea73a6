[2025-10-04 00:00:00.001 +02:00] [INF] [26] Jellyfin.Plugin.PlaybackReporting.TaskCleanDb: Playback Reporting Data Trim
[2025-10-04 00:00:00.001 +02:00] [INF] [26] Jellyfin.Plugin.PlaybackReporting.TaskCleanDb: MaxDataAge : 3
[2025-10-04 00:00:00.002 +02:00] [INF] [26] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: DeleteOldData : "delete from PlaybackActivity where DateCreated < '2025-07-04 00:00:00.0018039'"
[2025-10-04 00:00:00.002 +02:00] [INF] [26] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Default journal_mode for "/config/data/data/playback_reporting.db" is "delete"
[2025-10-04 00:00:00.003 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Playback Reporting Trim Db" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:00:00.999 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Playback Reporting Trim Db" set to fire at 2025-10-05 00:00:00.000 +02:00, which is 23:59:59.0004878 from now.
[2025-10-04 00:00:45.253 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:00:45.284 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:01:00.000 +02:00] [INF] [20] Jellyfin.Plugin.KodiSyncQueue.ScheduledTasks.RetentionTask: Retention deletion not possible if retention days is set to zero!
[2025-10-04 00:01:00.000 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Remove Old Sync Data" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:01:01.000 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Remove Old Sync Data" set to fire at 2025-10-05 00:01:00.000 +02:00, which is 23:59:59.0000283 from now.
[2025-10-04 00:01:50.848 +02:00] [INF] [29] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" request
[2025-10-04 00:02:16.253 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:02:16.284 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:03:38.848 +02:00] [INF] [26] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-10-04 00:03:47.253 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:03:47.285 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:04:26.849 +02:00] [INF] [14] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-10-04 00:05:14.849 +02:00] [INF] [14] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-10-04 00:05:18.252 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:05:18.284 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:06:02.848 +02:00] [INF] [26] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-10-04 00:06:14.849 +02:00] [INF] [26] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-10-04 00:06:26.849 +02:00] [INF] [26] Emby.Server.Implementations.Session.SessionWebSocketListener: Lost 1 WebSockets.
[2025-10-04 00:06:49.253 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:06:49.285 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:07:50.143 +02:00] [WRN] [14] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "10.89.0.6" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-10-04 00:07:50.147 +02:00] [INF] [14] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" closed
[2025-10-04 00:08:20.253 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:08:20.286 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:09:51.252 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:09:51.285 +02:00] [INF] [27] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:11:22.253 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:11:22.285 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:12:53.254 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:12:53.286 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:14:24.254 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:14:24.286 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:15:55.253 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:15:55.287 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:17:26.254 +02:00] [INF] [27] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:17:26.287 +02:00] [INF] [27] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:18:57.254 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:18:57.287 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:20:28.254 +02:00] [INF] [27] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:20:28.287 +02:00] [INF] [27] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:21:59.254 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:21:59.288 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:23:30.254 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:23:30.288 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:25:01.254 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:25:01.287 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:25:59.376 +02:00] [INF] [25] Emby.Server.Implementations.IO.LibraryMonitor: "PELIS" ("/CONTENIDO/PELIS") will be refreshed.
[2025-10-04 00:26:32.256 +02:00] [INF] [34] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:26:32.288 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:28:03.255 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:28:03.288 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:29:34.257 +02:00] [INF] [30] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:29:34.288 +02:00] [INF] [33] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:30:44.458 +02:00] [INF] [21] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" request
[2025-10-04 00:31:05.257 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:31:05.289 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:32:08.464 +02:00] [INF] [15] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-10-04 00:32:20.464 +02:00] [INF] [15] Emby.Server.Implementations.Session.SessionWebSocketListener: Lost 1 WebSockets.
[2025-10-04 00:32:36.257 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:32:36.290 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:34:07.258 +02:00] [INF] [32] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:34:07.289 +02:00] [INF] [32] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:35:38.259 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:35:38.290 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:37:09.260 +02:00] [INF] [30] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:37:09.289 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:38:40.260 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:38:40.290 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:40:11.261 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:40:11.290 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:41:42.262 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:41:42.290 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:43:13.261 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:43:13.290 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:44:44.261 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:44:44.290 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:46:20.260 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:46:20.289 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:47:51.261 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:47:51.289 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:49:22.262 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:49:22.290 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:50:53.261 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:50:53.289 +02:00] [INF] [27] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:51:03.935 +02:00] [WRN] [25] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "10.89.0.6" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-10-04 00:51:03.939 +02:00] [INF] [25] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" closed
[2025-10-04 00:52:24.262 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:52:24.290 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:53:55.262 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:53:55.290 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:55:26.262 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:55:26.290 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:56:57.263 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:56:57.291 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:58:28.263 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:58:28.291 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:59:59.263 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 00:59:59.290 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:01:34.714 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:01:34.740 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:03:05.716 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:03:05.741 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:04:36.716 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:04:36.741 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:06:07.717 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:06:07.740 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:07:38.718 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:07:38.741 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:09:09.718 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:09:09.742 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:10:40.718 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:10:40.742 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:12:11.718 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:12:11.742 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:13:42.718 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:13:42.742 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:15:13.718 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:15:13.742 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:16:20.206 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:20.207 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:20.207 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:16:20.206 +02:00] [INF] [15] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:20.207 +02:00] [INF] [15] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:20.207 +02:00] [INF] [15] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:16:25.172 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:25.173 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:25.173 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:16:30.183 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:30.183 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:30.184 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:16:35.195 +02:00] [INF] [15] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:35.195 +02:00] [INF] [15] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:16:35.195 +02:00] [INF] [15] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:16:48.904 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:16:48.927 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:17:43.716 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:17:43.716 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:17:43.717 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:17:43.717 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:17:43.717 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:17:43.717 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:18:19.905 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:18:19.929 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:19:11.709 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:19:11.709 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:19:11.711 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:19:11.711 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 01:19:11.711 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:19:11.712 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 01:19:50.906 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:19:50.928 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:21:21.905 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:21:21.928 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:22:52.905 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:22:52.928 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:24:23.906 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:24:23.929 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:25:54.906 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:25:54.930 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:27:25.906 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:27:25.930 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:28:56.906 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:28:56.930 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:30:27.907 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:30:27.930 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:31:58.908 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:31:58.931 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:33:29.907 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:33:29.930 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:35:00.908 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:35:00.931 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:36:31.908 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:36:31.932 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:38:02.909 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:38:02.933 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:39:33.909 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:39:33.933 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:41:04.910 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:41:04.933 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:42:35.912 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:42:35.933 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:44:06.913 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:44:06.934 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:45:37.913 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:45:37.935 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:47:08.913 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:47:08.936 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:48:39.914 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:48:39.936 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:50:10.915 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:50:10.937 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:51:41.915 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:51:41.938 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:53:12.915 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:53:12.939 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:54:43.915 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:54:43.940 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:56:14.915 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:56:14.940 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:57:45.915 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:57:45.940 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:59:16.914 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 01:59:16.940 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:00:13.665 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.668 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Tímido ante la cámara" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.675 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Stopping chapter extraction for "Tocado por un ángel" because a chapter was found with a position greater than the runtime.
[2025-10-04 02:00:13.678 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Los miércoles son tristes" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.679 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "La tristeza es solitaria" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.679 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Tristemente amiga o enemiga" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.680 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Qué noche la de ese día" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.680 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Quien siembra vientos recoge tristeza" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.680 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Hoy por mí, mañana por mí" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.681 +02:00] [INF] [21] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Qué triste que todavía no me conozcas" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-10-04 02:00:13.716 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:00:14.635 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-10-05 02:00:00.000 +02:00, which is 23:59:45.3643239 from now.
[2025-10-04 02:00:47.914 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:00:47.940 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:02:18.915 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:02:18.940 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:03:49.916 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:03:49.941 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:05:20.917 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:05:20.941 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:06:51.917 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:06:51.942 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:08:22.917 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:08:22.942 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:09:53.917 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:09:53.941 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:11:24.917 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:11:24.942 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:12:55.918 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:12:55.942 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:14:26.918 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:14:26.941 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:15:57.918 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:15:57.942 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:17:28.917 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:17:28.941 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:18:59.918 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:18:59.942 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:20:30.918 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:20:30.942 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:22:01.918 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:22:01.942 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:23:32.919 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:23:32.943 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:25:03.921 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:25:03.943 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:26:34.921 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:26:34.944 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:28:05.922 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:28:05.944 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:29:36.922 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:29:36.945 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:31:07.922 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:31:07.946 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:32:38.924 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:32:38.948 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:34:09.926 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:34:09.949 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:35:40.925 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:35:40.948 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:37:11.927 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:37:11.950 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:38:42.927 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:38:42.950 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:40:13.927 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:40:13.952 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:41:44.929 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:41:44.953 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:43:15.930 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:43:15.955 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:44:46.930 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:44:46.955 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:46:17.930 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:46:17.955 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:47:48.930 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:47:48.956 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:49:19.931 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:49:19.956 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:50:50.932 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:50:50.956 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:52:21.932 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:52:21.956 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:53:52.933 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:53:52.957 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:55:23.933 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:55:23.958 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:56:54.932 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:56:54.959 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:58:25.933 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:58:25.958 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:59:56.934 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 02:59:56.959 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:00:13.664 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:00:14.636 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-10-05 03:00:00.000 +02:00, which is 23:59:45.3638404 from now.
[2025-10-04 03:01:27.934 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:01:27.958 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:02:58.935 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:02:58.958 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:04:29.936 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:04:29.959 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:06:00.936 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:06:00.959 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:07:31.936 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:07:31.959 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:09:02.936 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:09:02.960 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:10:33.936 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:10:33.960 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:12:04.937 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:12:04.960 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:13:35.938 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:13:35.961 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:15:06.938 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:15:06.961 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:16:37.938 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:16:37.961 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:18:08.937 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:18:08.960 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:19:39.938 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:19:39.961 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:21:10.938 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:21:10.962 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:22:41.938 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:22:41.961 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:24:12.939 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:24:12.962 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:25:43.939 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:25:43.962 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:27:14.939 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:27:14.962 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:28:45.939 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:28:45.962 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:30:16.940 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:30:16.963 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:31:47.940 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:31:47.963 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:33:18.940 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:33:18.964 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:34:49.940 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:34:49.965 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:36:20.939 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:36:20.964 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:37:51.940 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:37:51.965 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:39:22.940 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:39:22.966 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:40:53.940 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:40:53.966 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:42:24.939 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:42:24.965 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:43:55.940 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:43:55.966 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:45:26.940 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:45:26.967 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:46:57.940 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:46:57.966 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:48:28.941 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:48:28.967 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:49:59.941 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:49:59.967 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:51:30.942 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:51:30.968 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:53:01.942 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:53:01.968 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:54:32.942 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:54:32.969 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:56:03.944 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:56:03.969 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:57:34.944 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:57:34.969 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:59:05.944 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 03:59:05.970 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:00:36.944 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:00:36.970 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:02:07.945 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:02:07.971 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:03:38.945 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:03:38.971 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:05:09.947 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:05:09.972 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:06:40.946 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:06:40.971 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:08:11.947 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:08:11.972 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:09:42.947 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:09:42.972 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:11:13.947 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:11:13.973 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:12:44.947 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:12:44.973 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:14:15.948 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:14:15.973 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:15:46.949 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:15:46.973 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:17:17.950 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:17:17.973 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:18:48.950 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:18:48.973 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:20:19.950 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:20:19.972 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:21:50.950 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:21:50.973 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:23:21.950 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:23:21.974 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:24:52.950 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:24:52.974 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:26:23.950 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:26:23.974 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:27:54.950 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:27:54.973 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:29:25.950 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:29:25.973 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:30:56.951 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:30:56.974 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:32:27.951 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:32:27.973 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:33:58.951 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:33:58.974 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:35:29.951 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:35:29.974 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:37:00.951 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:37:00.976 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:38:31.951 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:38:31.977 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:40:02.951 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:40:02.978 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:41:33.951 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:41:33.978 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:43:04.950 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:43:04.978 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:44:35.951 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:44:35.977 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:46:06.952 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:46:06.977 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:47:37.954 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:47:37.978 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:49:08.953 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:49:08.978 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:50:39.955 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:50:39.978 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:52:10.955 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:52:10.977 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:53:41.956 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:53:41.979 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:55:12.957 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:55:12.980 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:56:43.957 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:56:43.981 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:58:14.958 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:58:14.981 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:59:45.958 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 04:59:45.981 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:01:16.958 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:01:16.981 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:02:47.959 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:02:47.982 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:04:18.961 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:04:18.984 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:05:49.961 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:05:49.986 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:07:20.962 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:07:20.987 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:08:51.962 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:08:51.987 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:10:22.963 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:10:22.988 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:11:53.963 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:11:53.989 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:13:24.963 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:13:24.989 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:14:55.964 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:14:55.990 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:16:26.964 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:16:26.989 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:17:57.964 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:17:57.990 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:19:28.965 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:19:28.990 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:20:59.965 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:20:59.989 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:22:30.966 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:22:30.990 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:24:01.967 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:24:01.990 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:25:32.967 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:25:32.991 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:27:03.966 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:27:03.990 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:28:34.967 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:28:34.990 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:30:05.967 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:30:05.990 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:31:36.968 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:31:36.991 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:33:07.969 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:33:07.992 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:34:38.969 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:34:38.992 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:36:09.969 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:36:09.992 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:37:40.969 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:37:40.992 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:39:11.969 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:39:11.993 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:40:42.970 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:40:42.994 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:42:13.970 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:42:13.993 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:43:44.970 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:43:44.993 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:45:15.971 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:45:15.994 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:46:46.971 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:46:46.994 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:48:17.971 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:48:17.995 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:49:48.970 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:49:48.996 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:51:19.971 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:51:19.996 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:52:50.970 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:52:50.996 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:54:21.971 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:54:21.996 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:55:52.971 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:55:52.997 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:57:23.971 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:57:23.998 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:58:54.971 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 05:58:54.997 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:00:25.972 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:00:25.998 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:01:56.972 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:01:56.999 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:03:27.972 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:03:27.999 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:04:58.972 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:04:58.998 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:06:29.972 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:06:29.998 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:08:00.973 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:08:00.999 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:09:31.972 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:09:31.999 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:11:02.973 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:11:02.999 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:12:33.974 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:12:34.000 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:14:04.973 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:14:05.000 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:15:35.974 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:15:36.000 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:17:06.974 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:17:06.999 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:18:37.975 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:18:37.999 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:20:08.975 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:20:08.999 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:21:39.976 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:21:40.000 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:23:10.976 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:23:11.001 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:24:41.977 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:24:42.000 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:26:12.978 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:26:13.001 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:27:43.978 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:27:44.000 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:29:14.979 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:29:15.000 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:30:45.980 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:30:46.002 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:32:16.981 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:32:17.003 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:33:47.981 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:33:48.004 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:35:18.981 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:35:19.004 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:36:49.981 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:36:50.005 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:38:20.981 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:38:21.006 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:39:51.981 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:39:52.006 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:41:22.981 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:41:23.005 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:42:53.981 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:42:54.005 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:44:24.980 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:44:25.005 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:45:55.981 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:45:56.006 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:47:26.981 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:47:27.006 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:48:57.981 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:48:58.005 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:50:28.982 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:50:29.006 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:51:59.981 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:52:00.005 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:53:30.982 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:53:31.005 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:55:01.983 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:55:02.006 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:56:32.982 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:56:33.006 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:58:03.984 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:58:04.007 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:59:34.985 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 06:59:35.008 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:01:05.986 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:01:06.008 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:02:36.986 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:02:37.010 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:04:07.987 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:04:08.012 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:05:38.987 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:05:39.011 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:07:09.988 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:07:10.012 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:07:45.818 +02:00] [INF] [22] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-10-04 07:07:45.818 +02:00] [INF] [22] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-10-04 07:07:45.819 +02:00] [INF] [22] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-10-04 07:07:45.875 +02:00] [WRN] [19] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/data/playlists" is inaccessible or empty, skipping
[2025-10-04 07:07:45.911 +02:00] [WRN] [21] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/data/playlists" is inaccessible or empty, skipping
[2025-10-04 07:07:46.377 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:07:54.627 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 8 seconds
[2025-10-04 07:07:54.668 +02:00] [INF] [15] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-10-04 07:07:54.679 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-10-04 07:08:40.989 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:08:41.030 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:10:11.990 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:10:12.029 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:11:42.990 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:11:43.029 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:13:13.991 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:13:14.031 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:14:44.992 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:14:45.032 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:16:15.992 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:16:16.035 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:17:46.992 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:17:47.035 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:19:17.993 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:19:18.036 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:20:48.993 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:20:49.036 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:22:19.993 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:22:20.037 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:23:50.993 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:23:51.037 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:25:21.993 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:25:22.037 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:26:52.992 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:26:53.037 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:28:23.994 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:28:24.037 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:29:54.994 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:29:55.038 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:31:25.994 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:31:26.039 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:32:56.994 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:32:57.040 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:34:27.994 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:34:28.040 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:35:58.995 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:35:59.041 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:37:29.996 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:37:30.041 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:39:00.996 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:39:01.042 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:40:31.997 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:40:32.042 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:42:02.997 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:42:03.042 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:43:33.997 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:43:34.043 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:45:04.997 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:45:05.043 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:46:35.999 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:46:36.044 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:48:06.999 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:48:07.044 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:49:38.000 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:49:38.044 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:51:08.999 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:51:09.045 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:52:40.000 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:52:40.044 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:54:11.002 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:54:11.045 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:55:42.003 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:55:42.045 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:57:13.003 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:57:13.044 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:58:44.004 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 07:58:44.045 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:00:15.004 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:00:15.045 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:01:46.003 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:01:46.046 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:03:17.003 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:03:17.046 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:04:48.004 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:04:48.045 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:06:19.004 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:06:19.046 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:07:50.004 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:07:50.046 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:09:21.005 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:09:21.045 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:10:52.006 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:10:52.045 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:12:23.006 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:12:23.046 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:13:54.006 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:13:54.045 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:15:25.006 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:15:25.046 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:16:56.006 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:16:56.047 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:18:27.006 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:18:27.046 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:19:58.007 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:19:58.047 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:21:29.008 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:21:29.047 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:23:00.008 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:23:00.046 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:24:31.008 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:24:31.047 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:26:02.008 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:26:02.048 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:27:33.008 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:27:33.047 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:29:04.010 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:29:04.048 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:30:35.010 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:30:35.049 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:32:06.011 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:32:06.049 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:33:37.011 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:33:37.050 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:35:08.012 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:35:08.050 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:36:39.013 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:36:39.050 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:38:10.014 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:38:10.050 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:39:41.013 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:39:41.050 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:41:12.015 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:41:12.051 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:42:43.015 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:42:43.052 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:44:14.015 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:44:14.052 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:45:45.016 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:45:45.053 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:47:16.016 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:47:16.054 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:48:47.017 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:48:47.054 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:50:18.017 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:50:18.055 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:51:49.018 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:51:49.055 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:53:20.018 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:53:20.055 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:54:51.019 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:54:51.055 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:56:22.019 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:56:22.056 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:57:53.019 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:57:53.057 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:59:24.019 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 08:59:24.057 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:00:55.020 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:00:55.058 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:02:26.020 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:02:26.058 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:03:57.021 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:03:57.058 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:05:28.021 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:05:28.059 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:06:59.021 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:06:59.059 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:08:30.022 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:08:30.059 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:10:01.021 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:10:01.059 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:11:32.022 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:11:32.058 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:13:03.023 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:13:03.058 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:14:34.025 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:14:34.059 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:16:05.025 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:16:05.059 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:17:36.026 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:17:36.059 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:19:07.026 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:19:07.060 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:20:38.026 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:20:38.061 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:22:09.025 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:22:09.062 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:23:40.026 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:23:40.062 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:25:11.027 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:25:11.062 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:26:42.027 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:26:42.063 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:28:13.027 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:28:13.064 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:29:44.027 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:29:44.064 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:31:15.028 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:31:15.065 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:32:46.028 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:32:46.065 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:34:17.028 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:34:17.066 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:35:48.028 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:35:48.066 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:37:19.028 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:37:19.065 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:38:50.029 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:38:50.065 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:40:21.030 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:40:21.066 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:41:52.029 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:41:52.066 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:43:23.030 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:43:23.067 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:44:54.030 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:44:54.068 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:46:25.031 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:46:25.068 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:47:56.032 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:47:56.068 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:49:27.032 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:49:27.068 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:50:58.032 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:50:58.067 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:52:29.033 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:52:29.068 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:54:00.033 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:54:00.067 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:55:31.034 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:55:31.067 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:57:02.033 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:57:02.068 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:58:33.034 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 09:58:33.068 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:00:04.035 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:00:04.069 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:01:35.035 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:01:35.070 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:03:06.036 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:03:06.069 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:04:37.037 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:04:37.070 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:06:08.038 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:06:08.070 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:07:39.039 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:07:39.071 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:09:10.039 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:09:10.070 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:10:41.040 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:10:41.072 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:12:12.040 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:12:12.073 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:13:43.040 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:13:43.072 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:15:14.040 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:15:14.073 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:16:45.039 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:16:45.073 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:18:16.041 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:18:16.074 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:19:47.041 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:19:47.075 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:21:18.042 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:21:18.075 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:22:49.042 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:22:49.076 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:24:20.042 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:24:20.076 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:25:03.261 +02:00] [INF] [11] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-10-04 10:25:03.261 +02:00] [INF] [11] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 1/0
[2025-10-04 10:25:03.261 +02:00] [INF] [11] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 24f5447d-4585-49f7-b7b4-460038974b9d
[2025-10-04 10:25:03.288 +02:00] [INF] [19] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" request
[2025-10-04 10:25:25.898 +02:00] [INF] [22] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" closed
[2025-10-04 10:25:51.042 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:25:51.076 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:26:18.553 +02:00] [INF] [15] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" request
[2025-10-04 10:26:57.205 +02:00] [INF] [14] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" closed
[2025-10-04 10:27:01.261 +02:00] [INF] [26] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" request
[2025-10-04 10:27:22.043 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:27:22.077 +02:00] [INF] [27] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:28:53.043 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:28:53.080 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:30:24.043 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:30:24.080 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:31:55.043 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:31:55.080 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:33:26.043 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:33:26.080 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:34:57.042 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:34:57.080 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:36:28.043 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:36:28.081 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:37:59.043 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:37:59.082 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:39:30.043 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:39:30.083 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:41:01.044 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:41:01.084 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:42:32.044 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:42:32.084 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:44:03.044 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:44:03.084 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:45:34.045 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:45:34.084 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:47:05.045 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:47:05.084 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:48:36.046 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:48:36.085 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:50:07.045 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:50:07.085 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:51:38.045 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:51:38.086 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:53:09.046 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:53:09.086 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:54:40.046 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:54:40.087 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:56:11.046 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:56:11.088 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:57:42.047 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:57:42.087 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:59:13.047 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:59:13.088 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 10:59:25.265 +02:00] [INF] [3] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-10-04 10:59:37.265 +02:00] [INF] [3] Emby.Server.Implementations.Session.SessionWebSocketListener: Lost 1 WebSockets.
[2025-10-04 11:00:44.048 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:00:44.088 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:02:15.048 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:02:15.088 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:03:46.047 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:03:46.089 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:05:17.048 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:05:17.089 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:06:48.048 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:06:48.089 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:08:19.049 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:08:19.089 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:09:50.050 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:09:50.089 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:11:21.050 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:11:21.089 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:12:52.051 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:12:52.088 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:13:26.632 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:26.633 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:26.633 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:26.634 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:26.635 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:26.636 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:29.103 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:29.103 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:29.103 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:29.103 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:29.103 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:29.103 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:43.412 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:43.412 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:43.412 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:43.419 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:43.419 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:43.419 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:44.763 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:44.765 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:44.765 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:44.765 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:44.767 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:44.767 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:49.745 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:49.745 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:49.745 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:51.997 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:51.997 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:51.997 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:13:52.002 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:52.002 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:13:52.002 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:14:01.260 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:01.260 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:01.260 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:01.260 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:01.260 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:14:01.260 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:14:02.454 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:02.455 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:02.455 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:14:02.456 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:02.456 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:14:02.456 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:14:23.052 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:14:23.089 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:15:06.945 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:06.945 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:06.945 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:06.945 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:06.945 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:06.945 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:11.923 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:11.923 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:11.924 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:16.934 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:16.934 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:16.934 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:21.943 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:21.944 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:21.944 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:26.953 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:26.953 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:26.953 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:31.964 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:31.964 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:31.964 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:36.974 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:36.974 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:36.974 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:41.984 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:41.985 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:41.985 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:46.995 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:46.995 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:46.995 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:49.244 +02:00] [INF] [16] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-10-04 11:15:49.244 +02:00] [INF] [16] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 2/0
[2025-10-04 11:15:49.244 +02:00] [INF] [16] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 24f5447d-4585-49f7-b7b4-460038974b9d
[2025-10-04 11:15:52.005 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:52.005 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:52.005 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:15:54.052 +02:00] [INF] [30] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:15:54.090 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:15:57.015 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:57.015 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:15:57.015 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:02.032 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:02.032 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:02.032 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:06.928 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:06.928 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:06.928 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:07.042 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:07.042 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:07.042 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:12.053 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:12.053 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:12.053 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:17.064 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:17.064 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:17.064 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:22.073 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:22.073 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:22.073 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:27.083 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:27.083 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:27.084 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:32.094 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:32.094 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:32.094 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:34.265 +02:00] [INF] [24] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" closed
[2025-10-04 11:16:37.103 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:37.103 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:37.103 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:42.114 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:42.114 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:42.114 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:47.124 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:47.124 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:47.124 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:52.135 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:52.135 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:52.135 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:16:57.145 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:57.145 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:16:57.145 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:02.159 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:02.159 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:02.159 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:06.943 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:06.943 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:06.943 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:07.174 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:07.174 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:07.174 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:12.186 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:12.186 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:12.186 +02:00] [INF] [30] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:15.491 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:15.492 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:15.492 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:15.494 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:15.494 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:15.494 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:17.544 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:17.544 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:17.544 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:17.544 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:17.544 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:17.544 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:22.560 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:22.560 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:22.560 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:25.052 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:17:25.091 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:17:27.576 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:27.576 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:27.576 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:32.588 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:32.588 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:32.588 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:37.598 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:37.598 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:37.598 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:42.610 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:42.610 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:42.610 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:47.620 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:47.620 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:47.621 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:52.631 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:52.631 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:52.631 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:17:57.642 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:57.642 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:17:57.642 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:02.653 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:02.653 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:02.653 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:06.956 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:06.956 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:06.956 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:07.664 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:07.664 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:07.664 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:12.676 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:12.676 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:12.676 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:17.688 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:17.688 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:17.688 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:22.699 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:22.699 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:22.699 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:27.710 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:27.710 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:27.710 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:32.720 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:32.720 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:32.720 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:37.731 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:37.731 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:37.731 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:42.742 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:42.742 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:42.742 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:47.754 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:47.754 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:47.754 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:52.764 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:52.764 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:52.764 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:18:56.053 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:18:56.091 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:18:57.776 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:57.776 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:18:57.776 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:02.786 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:02.786 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:02.786 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:06.966 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:06.966 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:06.966 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:07.797 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:07.797 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:07.797 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:12.807 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:12.807 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:12.807 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:17.818 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:17.818 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:17.818 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:22.828 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:22.828 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:22.828 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:27.838 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:27.838 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:27.838 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:32.850 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:32.850 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:32.850 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:37.862 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:37.862 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:37.862 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:42.873 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:42.873 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:42.873 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:47.884 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:47.884 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:47.884 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:52.895 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:52.895 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:52.895 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:19:57.906 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:57.906 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:19:57.906 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:02.916 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:02.916 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:02.916 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:06.976 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:06.976 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:06.976 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:07.926 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:07.926 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:07.926 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:12.936 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:12.936 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:12.936 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:17.947 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:17.947 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:17.947 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:22.958 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:22.958 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:22.958 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:27.054 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:20:27.092 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:20:27.968 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:27.968 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:27.968 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:32.979 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:32.979 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:32.979 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:37.991 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:37.991 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:37.991 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:43.004 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:43.004 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:43.004 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:48.015 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:48.015 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:48.015 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:53.026 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:53.026 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:53.026 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:20:58.036 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:58.036 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:20:58.036 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:03.046 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:03.046 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:03.046 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:06.984 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:06.985 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:06.985 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:08.055 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:08.055 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:08.055 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:13.066 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:13.066 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:13.066 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:18.076 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:18.076 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:18.076 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:23.087 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:23.087 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:23.087 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:28.096 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:28.096 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:28.097 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:33.107 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:33.107 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:33.107 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:38.117 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:38.117 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:38.117 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:43.127 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:43.127 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:43.127 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:48.137 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:48.137 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:48.138 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:48.740 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:48.740 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:48.740 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:48.740 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:48.740 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:21:48.740 +02:00] [INF] [31] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:21:58.054 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:21:58.093 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:22:12.433 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:12.433 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:12.433 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:22:12.434 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:12.435 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:12.435 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:22:18.760 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:18.761 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:18.761 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:18.761 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:22:18.761 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:18.761 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:22:19.917 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:19.918 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:19.918 +02:00] [INF] [11] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:22:19.919 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:19.919 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-10-04 11:22:19.920 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-10-04 11:22:23.204 +02:00] [INF] [19] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "10.89.0.6" request
[2025-10-04 11:23:29.054 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-10-04 11:23:29.093 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
