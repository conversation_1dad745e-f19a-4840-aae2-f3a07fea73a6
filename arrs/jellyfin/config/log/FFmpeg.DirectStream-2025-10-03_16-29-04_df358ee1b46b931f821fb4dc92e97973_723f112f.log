{"Protocol":0,"Id":"df358ee1b46b931f821fb4dc92e97973","Path":"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":2607651779,"Name":"Dexter - S08E04 - Scar Tissue WEBDL-1080p","IsRemote":false,"ETag":"4d1f702cbe558ddef5cc04839d582871","RunTimeTicks":30152000000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":6918683,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano - Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Ingl\u00E9s - English - Dolby Digital\u002B - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Completos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Completos] - Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s [Para sordos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Ingl\u00E9s [Para sordos] - English - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":7686683,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:26:26.084 -noaccurate_seek -fflags +genpts  -i file:"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 libfdk_aac -ac 2 -ab 256000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "7a066d43e13586abed81d48b714e4055-1.mp4" -start_number 264 -hls_segment_filename "/config/cache/transcodes/7a066d43e13586abed81d48b714e4055%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/config/cache/transcodes/7a066d43e13586abed81d48b714e4055.m3u8"


ffmpeg version 7.1.2-Jellyfin Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 13 (Ubuntu 13.3.0-6ubuntu2~24.04)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, matroska,webm, from 'file:/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E04 - Scar Tissue WEBDL-1080p.mkv':
  Metadata:
    title           : Dexter S08E04 1080p NF WEB-DL DDP2.0 x264 - TSeD @danixd
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2018-08-18T08:37:21.000000Z
  Duration: 00:50:15.20, start: 0.000000, bitrate: 6918 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 6148245
        DURATION-eng    : 00:50:15.137000000
        NUMBER_OF_FRAMES-eng: 72291
        NUMBER_OF_BYTES-eng: 2317225192
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        title           : Castellano
        BPS-eng         : 128000
        DURATION-eng    : 00:50:15.200000000
        NUMBER_OF_FRAMES-eng: 94225
        NUMBER_OF_BYTES-eng: 48243200
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(eng): Audio: eac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        title           : Inglés
        BPS-eng         : 640000
        DURATION-eng    : 00:50:15.200000000
        NUMBER_OF_FRAMES-eng: 94225
        NUMBER_OF_BYTES-eng: 241216000
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(spa): Subtitle: subrip (srt)
      Metadata:
        title           : Castellano [Completos]
        BPS-eng         : 70
        DURATION-eng    : 00:47:24.050000000
        NUMBER_OF_FRAMES-eng: 755
        NUMBER_OF_BYTES-eng: 25189
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(eng): Subtitle: subrip (srt)
      Metadata:
        title           : Inglés [Para sordos]
        BPS-eng         : 72
        DURATION-eng    : 00:48:51.720000000
        NUMBER_OF_FRAMES-eng: 1425
        NUMBER_OF_BYTES-eng: 26446
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:37:21
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (eac3 (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055-1.mp4' for writing
Output #0, hls, to '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055.m3u8':
  Metadata:
    encoder         : Lavf61.7.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: aac, 48000 Hz, stereo, s16, 256 kb/s (default)
      Metadata:
        encoder         : Lavc61.19.101 libfdk_aac
      Side data:
        cpb: bitrate max/min/avg: 256000/256000/256000 buffer size: 0 vbv_delay: N/A
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055264.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055265.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055266.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055267.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055268.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055269.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055270.mp4' for writing
frame= 1015 fps=0.0 q=-1.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055271.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055272.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055273.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055274.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055275.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055276.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055277.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055278.mp4' for writing
frame= 2167 fps=2167 q=-1.0 size=N/A time=00:00:48.04 bitrate=N/A speed=  48x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055279.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055280.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055281.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055282.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055283.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055284.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055285.mp4' for writing
frame= 3229 fps=2152 q=-1.0 size=N/A time=00:01:32.09 bitrate=N/A speed=61.4x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055286.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055287.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055288.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055289.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055290.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055291.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055292.mp4' for writing
frame= 4247 fps=2123 q=-1.0 size=N/A time=00:02:14.76 bitrate=N/A speed=67.4x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055293.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055294.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055295.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055296.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055297.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055298.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055299.mp4' for writing
frame= 5335 fps=2134 q=-1.0 size=N/A time=00:03:00.05 bitrate=N/A speed=  72x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055300.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055301.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055302.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055303.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055304.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055305.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055306.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055307.mp4' for writing
frame= 6425 fps=2141 q=-1.0 size=N/A time=00:03:45.42 bitrate=N/A speed=75.1x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055308.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055309.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055310.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055311.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055312.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055313.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055314.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055315.mp4' for writing
frame= 7537 fps=2153 q=-1.0 size=N/A time=00:04:31.80 bitrate=N/A speed=77.6x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055316.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055317.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055318.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055319.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055320.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055321.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055322.mp4' for writing
frame= 8604 fps=2151 q=-1.0 size=N/A time=00:05:16.39 bitrate=N/A speed=79.1x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055323.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055324.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055325.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055326.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055327.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055328.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055329.mp4' for writing
frame= 9640 fps=2142 q=-1.0 size=N/A time=00:05:59.61 bitrate=N/A speed=79.9x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055330.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055331.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055332.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055333.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055334.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055335.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055336.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055337.mp4' for writing
frame=10722 fps=2144 q=-1.0 size=N/A time=00:06:44.84 bitrate=N/A speed=  81x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055338.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055339.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055340.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055341.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055342.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055343.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055344.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055345.mp4' for writing
frame=11827 fps=2150 q=-1.0 size=N/A time=00:07:30.81 bitrate=N/A speed=  82x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055346.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055347.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055348.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055349.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055350.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055351.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055352.mp4' for writing
frame=12925 fps=2154 q=-1.0 size=N/A time=00:08:16.74 bitrate=N/A speed=82.8x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055353.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055354.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055355.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055356.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055357.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055358.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055359.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055360.mp4' for writing
frame=14069 fps=2164 q=-1.0 size=N/A time=00:09:04.62 bitrate=N/A speed=83.8x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055361.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055362.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055363.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055364.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055365.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055366.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055367.mp4' for writing
frame=14995 fps=2142 q=-1.0 size=N/A time=00:09:43.06 bitrate=N/A speed=83.3x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055368.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055369.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055370.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055371.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055372.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055373.mp4' for writing
frame=15928 fps=2123 q=-1.0 size=N/A time=00:10:21.84 bitrate=N/A speed=82.9x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055374.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055375.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055376.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055377.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055378.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055379.mp4' for writing
frame=16828 fps=2103 q=-1.0 size=N/A time=00:10:59.45 bitrate=N/A speed=82.4x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055380.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055381.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055382.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055383.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055384.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055385.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055386.mp4' for writing
frame=17780 fps=2091 q=-1.0 size=N/A time=00:11:39.20 bitrate=N/A speed=82.2x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055387.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055388.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055389.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055390.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055391.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055392.mp4' for writing
frame=18673 fps=2074 q=-1.0 size=N/A time=00:12:16.46 bitrate=N/A speed=81.8x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055393.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055394.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055395.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055396.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055397.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055398.mp4' for writing
frame=19585 fps=2061 q=-1.0 size=N/A time=00:12:54.37 bitrate=N/A speed=81.5x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055399.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055400.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055401.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055402.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055403.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055404.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055405.mp4' for writing
frame=20564 fps=2056 q=-1.0 size=N/A time=00:13:35.44 bitrate=N/A speed=81.5x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055406.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055407.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055408.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055409.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055410.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055411.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055412.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055413.mp4' for writing
frame=21625 fps=2059 q=-1.0 size=N/A time=00:14:19.60 bitrate=N/A speed=81.9x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055414.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055415.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055416.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055417.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055418.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055419.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055420.mp4' for writing
frame=22710 fps=2064 q=-1.0 size=N/A time=00:15:04.87 bitrate=N/A speed=82.2x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055421.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055422.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055423.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055424.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055425.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055426.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055427.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055428.mp4' for writing
frame=23766 fps=2066 q=-1.0 size=N/A time=00:15:48.94 bitrate=N/A speed=82.5x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055429.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055430.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055431.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055432.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055433.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055434.mp4' for writing
frame=24671 fps=2056 q=-1.0 size=N/A time=00:16:26.45 bitrate=N/A speed=82.2x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055435.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055436.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055437.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055438.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055439.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055440.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055441.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055442.mp4' for writing
frame=25860 fps=2068 q=-1.0 size=N/A time=00:17:16.26 bitrate=N/A speed=82.9x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055443.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055444.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055445.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055446.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055447.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055448.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055449.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055450.mp4' for writing
frame=26938 fps=2072 q=-1.0 size=N/A time=00:18:01.08 bitrate=N/A speed=83.1x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055451.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055452.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055453.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055454.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055455.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055456.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055457.mp4' for writing
frame=28028 fps=2076 q=-1.0 size=N/A time=00:18:46.46 bitrate=N/A speed=83.4x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055458.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055459.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055460.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055461.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055462.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055463.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055464.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055465.mp4' for writing
frame=29202 fps=2086 q=-1.0 size=N/A time=00:19:35.48 bitrate=N/A speed=  84x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055466.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055467.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055468.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055469.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055470.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055471.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055472.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055473.mp4' for writing
frame=30308 fps=2090 q=-1.0 size=N/A time=00:20:21.76 bitrate=N/A speed=84.2x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055474.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055475.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055476.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055477.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055478.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055479.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055480.mp4' for writing
frame=31291 fps=2086 q=-1.0 size=N/A time=00:21:02.92 bitrate=N/A speed=84.2x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055481.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055482.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055483.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055484.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055485.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055486.mp4' for writing
frame=32260 fps=2081 q=-1.0 size=N/A time=00:21:43.04 bitrate=N/A speed=84.1x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055487.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055488.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055489.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055490.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055491.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055492.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055493.mp4' for writing
frame=33169 fps=2073 q=-1.0 size=N/A time=00:22:21.01 bitrate=N/A speed=83.8x    
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055494.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055495.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055496.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055497.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055498.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055499.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055500.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055501.mp4' for writing
[hls @ 0x56411233af80] Opening '/config/cache/transcodes/7a066d43e13586abed81d48b714e4055502.mp4' for writing
[out#0/hls @ 0x564112329bc0] video:1067779KiB audio:44677KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=34275 fps=2082 q=-1.0 Lsize=N/A time=00:23:07.38 bitrate=N/A speed=84.3x    