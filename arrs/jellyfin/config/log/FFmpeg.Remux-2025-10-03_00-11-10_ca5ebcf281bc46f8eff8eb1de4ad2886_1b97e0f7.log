{"Protocol":0,"Id":"ca5ebcf281bc46f8eff8eb1de4ad2886","Path":"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E01 - A Beautiful Day WEBDL-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":2599540840,"Name":"Dexter - S08E01 - A Beautiful Day WEBDL-1080p","IsRemote":false,"ETag":"dad83b22ac6eba5ea408c519e51c4eac","RunTimeTicks":31876160000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":6524100,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano - Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Ingl\u00E9s - English - Dolby Digital\u002B - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Forzados]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Forzados] - Spanish - Predeterminado - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano [Completos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Castellano [Completos] - Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Ingl\u00E9s [Para sordos]","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Ingl\u00E9s [Para sordos] - English - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":7292100,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:13:01.280 -noaccurate_seek -fflags +genpts  -i file:"/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E01 - A Beautiful Day WEBDL-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "3f67245c2c83e0b6b5b28f48d6c5c12f-1.mp4" -start_number 130 -hls_segment_filename "/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f.m3u8"


ffmpeg version 7.1.2-Jellyfin Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 13 (Ubuntu 13.3.0-6ubuntu2~24.04)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, matroska,webm, from 'file:/CONTENIDO/SERIES/Dexter/Season 8/Dexter - S08E01 - A Beautiful Day WEBDL-1080p.mkv':
  Metadata:
    title           : Dexter S08E01 1080p NF WEB-DL DDP2.0 x264 - TSeD @danixd
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2018-08-18T08:13:53.000000Z
  Duration: 00:53:07.62, start: 0.000000, bitrate: 6524 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 5754092
        DURATION-eng    : 00:53:07.309000000
        NUMBER_OF_FRAMES-eng: 76419
        NUMBER_OF_BYTES-eng: 2292508903
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        title           : Castellano
        BPS-eng         : 128000
        DURATION-eng    : 00:53:07.360000000
        NUMBER_OF_FRAMES-eng: 99605
        NUMBER_OF_BYTES-eng: 50997760
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(eng): Audio: eac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        title           : Inglés
        BPS-eng         : 640000
        DURATION-eng    : 00:53:07.616000000
        NUMBER_OF_FRAMES-eng: 99613
        NUMBER_OF_BYTES-eng: 255009280
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(spa): Subtitle: subrip (srt) (default) (forced)
      Metadata:
        title           : Castellano [Forzados]
        BPS-eng         : 321
        DURATION-eng    : 00:00:01.793000000
        NUMBER_OF_FRAMES-eng: 1
        NUMBER_OF_BYTES-eng: 72
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(spa): Subtitle: subrip (srt)
      Metadata:
        title           : Castellano [Completos]
        BPS-eng         : 70
        DURATION-eng    : 00:51:42.434000000
        NUMBER_OF_FRAMES-eng: 858
        NUMBER_OF_BYTES-eng: 27455
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:5(eng): Subtitle: subrip (srt)
      Metadata:
        title           : Inglés [Para sordos]
        BPS-eng         : 76
        DURATION-eng    : 00:51:43.393000000
        NUMBER_OF_FRAMES-eng: 1642
        NUMBER_OF_BYTES-eng: 29526
        _STATISTICS_WRITING_APP-eng: mkvmerge v25.0.0 ('Prog Noir') 32-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2018-08-18 08:13:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f-1.mp4' for writing
[mp4 @ 0x55699eedca40] track 1: codec frame size is not set
Output #0, hls, to '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f.m3u8':
  Metadata:
    encoder         : Lavf61.7.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: eac3, 48000 Hz, stereo, fltp, 128 kb/s (default)
Press [q] to stop, [?] for help
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f130.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f131.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f132.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f133.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f134.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f135.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f136.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f137.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f138.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f139.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f140.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f141.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f142.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f143.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f144.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f145.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f146.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f147.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f148.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f149.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f150.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f151.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f152.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f153.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f154.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f155.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f156.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f157.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f158.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f159.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f160.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f161.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f162.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f163.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f164.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f165.mp4' for writing
frame= 5280 fps=0.0 q=-1.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f166.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f167.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f168.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f169.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f170.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f171.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f172.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f173.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f174.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f175.mp4' for writing
frame= 6769 fps=6768 q=-1.0 size=N/A time=00:01:02.10 bitrate=N/A speed=62.1x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f176.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f177.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f178.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f179.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f180.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f181.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f182.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f183.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f184.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f185.mp4' for writing
frame= 8163 fps=5441 q=-1.0 size=N/A time=00:02:00.24 bitrate=N/A speed=80.2x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f186.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f187.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f188.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f189.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f190.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f191.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f192.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f193.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f194.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f195.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f196.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f197.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f198.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f199.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f200.mp4' for writing
frame=10306 fps=5152 q=-1.0 size=N/A time=00:03:29.62 bitrate=N/A speed= 105x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f201.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f202.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f203.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f204.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f205.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f206.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f207.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f208.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f209.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f210.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f211.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f212.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f213.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f214.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f215.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f216.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f217.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f218.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f219.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f220.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f221.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f222.mp4' for writing
frame=13393 fps=5356 q=-1.0 size=N/A time=00:05:38.37 bitrate=N/A speed= 135x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f223.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f224.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f225.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f226.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f227.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f228.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f229.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f230.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f231.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f232.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f233.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f234.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f235.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f236.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f237.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f238.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f239.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f240.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f241.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f242.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f243.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f244.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f245.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f246.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f247.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f248.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f249.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f250.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f251.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f252.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f253.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f254.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f255.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f256.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f257.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f258.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f259.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f260.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f261.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f262.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f263.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f264.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f265.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f266.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f267.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f268.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f269.mp4' for writing
frame=20161 fps=6719 q=-1.0 size=N/A time=00:10:20.66 bitrate=N/A speed= 207x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f270.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f271.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f272.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f273.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f274.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f275.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f276.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f277.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f278.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f279.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f280.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f281.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f282.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f283.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f284.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f285.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f286.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f287.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f288.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f289.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f290.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f291.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f292.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f293.mp4' for writing
frame=23745 fps=6783 q=-1.0 size=N/A time=00:12:50.14 bitrate=N/A speed= 220x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f294.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f295.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f296.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f297.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f298.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f299.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f300.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f301.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f302.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f303.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f304.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f305.mp4' for writing
frame=25345 fps=6335 q=-1.0 size=N/A time=00:13:56.87 bitrate=N/A speed= 209x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f306.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f307.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f308.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f309.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f310.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f311.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f312.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f313.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f314.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f315.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f316.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f317.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f318.mp4' for writing
frame=27322 fps=6071 q=-1.0 size=N/A time=00:15:19.33 bitrate=N/A speed= 204x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f319.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f320.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f321.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f322.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f323.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f324.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f325.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f326.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f327.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f328.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f329.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f330.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f331.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f332.mp4' for writing
frame=29262 fps=5852 q=-1.0 size=N/A time=00:16:40.24 bitrate=N/A speed= 200x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f333.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f334.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f335.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f336.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f337.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f338.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f339.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f340.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f341.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f342.mp4' for writing
frame=30691 fps=5579 q=-1.0 size=N/A time=00:17:39.85 bitrate=N/A speed= 193x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f343.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f344.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f345.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f346.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f347.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f348.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f349.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f350.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f351.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f352.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f353.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f354.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f355.mp4' for writing
frame=32629 fps=5437 q=-1.0 size=N/A time=00:19:00.68 bitrate=N/A speed= 190x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f356.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f357.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f358.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f359.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f360.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f361.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f362.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f363.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f364.mp4' for writing
frame=33985 fps=5228 q=-1.0 size=N/A time=00:19:57.23 bitrate=N/A speed= 184x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f365.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f366.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f367.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f368.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f369.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f370.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f371.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f372.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f373.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f374.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f375.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f376.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f377.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f378.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f379.mp4' for writing
frame=36109 fps=5158 q=-1.0 size=N/A time=00:21:25.82 bitrate=N/A speed= 184x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f380.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f381.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f382.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f383.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f384.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f385.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f386.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f387.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f388.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f389.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f390.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f391.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f392.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f393.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f394.mp4' for writing
frame=38199 fps=5092 q=-1.0 size=N/A time=00:22:53.16 bitrate=N/A speed= 183x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f395.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f396.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f397.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f398.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f399.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f400.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f401.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f402.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f403.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f404.mp4' for writing
frame=39601 fps=4949 q=-1.0 size=N/A time=00:23:51.47 bitrate=N/A speed= 179x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f405.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f406.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f407.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f408.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f409.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f410.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f411.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f412.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f413.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f414.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f415.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f416.mp4' for writing
frame=41448 fps=4875 q=-1.0 size=N/A time=00:25:08.50 bitrate=N/A speed= 177x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f417.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f418.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f419.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f420.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f421.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f422.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f423.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f424.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f425.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f426.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f427.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f428.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f429.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f430.mp4' for writing
frame=43345 fps=4815 q=-1.0 size=N/A time=00:26:27.62 bitrate=N/A speed= 176x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f431.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f432.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f433.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f434.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f435.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f436.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f437.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f438.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f439.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f440.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f441.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f442.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f443.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f444.mp4' for writing
frame=45452 fps=4784 q=-1.0 size=N/A time=00:27:55.50 bitrate=N/A speed= 176x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f445.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f446.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f447.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f448.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f449.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f450.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f451.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f452.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f453.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f454.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f455.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f456.mp4' for writing
frame=47232 fps=4722 q=-1.0 size=N/A time=00:29:09.74 bitrate=N/A speed= 175x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f457.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f458.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f459.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f460.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f461.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f462.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f463.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f464.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f465.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f466.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f467.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f468.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f469.mp4' for writing
frame=48913 fps=4658 q=-1.0 size=N/A time=00:30:19.85 bitrate=N/A speed= 173x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f470.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f471.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f472.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f473.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f474.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f475.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f476.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f477.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f478.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f479.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f480.mp4' for writing
frame=50605 fps=4600 q=-1.0 size=N/A time=00:31:30.43 bitrate=N/A speed= 172x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f481.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f482.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f483.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f484.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f485.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f486.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f487.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f488.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f489.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f490.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f491.mp4' for writing
frame=52224 fps=4540 q=-1.0 size=N/A time=00:32:37.95 bitrate=N/A speed= 170x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f492.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f493.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f494.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f495.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f496.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f497.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f498.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f499.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f500.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f501.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f502.mp4' for writing
frame=53760 fps=4479 q=-1.0 size=N/A time=00:33:42.02 bitrate=N/A speed= 168x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f503.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f504.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f505.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f506.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f507.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f508.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f509.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f510.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f511.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f512.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f513.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f514.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f515.mp4' for writing
frame=55627 fps=4449 q=-1.0 size=N/A time=00:34:59.88 bitrate=N/A speed= 168x    
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f516.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f517.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f518.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f519.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f520.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f521.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f522.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f523.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f524.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f525.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f526.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f527.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f528.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f529.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f530.mp4' for writing
[hls @ 0x55699eed5500] Opening '/config/cache/transcodes/3f67245c2c83e0b6b5b28f48d6c5c12f531.mp4' for writing
[out#0/hls @ 0x55699ef0bdc0] video:1686324KiB audio:37602KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=57699 fps=4479 q=-1.0 Lsize=N/A time=00:36:26.30 bitrate=N/A speed= 170x    