{"category": "Other", "changelog": "- 10.9 (#38) @crobibero\n- Add org workflows (#29) @crobibero\n- Added code to order detected videos by name. (#16) @BrianCArnold\n\n### New features and improvements ###\n- added ability to only show intros for movies (#32) @Terence-D\n- Added code to handle repeating date ranges in backend.  (#15) @BrianCArnold\n\n### Documentation updates ###\n- Update config Discord link (#31) @anthonylavado", "description": "Select a flashy pre-roll from local storage to run before any video content. The video details are loaded from disk after a video is played from the library from the beginning of the video. A random video is selected, and all videos in the local directory are enabled by default. Specific videos can be enabled or disabled as necessary. After videos are loaded for the first time, additional videos found in the directory are not enabled by default.\n", "guid": "07d86795-01f2-4d22-b174-cdc6056c3e7c", "name": "Local Intros", "overview": "Display flashy intros before movies and other videos", "owner": "<PERSON><PERSON><PERSON>", "targetAbi": "********", "timestamp": "2024-05-11T19:59:28.0000000Z", "version": "*******", "status": "Active", "autoUpdate": true, "imagePath": "", "assemblies": []}