{"category": "General", "changelog": "### Bug Fixes ###\n- Implement a database upgrade system, properly migrate ItemId to Guid (#107) @Insprill", "description": "Companion plugin that provides dynamic stream files and shorter sync times while using Jellyfin for Kodi.", "guid": "771e19d6-5385-4caf-b35c-28a0e865cf63", "name": "<PERSON><PERSON>", "overview": "Sync all media changes with Kodi clients", "owner": "jellyfin", "targetAbi": "*********", "timestamp": "2025-02-05T20:18:00.0000000Z", "version": "********", "status": "Active", "autoUpdate": true, "imagePath": "/config/data/plugins/Kodi Sync Queue_********/jellyfin-plugin-kodisyncqueue.png", "assemblies": []}